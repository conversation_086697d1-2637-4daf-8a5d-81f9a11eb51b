import { z } from 'zod'
import { zodToTs } from 'zod-to-ts'
import * as ts from 'typescript'

import { writeFileSync } from 'fs'

const DefalutParamsSchema = z.object({
  compatible: z.boolean().optional(),
  store_id: z.string().optional(),
  merchant_id: z.string().optional(),
  merchantId: z.string().optional(),
  storeId: z.string().optional(),
});

const RecordSchema = z.object({
  id: z.string(),
  item_id: z.string(),
  spec_id: z.string(),
  name: z.string(),
  attached_info: z.string(),
  status: z.number(),
  price: z.number(),
  num: z.number(),
  ctime: z.number(),
  mtime: z.number(),
  category_id: z.string(),
  url: z.string(),
  sku: z.string().nullable(),
  out_of_stock: z.boolean(),
  user_icons: z.array(z.string()),
  last_deal_type: z.string(),
  last_add_num: z.number(),
  user_id: z.string(),
  user_name: z.string(),
  user_icon: z.string(),
  last_add: z.boolean(),
  open_table_must_order: z.boolean(),
  open_table_must_order_editable: z.boolean(),
  spu_type: z.string(),
  min_sale_num: z.number().nullable(),
  display_order: z.number(),
  category_sort: z.number(),
  materials: z.array(z.unknown()),
  brand_acd_product_id: z.union([z.string(),z.number()]).nullable(),
  recommend_materials: z.array(z.unknown()),
  attributes: z.unknown().nullable(),
  client_version: z.unknown().nullable(),
  brand_act: z.boolean(),
});

const priceBreakCardSchema = z.object({
  discountType: z.number(),
  fills: z.number(),
  minus: z.number(),
  activitySn: z.string(),
  isEnable: z.boolean()
});
const discountsSchema = z.object({
  singleActivity: z.number(),
  secondDiscounts: z.array(z.unknown()), // Refine based on exact type
  globalDiscount: z.unknown().nullable(), // Refine based on exact type
  collectionDiscount: z.unknown().nullable(), // Refine based on exact type
  timeDiscount: z.array(z.unknown()), // Refine based on exact type
  priceBreak: z.unknown().nullable(), // Refine based on exact type
  priceBreaks: z.array(z.unknown()), // Refine based on exact type
  priceBreakCards: z.array(priceBreakCardSchema),
  returnCards: z.array(z.unknown()), // Refine based on exact type
  membershipCards: z.array(z.unknown()) // Refine based on exact type
});

const deliveryTimesV2Schema = z.record(z.array(
  z.object({
    startTime: z.number(),
    endTime: z.number()
  })
));
const campusSchema = z.object({
  deliveryType: z.number(),
  timeoutPay: z.number(),
  weight: z.number(),
  campusId: z.number()
});
const storeSetSchema = z.object({
  score: z.unknown().nullable(),
  id: z.union([z.string(),z.number()]).nullable(),
  categoriesNames: z.unknown().nullable(),
  coupons: z.array(z.unknown()), // Adjust type if needed
  merchantId: z.string(),
  scanStatus: z.number(),
  wechatActivityMessages: z.array(z.unknown()), // Adjust type if needed
  alipayActivityMessages: z.array(z.unknown()), // Adjust type if needed
  campusDelivery: z.unknown().nullable(),
  timeoutPay: z.number(),
  contactPhone: z.string(),
  streetAddress: z.string(),
  houseNumber: z.string(),
  deliveryTimesV2: deliveryTimesV2Schema,
  discount: z.unknown().nullable(),
  storeSn: z.string(),
  storeExtraName: z.string(),
  alipayStatus: z.number(),
  wechatStatus: z.number(),
  collectionNums: z.unknown().nullable(),
  logoUrl: z.string(),
  coverPhoto: z.string(),
  industry: z.string(),
  distance: z.number(),
  industryCode: z.string(),
  merchantName: z.string(),
  businessHoursStart: z.string(),
  businessHoursEnd: z.string(),
  slogan: z.string(),
  aoiName: z.string(),
  appId: z.string(),
  campuses: z.array(campusSchema),
  storeName: z.string(),
  items: z.unknown().nullable(),
  merchantSn: z.string(),
  location: z.string(),
  takeoutStatus: z.number(),
  discounts: discountsSchema,
  address: z.string(),
  storeId: z.string()
});

const specOptionSchema = z.object({
  id: z.string(),
  name: z.string(),
  price: z.number(),
  seq: z.number(),
  activity_price: z.unknown().nullable(),
  quota_count: z.unknown().nullable(),
  second_activity_discount: z.unknown().nullable(),
  discount_prices: z.unknown().nullable(),
  discount_texts: z.unknown().nullable(),
  arrival_price: z.number(),
  takeout_price: z.number()
});

const attributeSchema = z.object({
  name: z.string(),
  title: z.string().nullable(),
  seq: z.unknown().nullable(),
  id: z.union([z.string(),z.number()]).nullable(),
});

const ChargeParamsSchema = z.object({
  chargeType: z.number(),
  freeOrderAmount: z.number(),
  merchantMpActivity: z.boolean(),
  ufoodActivity: z.boolean(),
  freeOrderCount: z.number(),
  diningHallActivity: z.boolean(),
  ufoodFreeFeeActivity: z.boolean(),
});

const extraInfoSchema = z.object({
  orderSource: z.string(),
  settleTime: z.number(),
  orderInCampus: z.boolean(),
  deliveryErrorCode: z.string(),
  acceptTime: z.number(),
  deliveryFeeDetail: z.object({}).optional(),
  weixinAppId: z.string(),
  brandActivity: z.boolean(),
  sqbScene: z.string(),
  membership: z.boolean(),
  upayProfitSharing: z.object({
    charge: z.array(
      z.object({
        charge_flag: z.string(),
        charge_params: ChargeParamsSchema,
      }),
    ),
    sharing_flag: z.string(),
    sharing_notify_url: z.string(),
  }),
  isDeliveryConvert: z.boolean(),
  sqbBizModel: z.array(z.string()),
  areaId: z.string(),
  channelUserId: z.string(),
  preReduceNo: z.string(),
  deliveryFloor: z.string(),
  deliveryErrorMsg: z.string(),
  tradeAppId: z.string(),
  amountComposition: z.object({
    compositionItems: z.array(
      z.object({
        amount: z.string(),
        category: z.string(),
      }),
    ),
  }),
});
// Define the schema for items array inside the main object
const ItemArraySchema = z.array(
  z.object({
    ctime: z.union([z.string(),z.number()]).nullable(),
    merchant_id: z.union([z.string(),z.number()]).nullable(),
    store_id: z.union([z.string(),z.number()]).nullable(),
    spec: z.object({
      name: z.string(),
      price: z.number(),
      id: z.string(),
    }),
    order_time: z.unknown().nullable(),
    item_uid: z.string(),
    extra_info: z.object({
      unit_type: z.string(),
      local_goods_id: z.string(),
      category_sort: z.number(),
      sale_weight: z.number(),
      item_sort: z.number(),
      origin_goods_price: z.number(),
    }),
    item: z.object({
      category_id: z.string(),
      url: z.string(),
      name: z.string(),
      total_amount: z.number(),
      sku: z.unknown().nullable(),
      attached_info: z.string(),
      attached_info_without_materials: z.string(),
      discount_number: z.number(),
      display_order: z.unknown().nullable(),
      photo_url: z.string(),
      spu_type: z.string(),
      category_sort: z.unknown().nullable(),
      min_sale_num: z.union([z.string(),z.number()]).nullable(),
      item_tag: z.number(),
      unit: z.string(),
      status: z.number(),
      discount_price: z.number(),
      number: z.number(),
      out_of_stock: z.unknown().nullable(),
      is_multiple: z.union([z.boolean(),z.number()]).nullable(),
      unit_type: z.number(),
      id: z.string(),
      weight: z.union([z.string(),z.number()]).nullable(),
      return_number: z.number(),
      price: z.number(),
      must: z.unknown().nullable(),
      number_decimal: z.number(),
    }),
    service_type: z.number(),
    user_icon: z.string().nullable(),
    package_group_name: z.string().nullable(),
    quota_count: z.unknown().nullable(),
    pack_fee: z.union([z.string(),z.number()]).nullable(),
    order_sn: z.union([z.string(),z.number()]).nullable(),
    gift_food: z.boolean(),
    table_id: z.union([z.string(),z.number()]).nullable(),
    tag_name: z.string().nullable(),
    manual_change_price: z.boolean(),
    brand_act_product: z.unknown().nullable(),
    sub_payway: z.unknown().nullable(),
    payway: z.unknown().nullable(),
    category_id: z.union([z.string(),z.number()]).nullable(),
    cover: z.boolean(),
    client_version: z.number(),
    materials: z.unknown().nullable(),
    brand_act: z.boolean(),
    discount_strategy: z.unknown().nullable(),
    meal_type: z.string(),
    mk_custom_info: z.unknown().nullable(),
    activity_name: z.string().nullable(),
    compatible: z.unknown().nullable(),
    from_cart: z.boolean(),
    user_name: z.string().nullable(),
    package_group_id: z.unknown().nullable(),
    package_items: z.unknown().nullable(),
    open_table_must_order: z.boolean(),
    open_table_item_editable: z.boolean(),
    people_num: z.union([z.string(),z.number()]).nullable(),
    terminal_sn: z.union([z.string(),z.number()]).nullable(),
    recommend_materials: z.unknown().nullable(),
    id: z.union([z.string(),z.number()]).nullable(),
    attributes: z.array(attributeSchema),
    process_status: z.string(),
    must: z.unknown().nullable(),
    number: z.number(),
  })
);

// Define the schema for invoice
const invoiceSchema = z.object({
  url: z.string().nullable(),
  text: z.string().nullable(),
  show: z.boolean(),
});

// Define the schema for order_campus_station
const orderCampusStationSchema = z.object({
  ctime: z.union([z.string(),z.number()]).nullable(),
  cellphone: z.union([z.string(),z.number()]).nullable(),
  user_id: z.union([z.string(),z.number()]).nullable(),
  name: z.string().nullable(),
  order_sn: z.string(),
  mtime: z.union([z.string(),z.number()]).nullable(),
  address: z.unknown().nullable(),
  gender: z.union([z.string(),z.number()]).nullable(),
  version: z.union([z.string(),z.number()]).nullable(),
  latitude: z.union([z.string(),z.number()]).nullable(),
  station_id: z.union([z.string(),z.number()]).nullable(),
  longitude: z.union([z.string(),z.number()]).nullable(),
  campus_id: z.number(),
  id: z.union([z.string(),z.number()]).nullable(),
});


const goodItemSchema = z.object({
  id: z.string(),
  order_sn: z.string(),
  category_id: z.string(),
  spu_id: z.string(),
  spu_title: z.string(),
  spu_type: z.string(),
  main_image_url: z.string().nullable(),
  sku_id: z.string().nullable(),
  sku_title: z.string().nullable(),
  sku_type: z.string(),
  activity_id: z.string().nullable(),
  activity_type: z.string().nullable(),
  discount_amount: z.number(),
  merchant_discount_share_amount: z.number().nullable(),
  refund_amount: z.number(),
  ref_pay_type: z.string(),
  has_processed_stock: z.string(),
  process_status: z.string(),
  batch_no: z.string(),
  attached_info: z.unknown().nullable(),
  attribute_infos: z.unknown().nullable(),
  materials: z.unknown().nullable(),
  total_amount: z.number().nullable(),
  discount_count: z.number().nullable(),
  package_goods: z.unknown().nullable(),
  count: z.number(),
  origin_sale_price: z.number(),
  now_discount_price: z.number(),
  goods_discount_type: z.unknown().nullable(),
  refund_count: z.number().nullable(),
  ref_goods_id: z.string().nullable(),
  ref_client_sn: z.string().nullable(),
  sale_unit: z.string(),
  unit_type: z.string(),
  extra_info: z.object({
    local_goods_id: z.string(),
    user_name: z.string(),
    item_sort: z.number(),
    order_time_stamp: z.string(), // Assuming this is a string representing a timestamp
    user_icon: z.string(),
    category_sort: z.number(),
    unit_type: z.string(),
    origin_goods_price: z.number(),
    sale_weight: z.number(),
  }),
  item_snapshot: z.unknown().nullable(),
  version: z.string().nullable(),
  goods_tag: z.number(),
  ctime: z.number(),
  mtime: z.number(),
  spec: z.unknown().nullable(),
  goods_redeem_result_v2: z.unknown().nullable(),
});
const OrderMainSchema = z.object({
  sn: z.string(),
  order_type: z.string(),
  status: z.string(),
  original_amount: z.number(),
  effective_amount: z.number(),
  need_pay_amount: z.number(),
  un_discountable_amount: z.number(),
  merchant_discount_total_amount: z.number(),
  coupon_discount_amount: z.number(),
  bonus_point_amount: z.number(),
  activity_discount_amount: z.number(),
  temp_discount_amount: z.number(),
  vip_discount_amount: z.number(),
  eliminate_amount: z.number(),
  product_discount_amount: z.number(),
  channel_assist_discount: z.number(),
  buyer_pay_amount: z.number(),
  receive_amount: z.number(),
  user_id: z.string(),
  user_name: z.string().nullable(),
  avatar_url: z.string().nullable(),
  refund_amount: z.number(),
  goods_count: z.number(),
  table_id: z.string(),
  table_no: z.string(),
  order_tag: z.number(),
  payment_channels: z.string().nullable(),
  pay_time: z.number(),
  pack_amount: z.string().nullable(),
  order_seq: z.string(),
  subject: z.string().nullable(),
  items_info: z.array(z.object({
    spu_title: z.string(),
    sku_title: z.string().nullable(),
    price: z.number(),
    sale_count: z.number(),
    id: z.string(),
    name: z.string(),
    value: z.any().nullable(),  // Assuming value can be any type or null
    number: z.number(),
  })),
  remark: z.string().nullable(),
  merchant_id: z.string(),
  merchant_name: z.string().nullable(),
  merchant_sn: z.string(),
  store_id: z.string(),
  store_sn: z.string(),
  store_name: z.string(),
  cashier_id: z.string().nullable(),
  cashier_name: z.string().nullable(),
  packed: z.string(), // Assuming this is a string "Y" or "N"
  deleted: z.string(), // Assuming this is a string "Y" or "N"
  terminal_id: z.string().nullable(),
  terminal_sn: z.string().nullable(),
  terminal_name: z.string().nullable(),
  terminal_type: z.string().nullable(),
  deliver_type: z.string().nullable(),
  deliver_amount: z.string().nullable(),
  profit_sharing_amount: z.string().nullable(),
  order_source: z.string(),
  ctime: z.number(),
  mtime: z.number(),
  version: z.number(),
  extra: z.object({
    people_num: z.union([z.string(),z.number()]).nullable(),
    areaId: z.string(),
    user_name: z.string(),
    open_table_time: z.string(),
    initialAppId: z.string(),
    user_icon: z.string(),
    initialChannelUserId: z.string(),
    acceptTime: z.number(),
  }),
  is_final: z.string().nullable(),
  id: z.string(),
  campus_order: z.string().nullable(), // Assuming this is nullable and string type
  campus_id: z.string().nullable(),
  campus_delivery_fee: z.string().nullable(),
  goods_list: z.array(goodItemSchema), // You can refine this with actual goods schema if known
  item_snapshots: z.unknown().nullable(), // Assuming this can be nullable and any structure
  pays_list: z.unknown().nullable(), // Assuming this can be nullable and any structure
  redeems: z.unknown().nullable(), // Assuming this can be nullable and any structure
  delivery_info: z.unknown().nullable(), // Assuming this can be nullable and any structure
  order_address: z.unknown().nullable(), // Assuming this can be nullable and any structure
  book_order_info_dto: z.unknown().nullable(), // Assuming this can be nullable and any structure
  ever_paid: z.boolean(),
  paid_or_with_redeem: z.boolean(),
});

const refundGoodSchema = z.object({
  main_image: z.any().nullable(), // Assuming main_image can be any type or null
  refund_count: z.number(),
  discount_goods_type: z.string(),
  name: z.any().nullable(), // Assuming name can be any type or null
  refund_amount: z.number(),
  db_goods_id: z.string(),
  id: z.string(),
  attach_info: z.any().nullable() // Assuming attach_info can be any type or null
});
const refundPaySchema = z.object({
  ref_pay_order_no: z.string().nullable(),
  ref_vip_order_no: z.string().nullable(),
  ref_platform_order_no: z.string().nullable(),
  origin_pays: z.any().nullable(), // Assuming origin_pays can be any type or null
  tag: z.any().nullable(), // Assuming tag can be any type or null
  refund_amount: z.number(),
  id: z.number(),
  local_order_no: z.string().nullable(),
  payment_channel: z.string(),
  payment_channel_name: z.string(),
  ref_local_order_no: z.string().nullable()
});

const cartsDurationSchema = z.object({
  start: z.number(),
  cost: z.number(),
  end: z.number()
});
const durationSchema = z.object({
  carts: cartsDurationSchema,
  discounts: z.object({
    start: z.number()
  }),
  tradeApp: z.object({
    start: z.number(),
    cost: z.number(),
    end: z.number()
  }),
  brandActivity: z.object({
    start: z.number(),
    cost: z.number(),
    end: z.number()
  })
});

const redeemDetailSchema = z.object({
  totalDiscount: z.number(),
  merchantRedeemDetail: z.object({
      activityId: z.string().nullable(),
      activityIds: z.array(z.string()).nullable(),
      discountAmount: z.number(),
      activitySn: z.string().nullable(),
      name: z.string().nullable(),
      message: z.string(),
      subType: z.string().nullable(),
      type: z.string().nullable(),
      originalType: z.string().nullable(),
  }),
  systemRedeemDetail: z.object({
      activityId: z.string().nullable(),
      activityIds: z.array(z.string()).nullable(),
      discountAmount: z.number(),
      activitySn: z.string().nullable(),
      name: z.string().nullable(),
      message: z.string(),
      subType: z.string().nullable(),
      type: z.string().nullable(),
      originalType: z.string().nullable(),
  }),
});
const orderSchema = z.object({
  storeName: z.string(),
  storeId: z.string(),
  merchantId: z.string(),
  clientSn: z.string().nullable(),
  payway: z.number(),
  subPayway: z.number(),
  orderType: z.number(),
  merchantActivitySn: z.string(),
  sn: z.string(),
  subscribeOrderSn: z.string(),
  userId: z.string(),
  channelUserId: z.string(),
  weixinAppId: z.string(),
  totalDiscount: z.number(),
  originalAmount: z.number(),
  netAmount: z.number(),
  ctime: z.number(),
  status: z.string(),
  redeemResult: redeemDetailSchema,
  goodsDetail: z.unknown().nullable(),
  userName: z.string(),
  avatarUrl: z.string().nullable(),
  mealPreparationStatus: z.unknown().nullable(),
});

//###############################################################################################
const RoundAddGoodRespSchema = z.object({
  sn: z.string().nullable(),
  people_num: z.union([z.string(),z.number()]).nullable(),
  version: z.number(),
  total: z.number(),
  total_price: z.number(),
  records: z.array(RecordSchema),
  last_deal_record: RecordSchema,
  spu_count_map: z.record(z.number()),
});

const DepositRuleSchema = z.object({
  depositAmountLevel: z.number(),
  discountAmountLevel: z.number(),
  discountCount: z.number()
})

const StoredCardActivitySchema = z.object({
  id: z.string(),
  sn: z.string(),
  status: z.number(),
  type: z.number(),
  storeId: z.string(),
  depositRules: z.array(DepositRuleSchema),
  storeIds: z.array(z.string())
})

const DeliveryActivitySchema = z.object({
  storeId: z.string(),
  discount: z.number(),
  activityId: z.string(),
  activitySn: z.string(),
  isSub: z.boolean(),
  mode: z.string(),
  goodsCouponFlag: z.boolean()
})

const SecondActivitySchema = z.object({
  discount: z.number(),
  activityId: z.string(),
  startDate: z.number(),
  endDate: z.number(),
  isSub: z.boolean(),
  ctime: z.number(),
  mode: z.string(),
  goodsCouponFlag: z.boolean(),
  activityName: z.string(),
  dateOption: z.number(),
  designateDate: z.array(z.unknown()),
  designateTime: z.array(z.unknown()),
  useCase: z.array(z.number())
})

const SingleActivitySchema = z.object({
  activityId: z.string(),
  startDate: z.number(),
  endDate: z.number(),
  isSub: z.boolean(),
  ctime: z.number(),
  mode: z.string(),
  goodsCouponFlag: z.boolean(),
  quotaOption: z.number(),
  activityName: z.string(),
  dateOption: z.number(),
  designateDate: z.array(z.unknown()),
  designateTime: z.array(z.unknown()),
  useCase: z.array(z.number())
})

const SaleTimeSchema = z.object({
  type: z.number(),
  start_date: z.number().nullable(),
  end_date: z.number().nullable(),
  cycle: z.array(z.number()),
  times: z.array(
    z.object({
      start_time: z.string(),
      end_time: z.string()
    })
  )
})

const GoodsItemSchema = z.object({
  uuid: z.string(),
  id: z.string(),
  category_id: z.string(),
  name: z.string(),
  sale_times: z
    .array(
      z.object({
        start_time: z.string(),
        end_time: z.string(),
        intime_section: z.boolean()
      })
    )
    .nullable(),
  has_material: z.boolean(),
  need_choose_spec: z.boolean(),
  price: z.number(),
  activity_price: z.number().nullable(),
  gift_card_discount_price_text: z.string(),
  description: z.string().nullable(),
  photo_url: z.string().nullable(),
  unit: z.string(),
  unit_type: z.number(),
  sku: z.number().nullable(),
  out_of_stock: z.boolean(),
  sale_count: z.number(),
  hot_sale_seq: z.number().nullable(),
  min_sale_num: z.number().nullable(),
  item_tag: z.number().nullable(),
  item_tags: z.array(z.unknown()).nullable(),
  discount_texts: z
    .array(
      z.object({
        discount_text: z.string(),
        quota_count: z.number().nullable()
      })
    )
    .nullable(),
  discount_text: z.string().nullable(),
  quota_count: z.number().nullable(),
  discount_type: z.number().nullable(),
  sale_time: SaleTimeSchema,
  multiple: z.boolean(),
  package: z.boolean(),
  hot_sale: z.boolean()
})

const StoreSchema = z.object({
  collectionNums: z.union([z.string(),z.number()]).nullable(),
  firstIndustry: z.string(),
  firstIndustryCode: z.string(),
  industry: z.string(),
  industryCode: z.string(),
  merchantName: z.string(),
  merchantId: z.string(),
  merchantSn: z.string(),
  alipayStatus: z.number(),
  wechatStatus: z.number(),
  disabled: z.number(),
  discounts: discountsSchema,
  discount: z.unknown().nullable(),
  organizationPath: z.unknown().nullable(),
  categoriesNames: z.string().nullable(),
  coupons: z.array(z.unknown()),
  storeId: z.string(),
  storeName: z.string(),
  storeExtraName: z.string(),
  contactPhone: z.string(),
  slogan: z.string(),
  payWay: z.number(),
  categoryId: z.union([z.string(),z.number()]).nullable(),
  location: z.object({
    lat: z.string(),
    lon: z.string()
  }),
  storeAddress: z.object({
    province: z.string(),
    city: z.string(),
    district: z.string(),
    streetAddress: z.string(),
    address: z.string(),
    aoiName: z.string().nullable(),
    houseNumber: z.string(),
    adcode: z.union([z.string(),z.number()]).nullable()
  }),
  storePhoto: z.object({
    innerPhotos: z.array(z.unknown()),
    outerPhotos: z.array(z.unknown()),
    goodsPhotos: z.array(z.unknown()),
    coverPhoto: z.string().nullable(),
    logoUrl: z.string().nullable()
  }),
  storeBasic: z.object({
    averageConsumption: z.unknown().nullable(),
    storeArea: z.unknown().nullable(),
    businessHoursStart: z.string(),
    businessHoursEnd: z.string()
  }),
  onceOpened: z.boolean(),
  storeType: z.number(),
  storeSn: z.string()
})

const ActivitySchema = z.object({
  globalDiscount: z.unknown().nullable(),
  collectionDiscount: z.unknown().nullable(),
  timeDiscount: z.array(z.unknown()),
  priceBreak: z.unknown().nullable(),
  cardDiscounts: z.array(z.unknown()),
  returnCardDiscount: z.unknown().nullable(),
  goodsCoupons: z.array(z.unknown()),
  membershipCard: z.array(z.unknown()),
  combinedActivities: z.array(z.unknown()),
  storedCardActivity: StoredCardActivitySchema,
  membershipActivities: z.array(z.unknown()),
  goodsCouponActivities: z.unknown().nullable(),
  membershipDays: z.union([z.string(),z.number()]).nullable(),
  deliveryActivities: z.array(DeliveryActivitySchema),
  secondActivities: z.array(SecondActivitySchema),
  singleActivities: z.array(SingleActivitySchema),
  memberActivities: z.object({
    globalDiscount: z.unknown().nullable(),
    goodsDiscount: z.unknown().nullable()
  }),
  goodsV2Coupons: z.array(z.unknown()),
  goodsCouponNum: z.number()
})
const ActivityRespSchema = z.object({
  page: z.number(),
  pageSize: z.number(),
  totalRow: z.number(),
  totalPage: z.number(),
  result: z.array(z.unknown()),
});

const GatherIndexRespSchema = z.object({
  activity: ActivitySchema,
  simplifyData: z.boolean(),
  supportCardPay: z.string(),
  goods: z.object({
    total: z.number(),
    pages: z.array(z.array(GoodsItemSchema))
  }),
  areas: z.object({
    enabled: z.boolean(),
    type: z.union([z.string(),z.number()]).nullable(),
    areas: z.unknown().nullable()
  }),
  store: StoreSchema,
  mcc: z.record(z.unknown())
})

const GatherIndexParams = z.object({
  token: z.string(),
  loginUserId: z.string(),
  openid: z.string(),
  unionid: z.string(),
  storeId: z.string(),
  hideServiceType: z.boolean(),
  serviceTypeName: z.string(),
  query: z.object({
    appid: z.string()
  }),
  buyAgainOrderType: z.string(),
  compatible: z.boolean(),
  apiVersion: z.number()
});

const PayloadSchema = z.object({
  agreementCode: z.string(),
  usableType: z.string(),
  usableValue: z.string()
});

const FetchStoredBillRecordSchema = z.object({
  orderSn: z.string(),
  storeId: z.string()
});

const AddressInfoSchema = z.object({
  address: z.string(),
  addressCode: z.string().optional(),
  addressId: z.number(),
  address_name: z.string().optional(),
  appVersion: z.number(),
  areaId: z.number(),
  areaName: z.string().nullable(),
  buildingNumber: z.string().optional(),
  campusId: z.number(),
  campusName: z.string().nullable(),
  cellphone: z.string(),
  city: z.string(),
  cityCode: z.string(),
  deliveryFee: z.union([z.string(),z.number()]).nullable(), // This could be refined based on the exact type
  distance: z.number(),
  district: z.string(),
  districtCode: z.string(),
  floor: z.string().optional(),
  gender: z.number(),
  houseNumber: z.string(),
  id: z.string(),
  invalidReason: z.string().nullable(), // This could be refined based on the exact type
  isDefault: z.boolean(),
  latitude: z.string(),
  longitude: z.string(),
  preChooseFloor: z.union([z.string(),z.number()]).nullable(), // This could be refined based on the exact type
  presetTime: z.number(),
  province: z.string(),
  provinceCode: z.string(),
  type: z.number(),
  userId: z.string(),
  userName: z.string(),
  valid: z.boolean()
});

// const GetDefaultAddressSchema = DefalutParamsSchema;
// const GetDefaultAddressRespSchema = AddressInfoSchema;

const GetDefaultAddressV2Schema = z.object({
  storeId: z.string(),
  presetTime: z.number()
});
const GetDefaultAddressV2RespSchema = AddressInfoSchema;

const AddAddressSchema = z.object({
  id: z.string().optional(),
  userName: z.string(),
  gender: z.number(),
  cellphone: z.string(),
  latitude: z.string(),
  longitude: z.string(),
  address: z.string(),
  campusId: z.number().optional(),
  buildingNumber: z.string().optional(),
  type: z.number().optional(),
  houseNumber: z.string(),
  userId: z.string(),
  addressCode: z.string().optional(),
  addressName: z.string(),
  areaId: z.number(),
  addressId: z.number()
});
const AddAddressRespSchema = z.boolean();

const DelAddressSchema = z.object({
  id: z.string()
});
const DelAddressRespSchema = z.boolean();

const GetAllAddressV2Schema = z.object({
  appVersion: z.number(),
}).merge(DefalutParamsSchema);
const GetAllAddressV2RespSchema = z.object({
  currentPage: z.number(),
  pageSize: z.number(),
  totalCoun: z.number(),
  value: z.array(AddressInfoSchema)
});

const FetchDeliverFeeSchema = AddressInfoSchema;
const FetchDeliverFeeRespSchema = z.object({
  delivery_fee: z.number(),
  reduction_amount: z.union([z.string(),z.number()]).nullable(),
  tradeApp: z.string(),
  amountComposition: z.object({
    compositionItems: z.array(z.object({
      amount: z.string(),
      category: z.string()
    }))
  })
});

const FetchUseStructuredAddressSchema = z.object({
  storeId: z.string(),
  lat: z.string(),
  lon: z.string(),
  campusId: z.number().optional(),
});
const FetchUseStructuredAddressRespSchema = z.object({
  campusPageName: z.string().nullable(),
  campusAddresses: z.any().nullable(),
  displayHouseNumber: z.boolean(),
  campusId: z.any().nullable(),
  useStructuredAddress: z.boolean()
});

const FetchMerchantAdIsInBlacklistSchema = z.object({
  merchantId: z.string(),
  tag: z.string(),
});

const FetchAddressLineSchema = z.object({
  from: z.object({
    lat: z.string().nullable(),
    lon: z.string().nullable()
  }),
  to: z.object({
    lat: z.string().nullable(),
    lon: z.string().nullable()
  }),
  geoType: z.string(),
  directionType: z.string()
}).merge(DefalutParamsSchema);

const routeSchema = z.object({
  mode: z.string(),
  distance: z.number(),
  duration: z.number(),
  direction: z.string(),
  polyline: z.array(z.number()),
  steps: z.array(z.object({
    instruction: z.string(),
    polyline_idx: z.array(z.number()),
    road_name: z.string(),
    dir_desc: z.string(),
    distance: z.number(),
    act_desc: z.string(),
    type: z.number()
  }))
});
const FetchAddressLineRespSchema = z.object({
  status: z.number(),
  message: z.string(),
  request_id: z.string(),
  result: z.object({
    routes: z.array(routeSchema)
  }).optional(),
});

const FetchRiderLocationSchema = DefalutParamsSchema;
const FetchRiderLocationRespSchema = z.object({
  "longitude": z.string(),
  "latitude": z.string()
});

const FetchDeliveryAreaSchema = DefalutParamsSchema;
const FetchDeliveryAreaRespSchema = z.object({
  type: z.string(),
  enabled: z.boolean(),
  areas: z.object({
    longitude: z.string(),
    campus_page_name: z.string(),
    id: z.number(),
    address: z.string(),
    name: z.string(),
    delivery_fee: z.null(),
    latitude: z.string()
  })
});

const FetchAOPResourceSchema = DefalutParamsSchema;
const FetchAOPResourceRespSchema = z.array(z.object({
  bannerUrl: z.string(),
  jumpUrl: z.string(),
  title: z.string(),
  terminal: z.number(),
  status: z.number(),
  source: z.string()
})
);

const FetchAOPPopupsContentSchema = z.object({
  serviceType: z.string(),
  sole: z.string(),
}).merge(DefalutParamsSchema);
const FetchAOPPopupsContentRespSchema = FetchAOPResourceRespSchema;

const GetCampusCollectionRespSchema = z.array(z.any().nullable());

const GetCampusDetailRespSchema = z.object({
  id: z.number(),
  campusName: z.string(),
  latitude: z.string(),
  longitude: z.string(),
  address: z.string(),
  announcement: z.string(),
  banner: z.string(),
  bannerUrl: z.string(),
  wxQrcode: z.string().nullable(),
  keyword: z.string()
});

const GetCampusDetailByBizIdRespSchema = z.object({
  campusBizId: z.string(),
  keyword: z.string(),
  id: z.number(),
  announcement: z.string(),
  banner: z.string(),
  latitude: z.string(),
  address: z.string(),
  wxQrcode: z.string().nullable(),
  bannerUrl: z.string(),
  longitude: z.string(),
  campusName: z.string()
});

const GetCampusIdByStoreIdSchema = DefalutParamsSchema;
const GetCampusIdByStoreIdRespSchema = z.object({});

const RemoveSchema = DefalutParamsSchema;
const RemoveRespSchema = z.object({
  total: z.number(),
  spu_count_map: z.record(z.unknown()), // An empty object, can be further specified if needed
  people_num: z.number().nullable(),
  version: z.number(),
  sn: z.string().nullable(),
  total_price: z.number(),
  records: z.array(RecordSchema), // An empty array, can be further specified if needed
  last_deal_record: RecordSchema
});

const RoundClearCartSchema = DefalutParamsSchema;
const RoundClearCartRespSchema = RemoveRespSchema;

const RoundGetCartSchema = DefalutParamsSchema.extend({
  table_id: z.string().optional(),
  version: z.union([z.string(), z.number()]).optional(),
});
const RoundGetCartRespSchema = RemoveRespSchema;

const GetEntranceRespSchema = z.object({
  PLUGIN_IS_SHOW_AD_ON_ORDER_DETAIL: z.boolean(),
  PLUGIN_GOODS_VIEWPORT_OFFSET: z.number(),
  PLUGIN_GOODS_PAGE_SIZE: z.number(),
  PLUGIN_GOODS_SCROLL_DEBOUNCE: z.number(),
  ADSENSE_SHOW_IN_ALIPAY: z.boolean(),
  ADSENSE_SHOW_IN_WECHAT: z.boolean(),
  AUTH_MERCHANT_LIST: z.array(z.string()),
  AUTH_ON: z.boolean(),
  PRE: z.string(),
  MIANGUANGGAO: z.string(),
  ALIPAY_PLUGIN: z.boolean(),
  DEBUG: z.boolean(),
  ACTIVITY_STATUS_VALID: z.string(),
  ALIPAY_BENEFIT: z.number(),
  SLS_ENABLE: z.boolean(),
  SLS_SEND_LOGS_TIMEOUT: z.number(),
  SENTRY_ENABLE: z.boolean(),
  SLS_WHITE_LIST: z.array(z.string()),
  SENTRY_WHITE_LIST: z.array(z.string()),
  QRCODE_MAPS: z.record(z.string()),
  BUTTONS: z.array(
    z.object({
      name: z.string(),
      type: z.string(),
      url: z.string(),
      method: z.string().nullable(),
      confirm: z.array(z.string()).nullable(),
      success: z.string().nullable(),
      pluginName: z.string().nullable(),
      componentName: z.string().nullable()
    })
  ),
  SQB_FLS_INFO: z.object({
    name: z.string(),
    desc: z.string(),
    image: z.string()
  }).nullable(),
  TAKEOUT_WECHAT_LINK: z.string().nullable()
});



const FetchAopContentNewSchema = DefalutParamsSchema;
const FetchAopContentNewRespSchema = z.array(z.any().nullable());

const CartSchema = DefalutParamsSchema;
const CartRespSchema = z.object({
  spu_count_map: z.record(z.unknown()), // Assuming the values are unknown. Adjust the type if needed.
  total: z.number(),
  sn: z.string().nullable(),
  records: z.array(RecordSchema), // Assuming the records array contains unknown types. Adjust if needed.
  total_price: z.number(),
  last_deal_record: RecordSchema.nullable(), // Assuming the value is unknown. Adjust the type if needed.
  people_num: z.union([z.string(),z.number()]).nullable(), // Assuming the value is unknown. Adjust the type if needed.
  version: z.number(),
});

const SetCartSchema = z.object({
  people_num: z.union([z.string(),z.number()]).nullable()
}).merge(DefalutParamsSchema);
const SetCartRespSchema = CartRespSchema;

const ItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  photo_url: z.string().optional(),
  description: z.string().nullable(),
  display_order: z.number().optional(),
  valid: z.nullable(z.any()),
  category_id: z.string(),
  merchant_id: z.string(),
  store_id: z.string(),
  price: z.number(),
  activity_price: z.number().nullable(),
  gift_card_discount_price: z.number().nullable(),
  discount_prices: z.array(z.object({
    can_enjoy: z.boolean(),
    discount_type: z.string(),
    discount_price: z.number(),
    quota_count: z.number(),
    discount: z.nullable(z.any())
  })),
  discount_texts: z.array(z.object({
    discount_text: z.string(),
    quota_count: z.number()
  })),
  second_activity_discount: z.number().nullable(),
  quota_count: z.nullable(z.any()),
  for_sale: z.boolean(),
  out_of_stock: z.boolean(),
  sku: z.nullable(z.any()),
  sku_default: z.nullable(z.any()),
  reset_sku: z.boolean(),
  unit: z.string(),
  unit_type: z.number(),
  category_name: z.string(),
  category_sort: z.nullable(z.any()),
  spu_type: z.string(),
  is_multiple: z.number(),
  item_tag: z.nullable(z.any()),
  add_count: z.nullable(z.any()),
  last30_days_sale_count: z.nullable(z.any()),
  hotsale_seq: z.nullable(z.any()),
  hotsale_product: z.boolean(),
  keep_price_equal: z.boolean(),
  arrival_price: z.number(),
  takeout_price: z.number(),
  min_sale_num: z.number().nullable(),
  sale_times: z.nullable(z.any()),
  barcode: z.nullable(z.any()),
  sale_terminals: z.array(z.number()),
  custom_out_of_stock: z.boolean(),
  handled_add_count: z.number().optional(),
  number: z.number()
});

const MkCustomInfoSchema = z.object({
  previous_pay_way: z.nullable(z.any()),
  gift_card_share_flag: z.boolean(),
  gift_card_overlay_flag: z.nullable(z.any()),
  specific_goods_card_list: z.array(z.any()),
  previous_biz_id: z.string().optional()
});

 const AddGooodSchema = z.object({
  compatible: z.boolean(),
  store_id: z.string(),
  merchant_id: z.string(),
  merchantId: z.string(),
  storeId: z.string(),
  items: z.array(z.object({
    client_version: z.number(),
    payway: z.number(),
    sub_payway: z.number(),
    store_id: z.string(),
    merchant_id: z.string(),
    service_type: z.number(),
    discount_strategy: z.string(),
    service_type_name: z.string(),
    from_cart: z.boolean(),
    cover: z.boolean(),
    compatible: z.boolean(),
    meal_type: z.string(),
    mk_custom_info: MkCustomInfoSchema,
    number: z.number(),
    item: ItemSchema,
    item_uid: z.string().optional()
  })),
  mk_custom_info: MkCustomInfoSchema
});

const AddAndRedeemBatchSchema = z.object({
  compatible: z.boolean(),
  store_id: z.string(),
  merchant_id: z.string(),
  merchantId: z.string(),
  storeId: z.string(),
  items: z.array(z.object({
    client_version: z.number(),
    payway: z.number(),
    sub_payway: z.number(),
    store_id: z.string(),
    merchant_id: z.string(),
    service_type: z.number(),
    discount_strategy: z.string(),
    service_type_name: z.string(),
    from_cart: z.boolean(),
    cover: z.boolean(),
    compatible: z.boolean(),
    meal_type: z.string(),
    mk_custom_info: MkCustomInfoSchema,
    number: z.number(),
    item: z.object({
      name: z.string(),
      price: z.number(),
      category_id: z.string(),
      id: z.string(),
      number: z.number()
    }),
    item_uid: z.string().optional()
  })),
  mk_custom_info: MkCustomInfoSchema
});
const redeemResultSchema = z.object({
  user_id: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  share_flag: z.boolean().nullable(),
  overlay_flag: z.boolean().nullable(), // Should be refined based on exact type
  merchant_redeem_detail: z.unknown().nullable(), // Should be refined based on exact type
  mk_custom_info: z.object({
    is_coupon_list_available: z.boolean(),
    cart_expire_time: z.number()
  }),
  redeem_digest: z.string().nullable(), // Should be refined based on exact type
  system_redeem_detail: z.unknown().nullable(), // Should be refined based on exact type
  ctime: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  total_discount: z.number(),
  redeem_details: z.array(z.unknown()), // Should be refined based on exact type
  store_id: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  merchant_id: z.union([z.string(),z.number()]).nullable()// Should be refined based on exact type
});
const cartSchema = z.object({
  people_num: z.union([z.string(),z.number()]).nullable(),
  spu_count_map: z.record(z.string(), z.number()),
  last_deal_record: z.unknown().nullable(), // Should be refined based on exact type
  records: z.array(RecordSchema),
  version: z.number(),
  total_price: z.number(),
  sn: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  total: z.number()
});
const AddAndRedeemBatchRespSchema = z.object({
  redeem_result: redeemResultSchema,
  cart: cartSchema
});


const RoundAddGoodSchema = z.object({
  table_id: z.union([z.number(),z.string()]).optional(),
  item_uid: z.string().optional(),
  category_id: z.string().optional(),
  service_type: z.number().optional(),
  item: ItemSchema.optional(),
  specs: z.nullable(z.any()),
  attributes: attributeSchema.optional(),
  material_ids: z.nullable(z.any()),
  materials: z.nullable(z.any()),
  item_tags: z.nullable(z.any()),
  material_groups: z.array(z.any()).optional(),
  ingredient_names: z.nullable(z.any()),
  bought_times: z.nullable(z.any()),
  latest_buy_time: z.nullable(z.any()),
  sale_time: SaleTimeSchema,
  photo_url_list: z.array(z.string()),
  package_items: z.array(z.object({
    item: ItemSchema,
    attributes: z.array(attributeSchema).optional(),
    material_ids: z.nullable(z.any()),
    materials: z.nullable(z.any()),
    item_tags: z.nullable(z.any()),
    material_groups: z.array(z.any()).optional(),
    ingredient_names: z.nullable(z.any()),
    package_must_order_products: z.nullable(z.any()),
    package_optional_groups: z.nullable(z.any()),
    bought_times: z.nullable(z.any()),
    latest_buy_time: z.nullable(z.any()),
    sale_time: SaleTimeSchema.optional(),
    package_group_id: z.string().optional(),
    package_group_name: z.string().optional(),
    listIndex: z.number().optional(),
    groupId: z.string().optional(),
    index: z.number().optional(),
    selectedAttributes: z.array(z.array(z.number())).optional(),
    spec: specOptionSchema.optional()
  })).optional(),
  user_icon: z.string().optional(),
  user_name: z.string().optional()
}).merge(DefalutParamsSchema);

const RoundReduceCartSchema = z.object({
  table_id: z.number(),
  service_type: z.number(),
  item_uid: z.union([z.string(),z.number()]).optional(),
}).merge(DefalutParamsSchema);
const RoundReduceCartRespSchema = z.object({
  sn: z.string().nullable(),
  people_num: z.union([z.string(),z.number()]).nullable(),
  version: z.number(),
  total: z.number(),
  total_price: z.number(),
  records: z.array(RecordSchema),
  last_deal_record: RecordSchema,
  spu_count_map: z.record(z.number()),
});

const FetchDiscountV2Schema = z.object({
  // compatible: z.boolean(),
  meal_type: z.string(),
  discount_strategy: z.string(),
  from_cart: z.boolean(),
  // merchantId: z.string(),
  // merchant_id: z.string(),
  mk_custom_info: MkCustomInfoSchema,
  payway: z.number(),
  recharge_and_pay: z.boolean(),
  service_type_name: z.string(),
  // storeId: z.string(),
  // store_id: z.string(),
  sub_payway: z.number(),
  table_id: z.union([z.number(),z.string()]),
  total_amount: z.number(),
}).merge(DefalutParamsSchema);
const FetchDiscountV2RespSchema = z.object({
  total_discount: z.number(),
  share_flag: z.boolean(),
  overlay_flag: z.unknown().nullable(),
  merchant_redeem_detail: z.object({
    name: z.string()
  }).nullable(),
  system_redeem_detail: z.object({
    name: z.string()
  }).nullable(),
  user_id: z.union([z.string(),z.number()]).nullable(),
  ctime: z.union([z.string(),z.number()]).nullable(),
  merchant_id: z.union([z.string(),z.number()]).nullable(),
  store_id: z.union([z.string(),z.number()]).nullable(),
  redeem_details: z.array(z.unknown()),
  mk_custom_info: z.object({
    cart_expire_time: z.number(),
    is_coupon_list_available: z.boolean(),
  }),
  redeem_digest: z.unknown().nullable(),
  alipay_redeem_detail: z.object({
    name: z.string()
  }).optional()
});

const GetRefundAmountSchema = z.object({
  sn: z.number(),
  refund_goods: z.array(z.object({
    id: z.string(),
    refund_count: z.number()
  }))
});
const GetRefundAmountRespSchema = z.object({
  refund_text: z.string(),
  deliver_amount: z.number(),
  refund_pays: z.array(refundPaySchema),
  refund_amount: z.number(),
  pack_amount: z.number(),
  refund_goods: z.array(refundGoodSchema)
});

const ClearCartSchema = DefalutParamsSchema.merge(z.object({service_type: z.number().optional()}));
const ClearCartRespSchema = CartRespSchema;

const GetCartSchema = DefalutParamsSchema.merge(z.object({version:z.union([z.string(),z.number()]).optional(), table_id:z.union([z.string(),z.number()]).optional()}));
const GetCartRespSchema = CartRespSchema; 

const ReOrderSchema = z.object({sn:z.union([z.string(),z.number()])}).merge(DefalutParamsSchema);
const ReOrderRespSchema = z.object({
  success: z.boolean(),
  error_code: z.union([z.string(),z.number()]).nullable(),
  error_msg: z.string().nullable(),
  error_tip_way: z.unknown().nullable(),
  invalid: z.array(z.unknown()),
  price: z.array(z.unknown()),
  check_fail_list: z.unknown().nullable(),
  pre_reduce_no: z.unknown().nullable(),
});

const AddAndRedeemBySpuIdsSchema = z.object({
  discount_strategy: z.string(),
  meal_type: z.string(),
  mk_custom_info: z.record(z.any()), // 定义为空对象或任意键值对的对象
  payway: z.number(),
  service_type: z.number(),
  specific_items: z.array(z.object({
    spu_id: z.string().uuid(),
    sku_id: z.string().nullable()
  })),
  sub_payway: z.number(),
  user_icon: z.string().url(),
  user_name: z.string(),
}).merge(DefalutParamsSchema);
const AddAndRedeemBySpuIdsRespSchema = AddAndRedeemBatchRespSchema;

const MigrateUserCartSchema = z.object({
  store_id: z.string(),
  service_type: z.number(),
  former_user_id: z.string()  
});

const CheckCashierOnlineSchema = DefalutParamsSchema;

const GoodsSchema = z.object({
  api_version: z.number(),
  search_terms: z.string(),
  service_type: z.number(),
}).merge(DefalutParamsSchema);
const GoodsRespSchema = z.object({
  records: z.array(GoodsItemSchema),
  total: z.number()
});


const GoodsDetailSchema = z.object({
  item_id: z.union([z.string(), z.number()]),
  service_type: z.number(),
}).merge(DefalutParamsSchema);
const GoodsDetailRespSchema = z.object({
  item: ItemSchema,
  specs: z.object({
    title: z.string(),
    options: z.array(specOptionSchema)
  }),
  attributes: z.array(attributeSchema),
  material_ids: z.unknown().nullable(),
  materials: z.unknown().nullable(),
  item_tags: z.unknown().nullable(),
  material_groups: z.array(z.unknown()),
  ingredient_names: z.unknown().nullable(),
  package_must_order_products: z.unknown().nullable(),
  package_optional_groups: z.unknown().nullable(),
  bought_times: z.unknown().nullable(),
  latest_buy_time: z.unknown().nullable(),
  sale_time: SaleTimeSchema
});

const GetRecommendMaterialsByGoodsSchema = z.object({
  item_ids: z.array(z.union([z.string(), z.number()]))
}).merge(DefalutParamsSchema);
const recommendationItemSchema = z.object({
  id: z.string(),
  name: z.string(),
  price: z.number(),
  seq: z.number(),
  status: z.number(),
  sale_count: z.number(),
});
const GetRecommendMaterialsByGoodsRespSchema = z.array(
  z.object({
    item_id: z.string(),
    recommends: z.array(recommendationItemSchema),
  })
);

const MaterialSchema = z.object({
  id: z.string().uuid(),
  name: z.string(),
  price: z.number(),
  number: z.number(),
  source: z.number(),
});
const AddMaterialAndRedeemSchema = z.object({
  materials: z.array(MaterialSchema),
  item_uid: z.string(),
  meal_type: z.string(),
  service_type: z.number(),
}).merge(DefalutParamsSchema);
const AddMaterialAndRedeemRespSchema = AddAndRedeemBatchRespSchema;

const TakeoutTimeRespSchema = z.array(z.object({
  label: z.string(),
  times: z.array(z.number()),
}));



const FetchGoodsV2Schema = z.object({
  page_size: z.number(),
  service_type: z.number(),
  page: z.number(),
  api_version: z.number(),
}).merge(DefalutParamsSchema);
const FetchGoodsV2RespSchema = z.object({
  total: z.number(),
  goods: z.array(z.object({
    category_id: z.string(),
    items: z.array(GoodsItemSchema),
  })),
});

const OrderSchema = z.object({sn:z.string()}).merge(DefalutParamsSchema);
const OrderRespSchema = z.object({
  campus_order: z.boolean(),
  order_campus_delivery: z.unknown().nullable(),
  merchant_discount_name: z.unknown().nullable(),
  merchant_activity_sn: z.union([z.string(),z.number()]).nullable(),
  merchant_activity_type: z.unknown().nullable(),
  buyer_uid: z.string(),
  buyer_login: z.string(),
  store_name: z.string(),
  terminal_id: z.string(),
  qr_code_name: z.string(),
  qr_code_type: z.unknown().nullable(),
  items: ItemArraySchema,
  trade_no: z.string(),
  remark: z.string().nullable(),
  extra: z.object({
    cellphone: z.string().nullable(),
    campus_delivery: z.string().nullable(),
    pre_reduce_no: z.string().nullable(),
    hb_fq: z.string().nullable(),
    delivery_info: z.string().nullable(),
    store_name: z.string(),
    campus_station: z.string().nullable(),
    dm_info: z.string().nullable(),
    book_order_info_dto: z.string().nullable(),
    wx_trace_id: z.string().nullable(),
    mp_scene: z.string().nullable(),
  }),
  status: z.number(),
  compatible: z.boolean(),
  deleted: z.boolean(),
  sub_payway: z.number(),
  payway: z.number(),
  redeem_details: z.unknown().nullable(),
  sqb_trans_sn: z.string(),
  order_campus_station: orderCampusStationSchema,
  station_address: z.unknown().nullable(),
  campus_exception: z.unknown().nullable(),
  pay_time: z.number(),
  refresh_on_mini: z.boolean(),
  cancel_enabled: z.boolean(),
  refund_revoke_enabled: z.boolean(),
  refund_apply: z.unknown().nullable(),
  wx_goods: z.unknown().nullable(),
  membership_list: z.unknown().nullable(),
  trans_sn: z.string(),
  cashier_biz_params: z.unknown().nullable(),
  goods_batch_infos: z.unknown().nullable(),
  order_seq: z.string(),
  allow_card_pay: z.boolean(),
  version: z.number(),
  delivery_floor: z.string(),
  process_status: z.string(),
  ctime: z.number(),
  merchant_id: z.string(),
  subject: z.unknown().nullable(),
  store_id: z.string(),
  pack_amount: z.number(),
  discount_amount: z.unknown().nullable(),
  merchant_discount: z.number(),
  qr_code: z.string(),
  client_sn: z.string(),
  cashier_mode: z.boolean(),
  table_no: z.string(),
  area_id: z.unknown().nullable(),
  book_order: z.boolean(),
  book_time: z.unknown().nullable(),
  refund_apply_enabled: z.boolean(),
  client_tracking_data: z.unknown().nullable(),
  order_track_status: z.unknown().nullable(),
  invoice: invoiceSchema,
  pay_controll: z.unknown().nullable(),
  pay_channel_list: z.unknown().nullable(),
  refund_amount: z.number(),
  table_id: z.string(),
  total_discount: z.number(),
  packed: z.boolean(),
  terminal_sn: z.union([z.string(),z.number()]).nullable(),
  extra_info: extraInfoSchema,
  id: z.string(),
  type: z.string(),
  sn: z.string(),
  mtime: z.number(),
  original_amount: z.number(),
  effective_amount: z.number(),
  profit_sharing_amount: z.unknown().nullable(),
  receive_amount: z.number(),
  buyer_pay_amount: z.number(),
  order_tag: z.number(),
});

const OrdersSchema = z.object({
  page: z.number(),
  page_size: z.number(),
  service_type: z.number(),
  table_id: z.union([z.number(),z.string()]),
  meal_type: z.string(),
}).merge(DefalutParamsSchema);
const OrdersRespSchema = z.object({
  records: z.array(OrderRespSchema),
  total: z.number(),
  page: z.number(),
  page_size: z.number(),
});

const GetTableOrderSchema = z.object({
  table_id: z.union([z.number(),z.string()]),
}).merge(DefalutParamsSchema);
const GetTableOrderRespSchema = OrderRespSchema;


const InitOrderV2Schema = z.object({
  service_type: z.number(),
  items: z.array(z.object({
    item_uid: z.string(),
    number: z.number(),
  })),
  table_id: z.union([z.number(),z.string()]),
}).merge(DefalutParamsSchema);
const InitOrderV2RespSchema = z.object({
  order_main: OrderMainSchema,
  pre_create_order_vo: z.unknown().nullable(),
  cart_check_result: z.unknown().nullable(),
  pay_result: z.unknown().nullable(),
});

const AddOrderGoodsV2Schema = z.object({
  service_type: z.number(),
  items: z.array(z.object({
    item_uid: z.string(),
    number: z.number(),
  })),
  order_sn: z.string(),
  remark: z.string(),
  table_id: z.union([z.number(),z.string()]),
}).merge(DefalutParamsSchema);
const AddOrderGoodsV2RespSchema = z.object({
  order_main: OrderMainSchema,
  pre_create_order_vo: z.unknown().nullable(),
  cart_check_result: z.unknown().nullable(),
  pay_result: z.unknown().nullable(),
});

const PayLockCancel = z.object({
  table_id: z.union([z.number(),z.string()]),
}).merge(DefalutParamsSchema);

const FetchLatestOrderSchema = z.object({
  table_id: z.union([z.number(),z.string()]),
}).merge(DefalutParamsSchema);
const FetchLatestOrderRespSchema = OrderRespSchema;

const CancelOrderSchema = z.object({
  sn: z.union([z.string(),z.number()]),
}).merge(DefalutParamsSchema);
const CancelOrderRespSchema = z.boolean();

const CancelRefundSchema = z.object({
  sn: z.union([z.string(),z.number()]),
}).merge(DefalutParamsSchema);
const CancelRefundRespSchema = z.boolean();

const GetRefundGoodsSchema = z.object({
  sn: z.union([z.string(),z.number()]),
}).merge(DefalutParamsSchema);

const PostRefundSchema = z.object({
  sn: z.string(),
  goods: z.array(z.object({
    id: z.string(),
    name: z.string(),
    attach_info: z.nullable(z.unknown()),
    main_image: z.string().nullable(),
    refund_count: z.number(),
  })),
  proofs: z.array(z.unknown()),  // Assuming proofs can contain any type of data
  reason: z.string(),
}).merge(DefalutParamsSchema);

const GetRefundWhiteListschema = z.object({
  biz_type: z.string(),
  campus_id: z.number(),
}).merge(DefalutParamsSchema);

const GetWhiteListSchema = z.object({
  checkParams: z.array(z.object({
    bizType: z.string(),
    storeId: z.string().uuid(),
    merchantId: z.string().uuid(),
  })),
}).merge(DefalutParamsSchema);
const GetWhiteListRespSchema = z.array(z.object({
  checkResult: z.boolean(),
  merchantId: z.string().uuid(), // Assuming merchantId is a UUID string
  campusId: z.string().nullable(), // Assuming campusId can be a string or null
  storeId: z.string().uuid(), // Assuming storeId is a UUID string
  bizType: z.string()
}))

const FetchPayloadSchema = z.object({
  discount_params: z.object({
    sub_payway: z.number(),
    payway: z.number(),
    recharge_and_pay: z.boolean(),
    mk_custom_info: MkCustomInfoSchema,
  }),
  preset_time: z.number(),
  order_type: z.string(),
  packed: z.boolean(),
  api_version: z.number(),
}).merge(DefalutParamsSchema);
const FetchPayloadRespSchema = z.object({
  defaultAddress: z.string(),
  discounts: redeemResultSchema,
  tradeApp: z.string(),
  addressList: z.string(),
  duration: durationSchema,
  brandActivity: z.string(),
  inCampus: z.boolean(),
  reductionAmount: z.number(),
  bookTimes: z.array(z.unknown()),
  discountAmount: z.number(),
  amountComposition: z.array(z.object({
    amount: z.string(),
    category: z.string()
  })),
  deliveryAmount: z.number(),
  hasAgreement: z.boolean(),
  totalAmount: z.number(),
  cart: CartRespSchema,
  packAmount: z.number()
});

const FetchExtraSchema = z.object({
  field_code: z.string(),
  field_style: z.string(),
  terminal_code: z.string(),
  client_version: z.string(),
  store_sn: z.string(),
}).merge(DefalutParamsSchema);
const durationEntrySchema = z.object({
  cost: z.number(),
  start: z.number(),
  end: z.number()
});
const FetchExtraRespSchema = z.object({
  planContent: z.object({
    records: z.array(z.unknown()),
    total: z.number()
  }),
  wxMchId: z.string(),
  wxStoreId: z.string(),
  wxGoodsActs: z.string(),
  duration: z.object({
    aop: durationEntrySchema,
    wxMchInfo: durationEntrySchema
  }),
  wxSubMchId: z.string(),
  payConfig: z.string(),
  wxProducts: z.string()
});

const MakeOrderAndPayForFistPayRoundSchema = z.object({
  table_id: z.union([z.number(),z.string()]),
  pay_way: z.number(),
  type: z.string(),
  items: z.array(z.object({
    item_uid: z.string(),
    number: z.number(),
  })),
  terminal_sn: z.string(),
  total_discount: z.number(),
  redeem_digest: z.string().nullable(),
  mk_custom_info: MkCustomInfoSchema,
  area_id: z.number(),
  table_name: z.string(),
}).merge(DefalutParamsSchema);

const wapPayRequestSchema = z.object({
  time_stamp: z.string(),
  pay_sign: z.string(),
  app_id: z.string(),
  sign_type: z.string(),
  nonce_str: z.string(),
  trade_no: z.string().nullable(),
  redirect_url: z.string().nullable(),
  scan_qr_code_content: z.string().nullable(),
  encrypt_data: z.string().nullable(),
  package: z.string()
});
const MakeOrderAndPayForFistPayRoundRespSchema = z.object({
  sn: z.string(),
  trans_sn: z.string().nullable(),
  client_sn: z.string(),
  net_amount: z.number().nullable(),
  status: z.string().nullable(),
  total_amount: z.string(),
  total_discount: z.number().nullable(),
  operator: z.string().nullable(),
  subject: z.string().nullable(),
  qr_code: z.string().nullable(),
  wap_pay_request: wapPayRequestSchema,
  has_risk: z.boolean(),
  action: z.string().nullable(),
  risk_id: z.string().nullable(),
  cart_check_result: z.unknown().nullable(),
  acquiring: z.unknown().nullable()
});

const PayForFistPayRoundOrderSchema = z.object({
  table_id: z.union([z.number(),z.string()]),
  pay_way: z.number(),
  type: z.string(),
  sn: z.string(),
  all_pay: z.boolean(),
  terminal_sn: z.string(),
  total_discount: z.number(),
  redeem_digest: z.string().nullable(),
  mk_custom_info: MkCustomInfoSchema,
  area_id: z.number(),
  table_name: z.string(),
}).merge(DefalutParamsSchema);
const PayForFistPayRoundOrderRespSchema = MakeOrderAndPayForFistPayRoundRespSchema;

const GetOrderOrBatchDetailSchema = z.object({
  table_id: z.union([z.number(),z.string()]),
  all_pay: z.boolean(),
  need_local_redeems: z.boolean(),
  pay_way: z.string().nullable(),
});
const GetOrderOrBatchDetailRespSchema = z.object({
  items: z.array(z.unknown()),
  redeem: redeemResultSchema,
  goods_batch_infos: z.unknown().nullable(),
  need_pay_amount: z.number(),
  cart: z.object({
    sn: z.string().nullable(),
    people_num: z.number().nullable(),
    version: z.number(),
    total: z.number(),
    total_price: z.number(),
    records: z.array(RecordSchema),
    last_deal_record: RecordSchema,
    spu_count_map: z.record(z.number()),
  }),
  disable_redeem: z.boolean(),
});

const CancelBatchOrderSchema = z.object({
  sn: z.string(),
  batch_no: z.string(),
});

// | 'subscribe_order' /*店内点单*/
//   | 'take_out_order' /*外卖订单*/
//   | 'pre_order' /*自取订单*/
//   | 'EAT_FIRST_ORDER' /*围餐*/
//   | string;
const FetchCashierParamsSchema = z.object({
  store_id: z.string(),
  order_type: z.union([z.enum(['subscribe_order','pre_order','EAT_FIRST_ORDER']),z.string()]), // 使用枚举类型来定义 order_type
  packed: z.boolean(),
});
const FetchCashierRespSchema = z.object({
  tradeApp: z.string(),
  amountComposition: z.object({
    compositionItems: z.array(z.object({
      amount: z.string(),
      category: z.string(),
    })),
  }),
});

const PayCancelSchema = z.object({orderSn:z.string()}).merge(DefalutParamsSchema);
const PayCancelRespSchema = z.unknown().nullable();

const FetchStoreDetailSchema = z.object({
  geoType: z.string(),
  storeId: z.string(),
});
const FetchStoreDetailRespSchema = z.object({
  collectionNums: z.null(),
  firstIndustry: z.string(),
  firstIndustryCode: z.string(),
  industry: z.string(),
  industryCode: z.string(),
  merchantName: z.string(),
  merchantId: z.string(),
  merchantSn: z.string(),
  alipayStatus: z.number(),
  wechatStatus: z.number(),
  disabled: z.number(),
  discounts: z.object({
    globalDiscount: z.null(),
    collectionDiscount: z.null(),
    timeDiscount: z.array(z.unknown()),
    priceBreak: z.null(),
    priceBreaks: z.array(z.unknown()),
    priceBreakCards: z.array(z.unknown()),
    returnCards: z.array(z.unknown()),
    membershipCards: z.array(z.unknown()),
    singleActivity: z.number(),
    secondDiscounts: z.array(z.unknown()),
  }),
  discount: z.null(),
  organizationPath: z.null(),
  categoriesNames: z.null(),
  coupons: z.array(z.unknown()),
  storeId: z.string(),
  storeName: z.string(),
  storeExtraName: z.string(),
  contactPhone: z.string(),
  slogan: z.string(),
  payWay: z.number(),
  categoryId: z.null(),
  location: z.object({
    lat: z.string(),
    lon: z.string(),
  }),
  storeAddress: z.object({
    province: z.string(),
    city: z.string(),
    district: z.string(),
    streetAddress: z.string(),
    address: z.string(),
    aoiName: z.null(),
    houseNumber: z.string(),
    adcode: z.null(),
  }),
  storePhoto: z.object({
    innerPhotos: z.array(z.unknown()),
    outerPhotos: z.array(z.unknown()),
    goodsPhotos: z.array(z.unknown()),
    coverPhoto: z.string(),
    logoUrl: z.null(),
  }),
  storeBasic: z.object({
    averageConsumption: z.null(),
    storeArea: z.null(),
    businessHoursStart: z.string(),
    businessHoursEnd: z.string(),
  }),
  onceOpened: z.boolean(),
  storeType: z.number(),
  storeSn: z.string(),
});

const GetStoreMultiConfigSchema = z.object({
  keys: z.array(z.string()),
}).merge(DefalutParamsSchema);
const GetStoreMultiConfigRespSchema = z.array(z.object({
  ctime: z.number(), // Assuming the timestamp is stored as a number
  app_id: z.string(),
  owner_type: z.string(),
  owner_id: z.string(),
  enabled: z.boolean(),
  mtime: z.number(), // Assuming the timestamp is stored as a number
  value: z.string(),
  id: z.nullable(z.string()), // Assuming id can be null
  name: z.string(),
}));

const FetchSelfTimeSchema = DefalutParamsSchema;
const FetchSelfTimeRespSchema = z.array(z.object({
  label: z.string(),
  times: z.array(z.number()),
}));

const AddCollectionStoreSchema = DefalutParamsSchema;
const AddCollectionStoreRespSchema = z.boolean();

const CancelCollectionStoreSchema = DefalutParamsSchema;
const CancelCollectionStoreRespSchema = z.boolean();

const GetCollectionStoreListSchema = DefalutParamsSchema;
const GetCollectionStoreListRespSchema = z.array(z.string())

const GetConfigForStoredDiscountSchema = DefalutParamsSchema;
const GetConfigForStoredDiscountRespSchema = z.object({
  storedCardCtrl: z.boolean(),
})
//storeSn
const GetStoreInCampusSchema = DefalutParamsSchema.extend({storeSn:z.string()});
const GetStoreInCampusRespSchema = z.boolean();

const GetWechatMchCodeSchema = DefalutParamsSchema;
const GetWechatMchCodeRespSchema = z.object({
  mchCode: z.string(),
  subMchCode: z.string(),
});

const CheckMerchantQualificationSchema = z.object({
  apiVersion: z.number(),
}).merge(DefalutParamsSchema);
const CheckMerchantQualificationRespSchema = z.object({
  business_license_url: z.string().nullable(),
  food_license_url: z.string().nullable(),
  code: z.string().nullable(),
  flag: z.boolean(),
});

const GetStoreHBFQConfigSchema = z.object({
  storeSn: z.string().optional(),
  payway: z.string().optional(),
  subPayway: z.string().optional(),
});

const alipayParamsSchema = z.array(
  z.object({
    hb_fq_seller_percent: z.string(),
    hb_fq_num: z.string(),
    seller_fee_rate: z.number(),
    buyer_fee_rate: z.number(),
  })
);
// Define the schema for the alipay_huabei_status and alipay_credit_params arrays
const alipayCreditParamsSchema = z.array(
  z.object({
    fq_seller_percent: z.string(),
    fq_num: z.string(),
    seller_fee_rate: z.number(),
    buyer_fee_rate: z.number(),
  })
);
// Define the schema for the lkl_up_trade_params object
const lklUpTradeParamsSchema = z.object({
  app_id: z.string(),
  cert_id: z.string(),
  sys_pid: z.string(),
  category: z.string().nullable(),
  fee_rate: z.string(),
  sign_type: z.string(),
  public_key: z.string(),
  service_id: z.string().nullable(),
  merchant_name: z.string(),
  up_private_key: z.string(),
  alipay_store_id: z.string().nullable(),
  provider_mch_id: z.string(),
  alipay_sub_mch_id: z.string(),
  liquidation_next_day: z.boolean(),
  fee_rate_tag: z.object({
    "4": z.string(),
  }),
});
const GetStoreHBFQConfigRespSchema = z.object({
  vendor_id: z.string(),
  merchant_id: z.string(),
  merchant_sn: z.string(),
  merchant_name: z.string(),
  merchant_country: z.string(),
  currency: z.string(),
  longitude: z.string(),
  latitude: z.string(),
  district_code: z.string(),
  store_id: z.string(),
  store_sn: z.string(),
  store_client_sn: z.string().nullable(),
  store_name: z.string(),
  store_city: z.string(),
  clearance_provider: z.number(),
  pay_status: z.number(),
  sharing_switch: z.number(),
  common_switch: z.string(),
  merchant_daily_max_sum_of_trans: z.number(),
  merchant_daily_max_credit_limit_trans: z.number().nullable(),
  merchant_monthly_max_credit_limit_trans: z.number().nullable(),
  union_over_seas_wallet_single_tran_limit: z.number(),
  union_over_seas_wallet_day_tran_limit: z.number(),
  payway_day_credit_limits: z.unknown().nullable(),
  payway_month_credit_limits: z.unknown().nullable(),
  merchant_bankcard_single_max_limit: z.number(),
  merchant_bankcard_day_max_limit: z.number(),
  merchant_bankcard_month_max_limit: z.number(),
  merchant_daily_payway_max_sum_of_trans: z.any().nullable(),
  merchant_single_max_of_tran: z.unknown().nullable(),
  credit_pay: z.unknown().nullable(),
  use_client_store_sn: z.unknown().nullable(),
  deposit: z.unknown().nullable(),
  is_sent_store_id: z.boolean(),
  is_need_refund_fee_flag: z.unknown().nullable(),
  hit_payway: z.unknown().nullable(),
  alipay_huabei_status: z.number(),
  alipay_huabei_limit: z.number(),
  alipay_huabei_params: alipayParamsSchema,
  alipay_credit_params: alipayCreditParamsSchema,
  provider: z.number(),
  lkl_up_trade_params: lklUpTradeParamsSchema,
  term_info: z.object({
    term_id: z.string(),
    term_type: z.string().nullable(),
    serial_num: z.string().nullable(),
  }),
  term_id: z.string(),
  channel_name: z.string(),
  trade_app: z.string(),
});


const CalcHBFQServiceChargeSchema = z.object({
  alipayHuabeiLimit: z.union([z.number(), z.string()]),
  alipayHuabeiParams: z.array(z.object({})), 
  totalAmount: z.union([z.number(), z.string()]),
});

const FetchRecommendSchema  = GetRecommendMaterialsByGoodsSchema;
const FetchRecommendRespSchema = z.array(
  z.object({
    item_id: z.string(),
    recommends: z.array(recommendationItemSchema),
  })
);

// const FetchDeliverFeeSchema = z.object({
//   delivery_info: FetchDeliverFeeSchema,
// }).merge(DefalutParamsSchema);

const GetStoreCampusListSchema = z.object({
  storeId: z.string(),
  longitude: z.string(),
  latitude: z.string(),
});

const GetLoveMerchantGraySchema = DefalutParamsSchema;
const GetLoveMerchantGrayRespSchema = z.boolean();

const GetCollectionsIdListSchema = DefalutParamsSchema;
const GetCollectionsIdListRespSchema = z.array(z.string());

const ConfirmTakeoutAgreementSchema = DefalutParamsSchema;
const ConfirmTakeoutAgreementRespSchema = z.boolean();

const GetShareMessageSchema = z.object({
  page_id: z.string(),
  query_store_id: z.string(),
});
const slotSchema = z.object({
  flag: z.string(),
  moduleBuildId: z.number(),
  hasBusinessConfig: z.boolean(),
  moduleKey: z.string(),
  boxStyle: z.object({
    height: z.string(),
  }),
  rank: z.number(),
  moduleId: z.number(),
  component: z.string(),
  name: z.string(),
  config: z.object({}).optional(),
});
const pageInfoSchema = z.object({
  config_name: z.string().nullable(),
  source_path: z.string().nullable(),
  source_type: z.string().nullable(),
  remark: z.string(),
  url: z.string(),
  params: z.unknown().nullable(),
  extend: z.unknown().nullable(),
  status: z.number(),
  product_group_id: z.string(),
  path: z.string().nullable(),
  type: z.number(),
  id: z.number(),
  page_id: z.string(),
  name: z.string(),
  dev_code: z.string(),
  source: z.unknown().nullable(),
});
const GetShareMessageRespSchema = z.object({
  template_detail: z.object({
    flag: z.string(),
    moduleBuildId: z.number(),
    hasBusinessConfig: z.boolean(),
    moduleKey: z.string(),
    boxStyle: z.object({
      height: z.string(),
    }),
    rank: z.number(),
    moduleId: z.number(),
    component: z.string(),
    slots: z.object({
      modules: z.array(slotSchema),
      nav: slotSchema,
    }),
    name: z.string(),
    config: z.object({}).optional(),
  }),
  template_id: z.number(),
  template_name: z.string(),
  store_id: z.string(),
  pageInfo: pageInfoSchema,
  page_id: z.string(),
  product_id: z.string().nullable(),
  theme_id: z.string().nullable(),
  theme_detail: z.unknown().nullable(),
});

const UpdatePeopleNumSchema = z.object({
  people_num: z.union([z.string(),z.number()]).nullable(), // 假设userCount是一个数字类型
  user_icon: z.string(),
  user_name: z.string(),
});


const UpdateUserCountSchema = UpdatePeopleNumSchema;
const UpdateUserCountRespSchema = cartSchema;


const CampusItemSchema = z.object({
  id: z.number(),
  campusBizId: z.string(),
  campusName: z.string(),
  campusPageName: z.string(),
  campusZoneId: z.number(),
  campusZoneBizId: z.string(),
  campusZoneName: z.string(),
  province: z.string(),
  city: z.string(),
  district: z.string(),
  provinceCode: z.string(),
  cityCode: z.string(),
  districtCode: z.string(),
  latitude: z.string(),
  longitude: z.string(),
  address: z.string(),
  hotSaleShow: z.number(),
  announcement: z.string().nullable(),
  banners: z.array(z.object({
    bannerUrl: z.string(),
    jumpUrl: z.string().nullable(),
    status: z.number(),
    terminal: z.number(),
    title: z.string().nullable(),
    pcId: z.string().nullable()
  })),
  banner: z.string().nullable(),
  bannerUrl: z.string().nullable(),
  cover: z.string(),
  status: z.number(),
  thirdDeliveryType: z.string().nullable(),
  outsideDeliveryType: z.string().nullable(),
  deliveryType: z.string().nullable(),
  deliveryRange: z.string().nullable(),
  fence: z.string(),
  inFence: z.boolean(),
  ctime: z.number(),
  storeId: z.string().nullable(),
  campusOperateToolType: z.string().nullable()
});

const GetNearByCampusSchema = z.object({
  lat: z.number(),
  lon: z.number(),
});

const GetNearbyCampusListSchema = GetNearByCampusSchema;
const GetNearbyCampusListRespSchema = z.array(CampusItemSchema);

const GetCustomerForChooseSchema = GetNearByCampusSchema;

const GetStoreListSchema = z.object({
  compatible: z.boolean().optional(),
  size: z.number(),
  distance: z.number(),
  distanceFrom: z.number(),
  geoType: z.string(),
  takeoutStores: z.number(),
  sort: z.string(),
  page: z.number(),
  lat: z.number(),
  lon: z.number(),
  debug: z.number()
});

const GetStoreListRespSchema = z.object({
  currentPage: z.number(),
  pageSize: z.number(),
  totalCount: z.number(),
  totalPage: z.number(),
  value: z.array(storeSetSchema)
});

//{ campusId: campus_id, page, pageSize: page_size }
const GetCampusGroupsSchema = z.object({
  campusId: z.union([z.string(),z.number()]),
  page: z.number(),
  pageSize: z.number()
});

const GetCampusGroupRoomsSchema = GetCampusGroupsSchema;

const GetCustomerServiceUrlSchema = z.object({
  orderSeq: z.string(),
  orderSn: z.string(),
  ctime: z.number(),
  username: z.string(),
  userAddress: z.string(),
  storeSn: z.string(),
  storeName: z.string(),
  orderStatus: z.string(),
  deliveryType: z.string(),
  campusStore: z.boolean()
});

const GetGatherDataRespSchema = z.object({
  bannerUrl: z.string(),
  mtime: z.number(),
  campusId: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  ctime: z.number(),
  storeList: z.unknown().nullable(), // Should be refined based on exact type
  sortType: z.number(),
  id: z.number(),
  status: z.number(),
  introduce: z.string(),
  name: z.string()
});

const GetGatherStoreListSchema = z.object({
  esId: z.union([z.string(),z.number()]).nullable(), // Should be refined based on exact type
  score: z.unknown().nullable(), // Should be refined based on exact type
  size: z.number(),
  distance: z.number(),
  distanceFrom: z.number(),
  geoType: z.string(),
  sort: z.string(),
  storeSetId: z.string(),
  campusId: z.number()
}).merge(DefalutParamsSchema);
const GetGatherStoreListRespSchema = z.object({
  storeCount: z.number(),
  storeSetList: z.array(storeSetSchema)
});

const GetRefundGoodsRespSchema = z.array(z.object({
  price: z.number(),
  gift_food: z.boolean(),
  discount_price: z.number(),
  attach_info: z.any().nullable(), // Assuming attach_info can be any type or null
  main_image: z.string().url(), // Assuming main_image is a URL string
  open_table_must_order: z.boolean(),
  refund_count: z.number(),
  goods_process_status: z.string(),
  name: z.string(),
  id: z.string().uuid(), // Assuming id is a UUID string
  count: z.number()
}));

const GetOssPolicySchema = z.object({
  scene: z.string(), // Assuming 'scene' can only be 'proofs'
  api_version: z.number(),
}).merge(DefalutParamsSchema);
const GetOssPolicyRespSchema = z.object({
  'x-oss-security-token': z.string(),
  policy: z.string(),
  signature: z.string(),
  uploadUrl: z.string(),
  ossAccessKeyId: z.string(),
  key: z.string(),
  visitUrl: z.string(),
});

const GetRefundDetailSchema = z.object({sn:z.string()}).merge(DefalutParamsSchema);
const GetRefundDetailRespSchema = z.object({
  apply_proof: z.array(z.unknown()),
  notified: z.string(),
  processed: z.string(),
  operate_log: z.array(z.object({
    status: z.string(),
    type: z.string(),
    sub_message: z.string(),
    seller_message: z.string(),
    refund_records: z.unknown().nullable(),
    seller_sub_message: z.string(),
    ctime: z.number(),
    refund_batch_no: z.unknown().nullable(),
    message: z.string(),
    apply_proof: z.array(z.unknown()),
  })),
  refund_amount: z.number(),
  id: z.number(),
  expire_time: z.number(),
  order_type: z.string(),
  part_refund: z.boolean(),
  mtime: z.number(),
  refund_items: z.array(z.object({
    name: z.string(),
    attach_info: z.string().nullable(),
    main_image: z.string(),
    refund_amount: z.number(),
    id: z.string(),
    refund_count: z.number(),
  })),
  ctime: z.number(),
  apply_reason: z.string(),
  pack_amount: z.number(),
  order_seq: z.string(),
  user_id: z.string(),
  merchant_id: z.string(),
  delivery_amount: z.number(),
  refund_type: z.string(),
  store_id: z.string(),
  apply_time: z.number(),
  refund_time: z.number().nullable(),
  reject_time: z.number().nullable(),
  apply_status: z.string(),
  refund_process_type: z.string().nullable(),
  reject_reason: z.string().nullable(),
  sn: z.string(),
});

const GetStoresForIdsRespSchema = z.array(storeSetSchema);

const FetchMyOrderSchema = DefalutParamsSchema;
const FetchMyOrderRespSchema = z.array(orderSchema);

// 使用 zod-to-ts 生成类型，并保存到不同文件
const generateTypeFile = (schema: z.ZodTypeAny, typeName: string, fileName: string) => {
  const { node } = zodToTs(schema, typeName);
  const printer = ts.createPrinter();
  const file = ts.createSourceFile(fileName, '', ts.ScriptTarget.Latest, false, ts.ScriptKind.TS);
  const typeDefinition = printer.printNode(ts.EmitHint.Unspecified, node, file);
  const output = `export interface ${typeName} ${typeDefinition}\n`;
  writeFileSync(fileName, output);
  console.log(`Type definitions generated and saved to ${fileName}`);
};

// 生成各个文件
// generateTypeFile(StoredCardActivitySchema, 'IStoredCardActivity', './@types/IStoredCardActivity.ts');
// generateTypeFile(DeliveryActivitySchema, 'IDeliveryActivity', './@types/IDeliveryActivity.ts');
// generateTypeFile(SecondActivitySchema, 'ISecondActivity', './@types/ISecondActivity.ts');
// generateTypeFile(SingleActivitySchema, 'ISingleActivity', './@types/ISingleActivity.ts');
// generateTypeFile(ActivitySchema, 'IActivity', './@types/IActivity.ts');
// generateTypeFile(GoodsItemSchema, 'IGoodsItem', './@types/IGoodsItem.ts');
// generateTypeFile(StoreSchema, 'IStore', './@types/IStore.ts');



// // 最后生成 Data 类型
// generateTypeFile(GatherIndexRespSchema, 'IGatherIndexResp', './@types/IGatherIndexResp.ts');
// generateTypeFile(GatherIndexParams, 'IGatherIndexParams', './@types/IGatherIndexParams.ts');

const schemaNamesObject = {
  StoredCardActivitySchema,
  DeliveryActivitySchema,
  SecondActivitySchema,
  SingleActivitySchema,
  ActivitySchema,
  GoodsItemSchema,
  StoreSchema,
  GatherIndexParams,
  PayloadSchema,
  FetchStoredBillRecordSchema,
  GetDefaultAddressV2Schema,
  AddAddressSchema,
  DelAddressSchema,
  FetchDeliverFeeSchema,
  FetchUseStructuredAddressSchema,
  FetchMerchantAdIsInBlacklistSchema,
  CartSchema,
  ItemSchema,
  MkCustomInfoSchema,
  AddGooodSchema,
  AddAndRedeemBatchSchema,
  RoundAddGoodSchema,
  FetchDiscountV2Schema,
  GetRefundAmountSchema,
  DefalutParamsSchema,
  ClearCartSchema,
  ReOrderSchema,
  AddAndRedeemBySpuIdsSchema,
  MigrateUserCartSchema,
  CheckCashierOnlineSchema,
  GoodsSchema,
  GoodsDetailSchema,
  GetRecommendMaterialsByGoodsSchema,
  MaterialSchema,
  AddMaterialAndRedeemSchema,
  FetchGoodsV2Schema,
  OrderSchema,
  OrdersSchema,
  GetTableOrderSchema,
  InitOrderV2Schema,
  AddOrderGoodsV2Schema,
  PayLockCancel,
  FetchLatestOrderSchema,
  PostRefundSchema,
  GetRefundWhiteListschema,
  GetWhiteListSchema,
  FetchPayloadSchema,
  FetchExtraSchema,
  MakeOrderAndPayForFistPayRoundSchema,
  PayForFistPayRoundOrderSchema,
  GetOrderOrBatchDetailSchema,
  CancelBatchOrderSchema,
  FetchCashierParamsSchema,
  GetStoreHBFQConfigSchema,
  CalcHBFQServiceChargeSchema,
  FetchRecommendSchema,
  GetStoreCampusListSchema,
  GetLoveMerchantGraySchema,
  GetShareMessageSchema,
  UpdatePeopleNumSchema,
  UpdateUserCountSchema,
  GetNearByCampusSchema,
  GetNearbyCampusListSchema,
  GetCustomerForChooseSchema,
  GetStoreListSchema,
  GetCampusGroupsSchema,
  GetCampusGroupRoomsSchema,
  GetCustomerServiceUrlSchema,
  // GetDefaultAddressSchema,
  FetchAddressLineSchema,
  FetchRiderLocationSchema,
  FetchDeliveryAreaSchema,
  FetchAOPResourceSchema,
  FetchAOPPopupsContentSchema,
  GetCampusIdByStoreIdSchema,
  RemoveSchema,
  RoundClearCartSchema,
  RoundGetCartSchema,
  CancelOrderSchema,
  CancelRefundSchema,
  GetRefundGoodsSchema,
  GetOssPolicySchema,
  GetRefundDetailSchema,
  PayCancelSchema,
  FetchStoreDetailSchema,
  GetStoreMultiConfigSchema,
  FetchSelfTimeSchema,
  AddCollectionStoreSchema,
  CancelCollectionStoreSchema,
  GetCollectionStoreListSchema,
  GetConfigForStoredDiscountSchema,
  GetStoreInCampusSchema,
  GetWechatMchCodeSchema,
  CheckMerchantQualificationSchema,
  GetCollectionsIdListSchema,
  ConfirmTakeoutAgreementSchema,
  FetchMyOrderSchema,
  // #### response data
  GatherIndexRespSchema,
    ActivityRespSchema,
    AddAddressRespSchema,
    DelAddressRespSchema,
    GetAllAddressV2Schema,
    GetAllAddressV2RespSchema,
    GetDefaultAddressV2RespSchema,
    FetchDeliverFeeRespSchema,
    FetchAopContentNewSchema,
    FetchAopContentNewRespSchema,
    GetNearbyCampusListRespSchema,
    CartRespSchema,
    ClearCartRespSchema,
    SetCartSchema,
    SetCartRespSchema,
    RoundReduceCartSchema,
    RoundReduceCartRespSchema,
    GetCartSchema,
    GetCartRespSchema,
    FetchDiscountV2RespSchema,
    ReOrderRespSchema,
    AddAndRedeemBySpuIdsRespSchema,
    GetGatherDataRespSchema,
    GetGatherStoreListSchema,
    GoodsRespSchema,
    GetRecommendMaterialsByGoodsRespSchema,
    FetchGoodsV2RespSchema,
    OrdersRespSchema,
    GetTableOrderRespSchema,
    InitOrderV2RespSchema,
    GetRefundAmountRespSchema,
    GetRefundGoodsRespSchema,
    FetchPayloadRespSchema,
    FetchExtraRespSchema,
    MakeOrderAndPayForFistPayRoundRespSchema,
    GetStoreHBFQConfigRespSchema,
    GetStoresForIdsRespSchema,
    FetchMyOrderRespSchema,
    RoundAddGoodRespSchema,
    GoodsDetailRespSchema,
    AddOrderGoodsV2RespSchema,
    GetWhiteListRespSchema,
    PayForFistPayRoundOrderRespSchema,
    FetchRecommendRespSchema,
    UpdateUserCountRespSchema,
    GetStoreListRespSchema,
    GetGatherStoreListRespSchema,
    GetOrderOrBatchDetailRespSchema,
    GetCampusDetailRespSchema,
    GetCampusDetailByBizIdRespSchema,
    GetCampusCollectionRespSchema,
    // GetDefaultAddressRespSchema,
    FetchAddressLineRespSchema,
    FetchRiderLocationRespSchema,
    FetchDeliveryAreaRespSchema,
    FetchAOPResourceRespSchema,
    FetchAOPPopupsContentRespSchema,
    GetCampusIdByStoreIdRespSchema,
    RemoveRespSchema,
    RoundClearCartRespSchema,
    RoundGetCartRespSchema,
    GetEntranceRespSchema,
    AddAndRedeemBatchRespSchema,
    AddMaterialAndRedeemRespSchema,
    TakeoutTimeRespSchema,
    FetchLatestOrderRespSchema,
    CancelOrderRespSchema,
    CancelRefundRespSchema,
    GetOssPolicyRespSchema,
    GetRefundDetailRespSchema,
    FetchCashierRespSchema,
    PayCancelRespSchema,
    FetchStoreDetailRespSchema,
    GetStoreMultiConfigRespSchema,
    FetchSelfTimeRespSchema,
    AddCollectionStoreRespSchema,
    CancelCollectionStoreRespSchema,
    GetCollectionStoreListRespSchema,
    GetConfigForStoredDiscountRespSchema,
    GetStoreInCampusRespSchema,
    GetWechatMchCodeRespSchema,
    CheckMerchantQualificationRespSchema,
    GetLoveMerchantGrayRespSchema,
    GetCollectionsIdListRespSchema,
    GetShareMessageRespSchema,
    ConfirmTakeoutAgreementRespSchema,
    FetchUseStructuredAddressRespSchema
};

// 返回值通用类型
// const RespUniversalSchema = z.object({
//   data: z.any(),
//   code: z.number(),
//   message: z.string(),
// });

function generateTypeFileBatch(schemaNamesObject:Record<string,z.ZodTypeAny>){
  const importObj:Record<string,string> = {};
  for(let schemaName in schemaNamesObject){
    if(schemaNamesObject.hasOwnProperty(schemaName)){
      const typeSchemaName = 'I'+ schemaName.replace('Schema','');
      // 保存类型声明文件的名称，方便后续导入
      importObj[typeSchemaName] = typeSchemaName;
      // 修改返回值类型为通用类型
      if(schemaName.includes('RespSchema')){
        const RespUniversalSchema = z.object({
          data: schemaNamesObject[schemaName],
          code: z.number(),
          message: z.string().optional(),
        });
        schemaNamesObject[schemaName] = RespUniversalSchema;
      }
      
      generateTypeFile(schemaNamesObject[schemaName], typeSchemaName, `./@types/${typeSchemaName}.ts`);
    }
  }
  console.log(importObj);
};

generateTypeFileBatch(schemaNamesObject);
