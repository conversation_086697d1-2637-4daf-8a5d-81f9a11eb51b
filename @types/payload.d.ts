export type TActivity = {
  globalDiscount: null
  collectionDiscount: null
  timeDiscount: null
  priceBreak: null
  cardDiscounts: null
  returnCardDiscount: null
  goodsCoupons: []
  membershipCard: []
  combinedActivities: null
  storedCardActivity: null
  membershipActivities: null
  goodsCouponActivities: null
  membershipDays: null
  deliveryActivities: null
  secondActivities: null
  singleActivities: null
}

export type TGoodsItem = {
  id: string
  name: string
  photo_url: string
  description: string | null
  display_order: number
  valid: boolean | null
  category_id: string
  merchant_id: string
  store_id: string
  price: number
  activity_price: number | null
  second_activity_discount: number | null
  quota_count: number | null
  for_sale: boolean
  out_of_stock: boolean
  sku: string | null
  sku_default: string | null
  reset_sku: boolean
  unit: '份'
  unit_type: number
  category_name: string
  category_sort: number | null
  spu_type: 'PRODUCT'
  is_multiple: 2 | 1
  item_tag: number
  add_count: number | null
  last30_days_sale_count: number
  hotsale_seq: number
  hotsale_product: boolean
  keep_price_equal: boolean
  arrival_price: number
  takeout_price: number
  min_sale_num: number | null
  sale_times: number | null
  barcode: string | null
  sale_terminals: (1 | 2)[]
}

export type TWrapGoodsItemOptions = {
  id: string
  name: string
  price: number
  seq: number
  activity_price: number | null
  quota_count: number | null
  second_activity_discount: number | null
  arrival_price: number
  takeout_price: number
}

export type TWrapGoodsItemAttr = {
  id: string
  multiple: 0 | 1
  seq: number
  title: string
  must: 1 | 0
  options: { id: string; name: string; seq: number }[]
}

export type TWrapGoodsItemMaterial = {
  id: string
  name: string
  price: number
  seq: number
  status: 1 | 0
}

export type TWrapGoodsItem = {
  item: TGoodsItem
  specs: {
    title: string
    options: TWrapGoodsItemOptions[]
  }
  attributes: TWrapGoodsItemAttr[]
  material_ids: string[] | null
  materials: TWrapGoodsItemMaterial[]
  item_tags: null
  material_groups: {
    group_id: number
    name: string
    seq: 1
    materials: TWrapGoodsItemMaterial[]
  }[]
  ingredient_names: string | null
  package_must_order_products: unknown[] | null
  package_optional_groups: unknown[] | null
  bought_times: number | null
  latest_buy_time: number | null
}

export type page = {
  category_name: string
  category_id: string
  items: TWrapGoodsItem[]
}[]

export type TTerminal = {
  id: string
  name: string
  qr_code: string
  qr_print_code: string
  status: 1 | 0
  bind_source: 'APP'
  bind_by: string
  bind_time: number
  merchant_id: string
  store_id: string
  terminal_id: string
  ctime: number
  mtime: number
  deleted: boolean
  version: number
  qr_code_type: number
  qrcode_type: 'TABLE'
  qrcodeBusiness: string | null
  meal_type: 'single' | 'round_meal'
  jjz_business_type: 1
  tableId: number
  tableName: string
  seatCount: number
  areaId: number
  tableStatus: 'FREE'
  require_phone: boolean
  terminal_sn: string
}

export type TStore = {
  collectionNums: number | null
  firstIndustry: string
  firstIndustryCode: string
  industry: string
  industryCode: string
  merchantName: string
  merchantId: string
  merchantSn: string
  alipayStatus: number
  wechatStatus: number
  disabled: 0 | 1
  discounts: {
    globalDiscount: number | null
    collectionDiscount: number | null
    timeDiscount: unknown[]
    priceBreak: unknown | null
    priceBreaks: unknown[]
    priceBreakCards: unknown[]
    returnCards: unknown[]
    membershipCards: unknown[]
    singleActivity: 0 | 1
    secondDiscounts: unknown[]
  }
  discount: number | null
  organizationPath: string | null
  categoriesNames: string | null
  coupons: unknown[]
  storeId: string
  storeName: string
  storeExtraName: string
  contactPhone: string
  slogan: string | ''
  payWay: 1 | 3
  categoryId: string | null
  location: {
    lat: string
    lon: string
  }
  storeAddress: {
    province: string
    city: string
    district: string
    streetAddress: ''
    address: string
    aoiName: string | null
    houseNumber: string
    adcode: string | null
  }
  storePhoto: {
    innerPhotos: unknown[]
    outerPhotos: unknown[]
    goodsPhotos: unknown[]
    coverPhoto: string | null
    logoUrl: string | null
  }
  storeBasic: {
    averageConsumption: string | null
    storeArea: string | null
    businessHoursStart: string | ''
    businessHoursEnd: string | ''
  }
  onceOpened: boolean
  storeType: 0 | 1
  storeSn: string
}

export type TLogin = {
  token: string
  userId: string
  cellphone: string
  thirdpartyUserId: string
  unionId: string
  wid: string | null
  ctime: number
  updatedAt: number
  externalSource: string | ''
  userInfo: {
    refUserId: string
    type: 'WECHAT' | 'ALIPAY'
    typeId: string
    nickName: string
    gender: number
    avatarUrl: string
    province: string
    city: string
    country: string
  }
}

export type TMCC = {
  material_recommend: 1
  order_call_count: 2
  discount_payment: 1
  show_ordered_dish: 1
  must_order_enable: 0
  order_call_display_type: ['dine']
  table_qrcode_types: { idx: number; title: string; scale: number }[]
  dineway_config: { dineway: 'store'; dinewaylist: ['store', 'takeout'] }
  mobile_order_type: 'PRODUCT'
  store_must_category: string | ''
  takeout_pre_remind_time: number
  voice_subscribe: 1
  unbound_store_qrcode: string
  delivery_type: 1 | 2
  store_product_tag_use: 1 | 0
  business_status: 1 | 0
  store_takeout_bulletin: string
  preset_days: number[]
  preset_print_time: number
  order_require_phone: {
    all_area: boolean
    require_phone: boolean
    all_table_area: boolean
    store_qrcode: boolean
    non_qrcode: boolean
    dineway: 'store'
  }
  recomend_show_way: 'horizontal'
  manual_input_takeout_no: 0
  voice_takeout: 1
  refund_without_pwd: 1
  meal_type: 'single' | 'round_meal'
  store_qrcode_types: { idx: number; title: string; scale: number; detail: string }[]
  show_discount_category: number
  order_call_finish_duration: number
  voice_pre: number
  store_bulletin: string
  cashier_mode: 0 | 1
  preset_remind_time: number
  only_in_store: boolean
  scan_activated: boolean
  must_order_editable: 0 | 1
  cashier_auto_settle_after_pay: 1 | 0
  takeout_pack_type: 0 | 1
  love_dining: 0 | 1
  hot_sale_product_config: {
    autoGenerate: boolean
    maxShowNum: number
    openSaleProduct: boolean
    storeId: string
  }
  auto_clean_after_settle: number
  material_recommend_method: 1 | 0
  notify_material_config: 0 | 9
  takeout_pre_days: number[]
  delivery_distance: 0 | 1
  takeout_business_status: 0 | 1
  pack_type: 0 | 1
  order_call_display_duration: number
  voice_cashier: 0 | 1
  takeoutProfitSharing: boolean
  takeout_pre_print_time: number
  takeout_activated: boolean
  takeout_pre_status: 0 | 1
  package_image_mode: 0 | 1
}

export type TExtra = {
  fullPath: string
  qrCode: string
  merchantId: string
  page: string
  storeId: string
  scene: 't'
  timestamp: number
}

export type TServiceTypes = {
  currentServiceTypeIndex: 0 | 1 | 2
  serviceType: 2
  serviceTypeName: 'subscribe_order' | 'takeout'
  selectServiceTypeShow: boolean
  serviceTypeList: [
    {
      alias: string | null
      name: string
      serviceType: 2 | 1
      serviceTypeName: 'subscribe_order' | 'takeout'
      shortName: string
      active: boolean
      action: 'confirm' | 'scan'
    }
  ]
}

export type TCategory = {
  id: string
  store_id: string
  name: string
  item_count: number
  display_order: number
  items: TWrapGoodsItem[]
}

export type TPayload = {
  activity: TActivity
  goods: {
    total: number
    pages: page[]
  }
  terminal: TTerminal
  store: TStore
  login: TLogin
  mcc: TMCC
  duration: any
  extra: TExtra
  serviceTypes: TServiceTypes
  theme: any
  category: TCategory[]
  config: any
  mch_mcc: any
}
