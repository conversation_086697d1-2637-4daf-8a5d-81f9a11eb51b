import type { TGoodsItem, TStore, TTerminal } from './payload'
import { TPayway } from './order'
import { PhoneVerify } from '@utils'

export type TPackType = 'store' | 'takeout' | 'none'

type CamelCase<S extends string> = S extends `${infer P1}_${infer P2}${infer P3}`
  ? `${Lowercase<P1>}${Uppercase<P2>}${CamelCase<P3>}`
  : Lowercase<S>

type KeysToCamelCase<T> = {
  [K in keyof T as CamelCase<string & K>]: T[K] extends {} ? KeysToCamelCase<T[K]> : T[K]
}

export type TLoveDonateConfig = {
  loveDonateOptions: {
    openid: string
    brandId: number
    mchCode: string
    subMchCode: string
    storeId: string
  }
}

export type TSubmitManagerParams = {
  bridge: TBridge
  view: ISubmit
}

export type TMKSSStatusReturns = {
  isStoredPaySelected?: boolean
  showMkssStoredActivityInSubmit: any
  showStoredPay?: boolean
  showSodexoPay?: boolean
  isNativePay?: boolean
  payway?: TPayway
  paytype?: string
}

export type TStoreInAnyCampus = boolean
export type TMaterial = {
  id: string
  name: string
  price: number
  seq?: number
  status?: 1 | 0
  source: number
}
export type TRecommendMaterials = {
  id: string
  name: string
  number: number
  price: number
  selected: boolean
  source: number
}
export type TCartRecord = {
  attached_info: string
  category_id: string
  category_sort: null
  ctime: number
  display_order: null
  id: string
  item_id: string
  last_add: boolean
  last_add_num: number | null
  last_deal_type: null
  materials?: TMaterial[]
  min_sale_num: null
  mtime: number | null
  name: string
  num: number
  open_table_must_order: boolean
  open_table_must_order_editable: boolean
  out_of_stock: boolean
  price: number
  recommend_materials: TRecommendMaterials[]
  sku: null
  spec_id: string
  spu_type: string
  status: number
  url: string
  user_icon: string | null
  user_icons: string[] | null
  user_id: string | null
  user_name: string | null
}
export type TCart = {
  people_num: number
  sn: string | null
  total: number
  total_price: number
  version: number | null
  records: TCartRecord[]
  spu_count_map: { [key: string]: number }
}
export type TStoreInfo = {
  merchantSn: string
  storeSn: string
  wechatStatus: number
  storeType: number
  payWay: number
  merchantId: string
  location: { lat: number; lon: number }
  alipayStatus: number
  contactPhone: string
}

export type TBookTime = {
  label: string
  times: number[]
}

// 花呗分期
export type THbConfig = {
  vendor_id: string
  merchant_id: string
  merchant_sn: string
  merchant_name: string
  merchant_country: string
  currency: string
  longitude: string
  latitude: string
  district_code: string
  store_id: string
  store_client_sn: string | null
  store_name: string
  store_city: string
  clearance_provider: number
  pay_status: number
  common_switch: string
  merchant_daily_max_sum_of_trans: number
  merchant_daily_max_credit_limit_trans: null
  merchant_monthly_max_credit_limit_trans: null
  merchant_daily_payway_max_sum_of_trans: object
  merchant_single_max_of_tran: null
  alipay_huabei_status: number
  alipay_huabei_params: {
    hb_fq_seller_percent: string
    hb_fq_num: number
    seller_fee_rate: number
    buyer_fee_rate: number
  }[]
  up_direct_trade_params: { fee_rate: number }
}

export type TDiscount = {
  total_discount: number
  merchant_redeem_detail: {
    activity_id: null
    discount_amount: number
    activity_sn: null
    card_detail: null
    name: string
    message: string
    sub_type: number
    type: null
    original_type: null
    goods_details: null
  }
  system_redeem_detail: {
    activity_id: null
    discount_amount: number
    activity_sn: null
    card_detail: null
    name: null
    message: null
    sub_type: null
    type: null
    original_type: null
    goods_details: null
  }
  redeem_details: {
    activity_id: string
    discount_amount: number
    activity_sn: string
    card_detail: null
    name: string
    message: string
    sub_type: number
    type: number
    original_type: string
    goods_details: null
    gift_card_discount_detail: unknown
  }[]
  redeem_digest: string
}
/**
 * 下单页聚合接口错误
 */
// GET_CARTS_ERROR("E0101", "获取购物车失败"),
//   GET_PACK_AMOUNT_ERROR("E0102", "计算打包费失败"),
//   GET_BOOK_TIMES_ERROR("E0103", "获取预定时间失败"),
//   GET_CAMPUS_ERROR("E0104", "获取门店入驻校园信息失败"),
//   GET_AGREEMENT_ERROR("E0105", "获取签署外卖协议失败"),
//   GET_ALL_ADDRESS_ERROR("E0106", "获取所有地址失败"),
//   GET_DEFAULT_ADDRESS_ERROR("E0107", "获取默认地址失败"),
//   GET_DELIVERY_AMOUNT_ERROR("E0108", "计算配送费失败"),
//   GET_DISCOUNTS_AMOUNT_ERROR("E0109", "计算订单优惠失败"),

type TPayloadRespErrorCode =
  | 'E0101'
  | 'E0102'
  | 'E0103'
  | 'E0104'
  | 'E0105'
  | 'E0106'
  | 'E0107'
  | 'E0108'
  | 'E0109'

export type TPayloadResp = {
  cart: TCart
  inCampus: boolean
  packAmount: number
  deliveryAmount: number
  reductionAmount: number
  bookTimes: TBookTime[]
  addressList: TAddress[] | null
  defaultAddress: TAddress | null
  totalAmount: number
  discountAmount: number
  discounts: TDiscount
  errors?: { code: TPayloadRespErrorCode; message: string }[]
}

export type TExtraResp = {
  planContent: { records: any[]; total: number }
  // 微信商户号
  wxMchId: string
  // 微信子商户号
  wxSubMchId: string
  wxStoreId: string
  wxGoodsActs: string[]
  payConfig: THbConfig | null
  // 同意外卖协议
  isConfirmTakeoutAgreement: boolean
  wxProducts?: any[]
}

export type TTabBarData = {}
export type TAddress = {
  type: 1
  area_id: number
  address: string
  area_name: string | null
  cellphone: string
  campus_name: string | null
  city_code: string
  address_code: string
  building_number: string
  house_number: string
  is_default: boolean
  province: string
  city: string
  valid: boolean
  district: string
  delivery_fee: number | null
  province_code: string
  id: string
  user_id: string
  user_name: string
  district_code: string
  gender: number
  latitude: string
  address_name: string
  longitude: string
  distance: number
  invalid_reason: string | null
  campus_id: number
  address_id: number
  floors: any[]
  preChooseFloor: string
}
export type TCamelAddress = KeysToCamelCase<TAddress>
export type TServiceTypeName =
  | 'subscribe_order' /*店内点单*/
  | 'take_out_order' /*外卖订单*/
  | 'pre_order' /*自取订单*/
  | 'retail_order' /*零售订单*/
  | 'EAT_FIRST_ORDER' /*围餐*/
  | string
export type TServiceType = 1 | 2
export type TDeliverFee = {
  // 配送费
  originDeliveryFee: number
  // 配送费减免
  reductionAmount: number
  // 实际所需配送费
  realDeliveryFee: number
}
export type TSubmitPayload = {
  amountComposition?: any
  currentFloor: string
  deliveryFloorData: any[]
  errors?: TSubmitError[]
  storeId?: string
  distance?: number
  addressDisabled?: boolean
  addressName?: string
  addressCode?: string
  currentAddressId?: string
  cart?: TCart
  packAmount: number
  deliverFee?: TDeliverFee
  realDeliveryFee?: number
  // storeInfo: TStoreInfo;
  store: TStore
  serviceType: TServiceType
  serviceTypeName: TServiceTypeName
  isTakeOutOrder: boolean
  canEditPeopleNum: boolean
  tableName: string | null
  requirePhone: boolean
  isInCampus: boolean
  originCart?: TCart
  isSelfDelivery: boolean
  tabBarData: TTabBarData
  // selfTime: TSelfTime;
  defaultAddress?: TCamelAddress | null | {}
  addressList?: TCamelAddress[] | null
  // currentAddressId: string;
  bookTimes?: TBookTime[]
  cellphone?: string
  phoneNumber?: string
  packed?: boolean
  discount: TDiscount
  paidAmount: number
  packType: TPackType
  mealTypes?: {
    name: string
    disabledName: string
    icon: string
    icon_selected: string
    value?: TPackType
    disabled: boolean
    icon_style?: string
    style?: string
    style_selected?: boolean
  }[]
}
export type TSubmitExtra = {
  payConfig?: THbConfig
  adsense?: any
  loveDonateOptions?: {
    openid: string
    brandId: string
    mchCode: string
    subMchCode: string
  }
  goodsSubsidyInfo?: {
    actIdsToShow: any[]
    mchId: string
    storeId: string // subMchCode
    actIds: string[]
    products: any[]
  }
}
export type TBridge = {
  getPayload: (storeId: string) => Promise<TPayloadResp>
  getLoginInfo: () => { uc_user_id: string }
  requestPayment: (params?: any) => void
  getServiceTypes: <T extends 'serviceType' | 'serviceTypeName' | undefined>(
    params?: T
  ) => T extends 'serviceType' ? 1 | 2 : T extends 'serviceTypeName' ? TServiceTypeName : object
  getPhoneNumber: () => Promise<{ phoneNumber: string }>
  getTerminal: () => TTerminal
  getMcc: (key?: string) => boolean | string | object
  getStore: <T>(path?: T) => T extends string ? string : TStore
  onGetPhoneNumber: () => Promise<any>
  navigateTo: (params: any) => void
  getUser: () => any
  refreshTerminalInfo: () => void
  request: any
  sls: (topic: 'PERFORMANCE' | 'INFO' | 'REQUEST', data: object) => void
  getHillsStage: () => 'test' | 'release'
  isJJZ: boolean
  track: (...params: any[]) => void
  getMiniProgramEnv: () => void
  getPrestrainEnv: () => void
  getConfig: (key: string) => any
  getSupportCardPay: () => any
}

export type TView = {
  terminal: TTerminal
  store: TStore
  trackIns: TTrackIns
  data: {
    mealTypes: TSubmitPayload['mealTypes']
  }
}

export type TWxGoods = {
  number: number
  act_id: string
  qualification_id: string
  spu_id?: string
  act_goods_name?: string
  activityId?: string
  chosenNum?: number
  qualificationId?: string
  priceAfterSubsidy?: number
  originPrice?: number
}

export type TFetchPayloadParams = {
  packType?: 'store' | 'takeout' | 'none'
  packed?: boolean
  store_id?: string
  order_type?: TServiceTypeName
  table_id?: number
  discount_params?: {
    sn?: string
    payway?: number
    sub_payway?: number
    terminal_sn?: string
    recharge_and_pay?: boolean
    recharge_interest_id?: string | number
    wx_goods?: TWxGoods[]
  }
}
export type TFetchExtraParams = {
  store_sn?: string
  store_id?: string
  merchant_id?: string
  // terminal_code?: string;
  // field_code?: string;
  // field_style?: string;
  // client_version?: string;
}

export type TTrackIns = {
  send: (name: string, properties: object, identifer: string) => void
  clear: () => void
}
export type TSubmitError = { code: string; message: string }
// 收银台chanel
export type TPayChannel = {
  registerBeforeSubmitEvent: (fn: any) => void
  stopSubmit: () => () => void
  setSubmitButtonStatue: () => (params: { disabled: boolean }) => void
  handleSetPrepayRechargeAmount: (bridge: TBridge) => (params: any) => void
  requestPayment: (bridge: TBridge) => (params: any, cashierBizParams: object) => void
  on: (
    event:
      | 'onPayToolChange'
      | 'payToolsOf105'
      | 'setPrepayRechargeAmount'
      | 'onSetPrepaySuccess'
      | 'onPayPluginPrepayChange'
      | 'receiveAmount'
      | 'prepayAmountIsNotEnough'
      | 'submit' //  const {amount, payTool} = e.data
      | 'onPayFinish', // {payResult": "success"} 支付结果：success、cancel、fail
    cb: (event: object) => void
  ) => void
  off: (
    eventName:
      | 'onPayToolChange'
      | 'payToolsOf105'
      | 'setPrepayRechargeAmount'
      | 'onSetPrepaySuccess'
      | 'onPayPluginPrepayChange'
      | 'receiveAmount'
      | 'prepayAmountIsNotEnough'
      | 'submit' //  const {amount, payTool} = e.data
      | 'onPayFinish' // {payResult": "success"} 支付结果：success、cancel、fail
  ) => void
}

export type TSubmitData = {
  usingPayTools?: any[]
  currentFloor: string
  userDeliveryData: any
  deliveryFloorData: { floor: string; floorText: string; fee: number }[]
  supportCardPay?: { take_out_order: boolean; pre_order: boolean; subscribe_order: boolean }
  combined_payment?: {
    acquiring_amount: number
    acquiring_order: { payway: number; amount: number }
  }
  cashierPayTools?: any[]
  takeoutProfitSharing?: boolean
  componentName?: string
  floatingBubbleVisible: boolean
  storeMerchantInfo?: { storeId: string; merchantId: string; bizScene: string }
  wechatPayLuginThemeName?: string
  client_sn?: number
  cashierBizParams?: any
  useCashier?: boolean
  payChannel?: TPayChannel
  floatingBubbleData: { pic?: string }
  errors: TSubmitError[]
  cart: TCart | null
  originCart: TCart | null
  bookTimes: TBookTime[]
  defaultAddress: TCamelAddress
  selectTimeSheetShow: boolean
  selectAddressSheetShow: boolean
  timerIdexMap: any
  currentTime: string
  addressList: TCamelAddress[]
  currentAddressId: string | null
  goodsCouponCount: number
  discount: TDiscount | null
  packAmount: number
  serviceTypeName: string
  packed: boolean
  TAKE_OUT_ORDER: TServiceTypeName
  PRE_ORDER: TServiceTypeName
  RETAIL_ORDER: TServiceTypeName
  SUBSCRIBE_ORDER: TServiceTypeName
  isTakeOutOrder: boolean
  presetTimeDict: any
  storeConfigs: object
  tabBarData: TTabBarData
  isShowStoredCard: boolean
  realDeliveryFee: number | null
  isShowPriceChange: boolean
  // addressDisabled: true,
  // 是否是正在购买储值卡
  isPayingStoredCard: boolean
  paying: boolean
  phoneNumber: string | null
  isShowAlipayModal: boolean
  huabeiParams: any
  hbfqModalRef: any
  isDamBoard: any
  isInCampus: boolean
  supportCampusDelivery: boolean
  terminal: TTerminal | null
  canEditPeopleNum: boolean
  serviceType: TServiceType
  cellphone: string | null
  requirePhone: boolean // 预留手机号 是否必填
  isWeixin: boolean
  aliShouldAuthPhone: boolean
  // packType: 'none',
  isShowSelectTip: boolean
  loveDonateOptions: object | null
  orderno: string | null
  devCode: string
  campusType: number
  sign: boolean
  // showTakeOutSign: false,
  // 是否选中储值支付
  // isStoredPaySelected: false,
  // payway
  payway: TPayway
  // 支付方式
  paytype: 'WX' | 'MY' | 'stored' | 'sodexo'
  // 是否使用微信&支付宝原生支付
  isNativePay: boolean
  // 是否显示储值支付
  showMkssStoredActivityInSubmit: boolean
  showStoredPay: boolean
  // 是否显示索迪斯支付
  showSodexoPay: boolean
  // 支付需要储值充值
  isNeedStoredRecharge: boolean
  // 优惠前支付总金额（除配送费）
  originalPrice: number
  // 初始化数据是否加载完成
  loaded: boolean
  // 微信加价购参数
  goodsSubsidyInfo: object | null
  disabledStored: string
  invalidGoodsList: TGoodsItem[]
  showPhoneNumberDialog: boolean
  cancelAuthPhoneNumber: boolean
  // 结构化地址
  addressCode: string
  addressName: string
  // 运营位数据
  adsense: object | {}
  // banner 埋点数据
  bannerTrackParams: { placement: string }
  hasAgreement: boolean // 是否签署过外卖协议， 堂食后端返回true
  mealTypes: TSubmitPayload['mealTypes']
  sqbBridge?: TBridge
  config?: any
  remark?: string
  packType?: string
  addressDisabled?: boolean
  mounted?: boolean
  deliverFee?: TDeliverFee
  paidAmount?: number
  distance?: number
  // 储值规则 ID 用于储值并支付
  ruleId?: string | number
  // 权益 ID 可以用来计算 discount
  interestId?: string | number
  // 储值支付时余额是否不足
  isNoEnoughBalance?: boolean
  // 是否有储值规则
  hasStoredActivity?: boolean
  // 是否正在拉取优惠
  isFetchingDiscount?: boolean

  brandActivity?: Record<any, any> // 加价购活动
  isRetail?: boolean // 是否是零售店铺
}

// type TSubmitDateP = Partial<TSubmitData>;

type TAlert = {
  (msg: string, cb: () => void): void
  (msg: string, title: string, cb: () => void): void
}

export type TFetchCashierParamsParams = {
  store_id: string
  order_type: TServiceTypeName
  packed: boolean
}

export interface ISubmit {
  // store: TStore
  // terminal: TTerminal
  getStore: () => TStore
  getTerminal: () => TTerminal
  trackIns: TTrackIns
  data: TSubmitData
  alert: TAlert
  phoneVerify: PhoneVerify
  orderSvc?: any
  paySvc?: any
  interestId?: string | number
}
