export interface IGetGatherStoreList {
    esId: (string | number) | null;
    score?: unknown | null;
    size: number;
    distance: number;
    distanceFrom: number;
    geoType: string;
    sort: string;
    storeSetId: string;
    campusId: number;
    compatible?: boolean | undefined;
    store_id?: string | undefined;
    merchant_id?: string | undefined;
    merchantId?: string | undefined;
    storeId?: string | undefined;
}
