// #ifdef alipay
import { wComponent } from '@morjs/core'
// #endif
import _ from '@wosai/emenu-mini-lodash'
import { performance, render } from '@utils/mixins'
import { useBaseBehavior, IMethod, MergedMethods } from '@behaviors'
// #ifdef alipay
import type { MorComponentOptions } from '@morjs/core/lib/component'
import type { IData } from '@morjs/core/lib/types'
// #endif

// 定义通用方法接口
interface CommonMethods {
  // 添加其他通用方法...
}

// 扩展 MorComponentOptions 类型
type ExtendedComponentOptions<
  D extends IData,
  P extends IData,
  M extends IMethod
> = MorComponentOptions<D, P, M & CommonMethods> & {
  classes?: string[]
  // 这里可以添加其他自定义选项
}

export default function BaseComponent<
  D extends IData = IData,
  P extends IData = IData,
  M extends IMethod = IMethod
>(options: ExtendedComponentOptions<D, P, M>) {
  const baseExternalClasses: string[] = []
  // 合并 externalClasses
  const mergedExternalClasses = _.uniq([...baseExternalClasses, ...(options.externalClasses || [])])
  const extendedOptions: MorComponentOptions<D, P, MergedMethods<M & CommonMethods>> = {
    externalClasses: mergedExternalClasses,
    ...options,
    behaviors: [useBaseBehavior(), performance(), render(), ...(options.behaviors || [])],
    options: {
      // @ts-ignore
      externalClasses: true,
      // styleIsolation: 'apply-shared',
      virtualHost: false,
      pureDataPattern: /^\$/,
      ...(options.options || {})
    },
    methods: {
      ...options.methods
    } as MergedMethods<M & CommonMethods>
  }

  if (options.props) {
    _.assign(extendedOptions.properties, options.props)
    delete (extendedOptions as any).props
  }

  // 处理生命周期方法
  const lifetimes: Array<keyof MorComponentOptions<D, P, MergedMethods<M & CommonMethods>>> = [
    'created',
    'ready',
    'detached'
  ]

  lifetimes.forEach(lifetime => {
    const method = options[
      lifetime as keyof MorComponentOptions<D, P, MergedMethods<M & CommonMethods>>
    ] as ((...args: any[]) => any) | undefined
    if (method) {
      extendedOptions.lifetimes = extendedOptions.lifetimes || {}
      extendedOptions.lifetimes[lifetime] = method
      delete (extendedOptions as any)[lifetime]
    }
  })

  const wrapAttached = function (originalAttached: any) {
    return function (this: WechatMiniprogram.Component.TrivialInstance) {
      // console.log('base component attached')
      // @ts-ignore
      const sqbBridge = this.prop?.sqbBridge
      if (sqbBridge) {
        this.setData({ sqbBridge })
      }

      if (originalAttached) {
        originalAttached.call(this)
      }
    }
  }

  const originalAttached = options?.lifetimes?.attached
  options.lifetimes = options.lifetimes || {}
  options.lifetimes.attached = wrapAttached(originalAttached)

  if (options.classes) {
    extendedOptions.externalClasses = options.classes
    delete (extendedOptions as any).classes
  }

  return process.env.PLATFORM === 'wechat'
    ? Component(extendedOptions)
    : wComponent(extendedOptions)
}
