import { getPayway } from '@wosai/emenu-mini-utils'

export default function apply(orderSvc) {
  // ctx 为submit页面实例

  // address-sheet 实例
  // const node = ctx.selectComponent('#deliver-address');

  const handlePaywayChanged = async ({ payway, orderModel }, cb) => {
    // await view.storeSvc.fetchDeliverFee(address);

    let pay_way = getPayway()
    if (payway === 'stored') {
      pay_way = 101
    }
    if (payway === 105) {
      pay_way = 105
    }
    orderModel.setPayway(pay_way)
    cb(null, { ...orderModel.toBody() })
  }

  orderSvc.hooks.paywayChanged.tapAsync('paywayPlugin', handlePaywayChanged)
}
