export default function apply(orderSvc) {
  const { view } = orderSvc

  let supportActionType

  const defaultAction = {
    type: 'CONFIRM'
  }

  function validAction(action) {
    return action && action.type && supportActionType[action.type]
  }

  function invokeAction(_action, params, context) {
    let action = _action
    if (!validAction(action)) {
      action = defaultAction
    }

    supportActionType[action.type](action, params, context)
  }

  supportActionType = {
    CONFIRM({ title, message, ok, cancel, onOk, onCancel }, context) {
      view.confirm({
        title,
        content: message,
        ok,
        cancel,
        okCb() {
          // onOk
          view.closeConfirm()
          invokeAction(onOk, context)
        },
        cancelCb() {
          // onCancel
          view.closeConfirm()
          invokeAction(onCancel, context)
        }
      })
    },
    PAY(action, { pay }) {
      pay()
    },
    CLOSE_WINDOW(action, context) {
      context.closeWindow()
    },
    CLOSE({ onClose }, context) {
      view.closeConfirm()
      invokeAction(onClose, context)
    },
    OUT_LINK({ link }, context) {
      context.redirect(link)
    },
    RELOAD_PAGE(action, context) {
      context.reload()
    },
    NONE() {}
  }

  const handleBeforePay = (orderRes, order, cb) => {
    const { code, data } = orderRes
    const { has_risk } = data
    if (!has_risk) {
      cb(null, orderRes)
      return
    }

    const { action, riskId, clientSn } = data
    const newOrder = { ...order, riskId, clientSn }

    invokeAction(action, {
      closeWindow: () => {
        view.closeWindow()
        cb(new Error('block by risk plugin'))
      },
      reload: () => {
        view.reload()
        cb(new Error('block by risk plugin'))
      },
      pay() {
        orderSvc.toPay(data)
        // order(newOrder, (status, newOrderResOrError) => {
        //   if (status === 'success') {
        //     handleBeforePay(newOrderResOrError, newOrder, cb);
        //   } else {
        //     cb(new Error('block by risk plugin'));
        //     ctx.alert(newOrderResOrError.message, () => {
        //       ctx.closeAlert();
        //     });
        //   }
        // });
      },
      redirect(link) {
        view.redirect(link, { key: 'risk' })
        cb(new Error('block by risk plugin'))
      }
    })
  }

  orderSvc.hooks.beforePay.tapAsync('riskPlugin', handleBeforePay)
}
