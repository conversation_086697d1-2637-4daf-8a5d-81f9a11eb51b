/* 在线链接服务仅供平台体验和调试使用，平台不承诺服务的稳定性，企业客户需下载字体包自行发布使用并做好备份。 */
@font-face {
  font-family: 'iconfont'; /* Project id 2336711 */
  src: url('//at.alicdn.com/t/c/font_2336711_1f045duextt.woff2?t=1735193242609') format('woff2'),
    url('//at.alicdn.com/t/c/font_2336711_1f045duextt.woff?t=1735193242609') format('woff'),
    url('//at.alicdn.com/t/c/font_2336711_1f045duextt.ttf?t=1735193242609') format('truetype');
}

.smart-icon {
  font-family: 'iconfont' !important;
  position: relative;
  display: inline-flex;
  justify-content: center;
  align-items: center;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.sqb-icon-border {
  box-sizing: border-box;
  pointer-events: none;
  position: absolute;
  top: 50%;
  left: 50%;
  width: 100%;
  height: 100%;
  transform: translate(-50%, -50%);
}

.smart-icon-prefix {
  font-family: 'iconfont' !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  display: flex;
  justify-content: center;
  align-items: center;
}

.smart-icon-backspace:before {
  content: '\e6a7';
}

.smart-icon-red-packet:before {
  content: '\e6a6';
}

.smart-icon-minus:before {
  content: '\e697';
}

.smart-icon-minus-round:before {
  content: '\e60e';
}

.smart-icon-creditcard:before {
  content: '\e690';
}

.smart-icon-arrow:before {
  content: '\e691';
}

.smart-icon-wechat:before {
  content: '\e692';
}

.smart-icon-cart:before {
  content: '\e693';
}

.smart-icon-alipay:before {
  content: '\e694';
}

.smart-icon-note:before {
  content: var(--icon-content, '\e649');
}

.smart-icon-phone-brief:before {
  content: '\e696';
}

.smart-icon-search:before {
  content: var(--icon-content, '\e648');
}

.smart-icon-search-brief:before {
  content: '\e698';
}

.smart-icon-switch:before {
  content: '\e699';
}

.smart-icon-tick:before {
  content: '\e62b';
}

.smart-icon-tickMcDonald:before {
  content: '\e69a';
}

.smart-icon-trash:before {
  content: '\e69c';
}

.smart-icon-order-pending:before {
  content: '\e69d';
}

.smart-icon-order-cancel:before {
  content: '\e6a4';
}

.smart-icon-plus:before {
  content: '\e69e';
}

.smart-icon-plus-round:before {
  content: '\e60d';
}

.smart-icon-share:before {
  content: '\e69f';
}

.smart-icon-order-fail:before {
  content: '\e6a0';
}

.smart-icon-phone:before {
  content: '\e6a1';
}

.smart-icon-praise:before {
  content: '\e6a2';
}

.smart-icon-write:before {
  content: '\e6a3';
}

.smart-icon-cancel-order:before {
  content: '\e64c';
}

.smart-icon-del:before {
  content: '\e654';
}

.smart-icon-close:before {
  content: '\e656';
}

.smart-icon-yilingqu:before {
  content: '\e657';
}

.smart-icon-recommend:before {
  content: '\e601';
}

.smart-icon-hotSale:before {
  content: '\e602';
}

.smart-icon-arrow-line:before {
  content: '\e603';
}

.smart-icon-marker:before {
  content: '\e600';
}

.smart-icon-star:before {
  content: var(--icon-content, '\e647');
}

.smart-icon-star-fill:before {
  content: var(--icon-content, '\e64a');
}

.smart-icon-msg:before {
  content: '\e658';
}

.smart-icon-store:before {
  content: '\e629';
}
.smart-icon-store-612::before {
  content: '\e612';
}

.smart-icon-order:before {
  content: '\e78a';
}

.smart-icon-like:before {
  content: '\e813';
}

.smart-icon-coupon:before {
  content: '\e669';
}

.smart-icon-message:before {
  content: '\e63c';
}

.smart-icon-wechat-line:before {
  content: '\e604';
}

.smart-icon-logo:before {
  content: '\e66b';
}

.smart-icon-like-fill:before {
  content: '\e670';
}
.smart-icon-industry:before {
  content: '\e60b';
}

.smart-icon-check:before {
  content: '\e60a';
}

.smart-icon-medal:before {
  content: '\e605';
}

.smart-icon-locate:before {
  content: '\e607';
}

.smart-icon-clock:before {
  content: '\e608';
}

.smart-icon-distribution:before {
  content: '\e60c';
}

.smart-icon-love:before {
  content: '\e61d';
}
.smart-icon-love-line:before {
  content: '\e622';
}

.smart-icon-notice:before {
  content: '\e609';
}

.smart-icon-re-order:before {
  content: '\e64f';
}

.smart-icon-call-merchant:before {
  content: '\e64d';
}

.smart-icon-call-rider:before {
  content: '\e62c';
}

.smart-icon-contact-service:before {
  content: '\e634';
}

.smart-icon-point-line:before {
  content: '\e613';
}

.smart-icon-clock-line:before {
  content: '\e611';
}

.smart-icon-love-un:before {
  content: '\e62d;';
}

.smart-icon-question:before {
  content: '\e62f';
}

.smart-icon-close-circle:before {
  content: '\e616';
}

.smart-icon-weixin-circle:before {
  content: '\e614';
}
.smart-icon-lower-triangle::before {
  content: '\e642';
}
.smart-icon-home::before {
  content: '\e751';
}

.smart-icon-message:before {
  content: '\e63c';
}

.smart-icon-time-615:before {
  content: '\e615';
}

.smart-icon-address-614:before {
  content: '\e618';
}

.smart-icon-store-home:before {
  content: '\e612';
}

.smart-icon-firstpay-accept-failed:before {
  content: '\e61c';
}

.smart-icon-firstpay-payed-but-not-accepted:before {
  content: '\e61e';
}

.smart-icon-firstpay-canceled:before {
  content: '\e61b';
}
.smart-icon-wait-pay:before {
  content: '\e61f';
}

.smart-icon-apply-refund:before {
  content: '\e630';
}
.smart-icon-cancel-refund:before {
  content: '\e653';
}
.smart-icon-pay-order:before {
  content: '\e633';
}
.smart-icon-navigation:before {
  content: '\e631';
}
.smart-icon-upstairs:before {
  content: '\e628';
}
.smart-icon-downstairs:before {
  content: '\e627';
}
.smart-icon-pencil:before {
  content: '\e632';
}
