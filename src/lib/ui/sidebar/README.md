# Sidebar 侧边导航

## 介绍

垂直展示的导航栏，用于在不同的内容区域之间进行切换。

## 引入

需要在app.json中引入sidebar和sidebar-item组件：

```json
"usingComponents": {
  "smart-sidebar": "@wosai/smart-mp-ui/sidebar/index",
  "smart-sidebar-item": "@wosai/smart-mp-ui/sidebar-item/index"
}
```

## 代码演示

### 基础用法

通过在`smart-sidebar`上设置`active-key`属性来控制选中项，`smart-sidebar-item`的索引值从0开始。

```html
<smart-sidebar active-key="{{ activeKey }}" bind:change="onChange">
  <smart-sidebar-item title="标签名称" />
  <smart-sidebar-item title="标签名称" />
  <smart-sidebar-item title="标签名称" />
</smart-sidebar>
```

```javascript
Page({
  data: {
    activeKey: 0
  },

  onChange(event) {
    this.setData({
      activeKey: event.detail
    });
  }
});
```

### 徽标提示

设置`dot`属性后，会在右上角展示一个小红点；设置`badge`属性后，会在右上角展示相应的徽标。

```html
<smart-sidebar active-key="{{ activeKey }}">
  <smart-sidebar-item title="标签名称" dot />
  <smart-sidebar-item title="标签名称" badge="5" />
  <smart-sidebar-item title="标签名称" badge="99+" />
</smart-sidebar>
```

### 禁用选项

通过`disabled`属性禁用选项。

```html
<smart-sidebar active-key="{{ activeKey }}">
  <smart-sidebar-item title="标签名称" />
  <smart-sidebar-item title="标签名称" disabled />
  <smart-sidebar-item title="标签名称" />
</smart-sidebar>
```

### 自定义内容

可以在`smart-sidebar-item`内部嵌入自定义内容。

```html
<smart-sidebar active-key="{{ activeKey }}">
  <smart-sidebar-item>
    <slot name="title">
      <image src="xxx.jpg" style="width: 24px; height: 24px;" />
      <text>自定义内容</text>
    </slot>
  </smart-sidebar-item>
  <smart-sidebar-item title="标签名称" />
  <smart-sidebar-item title="标签名称" />
</smart-sidebar>
```

### 切换事件

设置`change`事件和`click`事件，在切换标签时触发。

```html
<smart-sidebar active-key="{{ activeKey }}" bind:change="onChange">
  <smart-sidebar-item title="标签1" bind:click="onItemClick" />
  <smart-sidebar-item title="标签2" bind:click="onItemClick" />
  <smart-sidebar-item title="标签3" bind:click="onItemClick" />
</smart-sidebar>
```

```javascript
Page({
  data: {
    activeKey: 0
  },

  onChange(event) {
    wx.showToast({
      title: `切换到标签 ${event.detail + 1}`,
      icon: 'none'
    });
  },

  onItemClick(event) {
    const index = event.detail;
    wx.showToast({
      title: `点击标签 ${index + 1}`,
      icon: 'none'
    });
  }
});
```

## API

### Sidebar Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| active-key | 选中项的索引 | *number* | `0` |

### Sidebar Events

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| bind:change | 切换菜单项时触发 | 当前选中项的索引 |

### Sidebar 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |

### SidebarItem Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| title | 内容 | *string* | - |
| dot | 是否显示右上角小红点 | *boolean* | `false` |
| badge | 图标右上角徽标的内容 | *string \| number* | - |
| info | 图标右上角徽标的内容（已废弃，请使用 badge 属性） | *string \| number* | - |
| disabled | 是否禁用该项 | *boolean* | `false` |

### SidebarItem Events

| 事件名 | 说明 | 参数 |
| --- | --- | --- |
| bind:click | 点击时触发 | 当前项的索引 |

### SidebarItem Slots

| 名称 | 说明 |
| --- | --- |
| title | 自定义标题 |
| 默认插槽 | 自定义内容 |

### SidebarItem 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |
| title-class | 标题样式类 |
| icon-class | 图标样式类 |

### css变量

组件提供了下列 CSS 变量，可用于自定义样式。

| 名称 | 默认值 | 描述 |
| --- | --- | --- |
| --sidebar-width | 80px | 侧边栏宽度 |
| --sidebar-font-size | 14px | 侧边栏字体大小 |
| --sidebar-line-height | 20px | 侧边栏行高 |
| --sidebar-text-color | #323233 | 侧边栏文字颜色 |
| --sidebar-padding | 20px 12px | 侧边栏内边距 |
| --sidebar-active-color | #f7f8fa | 侧边栏选中时的背景色 |
| --sidebar-background-color | #fff | 侧边栏背景色 |
| --sidebar-selected-text-color | #1989fa | 侧边栏选中项的文字颜色 |
| --sidebar-selected-font-weight | 500 | 侧边栏选中项的字体粗细 |
| --sidebar-selected-border-color | #1989fa | 侧边栏选中项的边框颜色 |
| --sidebar-selected-background-color | #fff | 侧边栏选中项的背景色 |
| --sidebar-disabled-text-color | #c8c9cc | 侧边栏禁用项的文字颜色 |
| --sidebar-item-border-radius | 16px | 侧边栏选中项边框圆角 

#### css变量使用方式
在业务组件或者页面组件的css样式中定义UI组件所提供的css变量的值，变量定义中经常使用var()函数进行嵌套，并提供回退值。然后通过外部定义的样式类（比如custom-class）应用到UI组件上，使得在业务组件或者页面组件中定义的CSS变量能够影响UI组件内部的样式。具体使用方式可参考如下案例：
```css
.submit-btn {
    --button-default-background-color: var(--smart-global-primary-theme__color, #fd6d05);
    --button-default-color: var(--smart-global-secondary-theme__color, #ffffff);
    --button-normal-font-size: 34px;
    --button-font-weight: 500;
  }
```
```html
<smart-ui-button catch:click="handleSubmit" round disabled="{{!isValid}}" custom-class="mt-40 submit-btn"></smart-ui-button>
```