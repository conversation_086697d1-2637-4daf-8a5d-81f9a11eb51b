# CollapseItem 折叠面板项

### 介绍

`CollapseItem` 为 `Collapse` 的子组件，用于展示单个可折叠的内容面板。

### 引入

```json
"usingComponents": {
  "smart-ui-collapse-item": "@wosai/smart-mp-ui/collapse-item/index"
}
```

## 代码演示

### 基础用法

`CollapseItem` 需要与 `Collapse` 组件配合使用，通过 `name` 属性来标识每一个面板的唯一性。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item title="标题1" name="1">
    内容1
  </smart-ui-collapse-item>
  <smart-ui-collapse-item title="标题2" name="2">
    内容2
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

```javascript
Page({
  data: {
    activeNames: ['1']
  },
  onChange(event) {
    this.setData({
      activeNames: event.detail
    });
  }
});
```

### 设置图标

通过 `icon` 属性可以设置标题栏左侧的图标。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item title="标题" name="1" icon="shop-o">
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

### 禁用状态

通过 `disabled` 属性可以禁用某个面板。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item title="正常面板" name="1">
    内容
  </smart-ui-collapse-item>
  <smart-ui-collapse-item title="禁用面板" name="2" disabled>
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

### 自定义标题内容

通过插槽可以自定义标题栏的内容。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item name="1">
    <view slot="title">
      <text>自定义标题</text>
      <smart-ui-icon name="question-o" />
    </view>
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

### 自定义右侧内容

通过 `value` 属性或 `value` 插槽可以设置或自定义标题栏右侧的内容。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <!-- 使用属性设置右侧内容 -->
  <smart-ui-collapse-item title="标题1" name="1" value="简要信息">
    内容
  </smart-ui-collapse-item>
  
  <!-- 使用插槽自定义右侧内容 -->
  <smart-ui-collapse-item title="标题2" name="2">
    <view slot="value">
      <text style="color: #ff5500;">¥128.00</text>
    </view>
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

### 设置标题大小

通过 `size` 属性可以设置标题栏的大小。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item title="标准大小" name="1">
    内容
  </smart-ui-collapse-item>
  <smart-ui-collapse-item title="大号标题" name="2" size="large">
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

### 添加标题描述信息

通过 `label` 属性可以添加标题下方的描述信息。

```html
<smart-ui-collapse value="{{ activeNames }}" bind:change="onChange">
  <smart-ui-collapse-item 
    title="标题" 
    name="1" 
    label="这里是描述信息"
  >
    内容
  </smart-ui-collapse-item>
</smart-ui-collapse>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| name | 唯一标识符，默认为索引值 | *string \| number* | `index` |
| title | 标题栏左侧内容 | *string \| number* | - |
| value | 标题栏右侧内容 | *string \| number* | - |
| icon | 标题栏左侧图标名称或图片链接，可选值见 Icon 组件 | *string* | - |
| size | 标题栏大小，可选值为 `large` | *string* | - |
| label | 标题栏描述信息 | *string* | - |
| border | 是否显示内边框 | *boolean* | `true` |
| is-link | 是否展示标题栏右侧箭头并开启点击反馈 | *boolean* | `true` |
| clickable | 是否开启点击反馈 | *boolean* | `false` |
| disabled | 是否禁用面板 | *boolean* | `false` |

### Slots

| 名称 | 说明 |
| --- | --- |
| - | 面板内容 |
| title | 自定义标题栏左侧内容，如果设置了 title 属性则不生效 |
| value | 自定义标题栏右侧内容，如果设置了 value 属性则不生效 |
| icon | 自定义标题栏左侧图标，如果设置了 icon 属性则不生效 |
| right-icon | 自定义标题栏右侧图标，会覆盖 is-link 设置的箭头 |

### 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |
| title-class | 标题样式类 |
| content-class | 内容样式类 |

### css变量

组件提供了以下 CSS 变量，可用于自定义样式。

| 名称 | 默认值 | 描述 |
| --- | --- | --- |
| --collapse-item-transition-duration | - | 折叠动画过渡时间 |
| --collapse-item-title-disabled-color | - | 禁用状态标题颜色 |
| --collapse-item-content-padding | - | 内容区域内边距 |
| --collapse-item-content-text-color | - | 内容区域文字颜色 |
| --collapse-item-content-font-size | - | 内容区域字体大小 |
| --collapse-item-content-line-height | - | 内容区域行高 |
| --collapse-item-content-background-color | - | 内容区域背景色 | 

#### css变量使用方式
在业务组件或者页面组件的css样式中定义UI组件所提供的css变量的值，变量定义中经常使用var()函数进行嵌套，并提供回退值。然后通过外部定义的样式类（比如custom-class）应用到UI组件上，使得在业务组件或者页面组件中定义的CSS变量能够影响UI组件内部的样式。具体使用方式可参考如下案例：
```css
.submit-btn {
    --button-default-background-color: var(--smart-global-primary-theme__color, #fd6d05);
    --button-default-color: var(--smart-global-secondary-theme__color, #ffffff);
    --button-normal-font-size: 34px;
    --button-font-weight: 500;
  }
```
```html
<smart-ui-button catch:click="handleSubmit" round disabled="{{!isValid}}" custom-class="mt-40 submit-btn"></smart-ui-button>
```

## 与父组件关系

`CollapseItem` 必须作为 `Collapse` 的子组件使用，不能单独使用。`Collapse` 组件通过 `value` 属性控制各个 `CollapseItem` 的展开状态：

- 非手风琴模式下，`value` 为数组格式，包含所有展开面板的 `name`
- 手风琴模式下，`value` 为字符串或数字，表示当前展开面板的 `name`

### 组件实现原理

1. `CollapseItem` 通过父子关系组件通信机制与 `Collapse` 联动
2. 点击 `CollapseItem` 时，会触发 `Collapse` 的 `switch` 方法更新展开状态
3. 通过 `animation` 动画控制面板的展开和收起效果

### 注意事项

1. 每个 `CollapseItem` 的 `name` 属性必须是唯一的，如果不指定则默认使用索引值
2. 在 `Collapse` 的 `accordion` 模式下，一次只能展开一个面板
3. 禁用状态下的面板无法通过点击展开或收起
4. 如果需要获取面板的展开/收起事件，请使用 `Collapse` 组件的 `bind:open` 和 `bind:close` 事件 