# GoodsAction 商品导航

### 介绍

用于为商品相关操作提供便捷交互，在商品详情页底部固定显示，用于展示购买按钮等常用操作。

### 引入

```json
"usingComponents": {
  "smart-ui-goods-action": "@wosai/smart-mp-ui/goods-action/index",
  "smart-ui-goods-action-icon": "@wosai/smart-mp-ui/goods-action-icon/index",
  "smart-ui-goods-action-button": "@wosai/smart-mp-ui/goods-action-button/index"
}
```

## 代码演示

### 基础用法

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" />
  <smart-ui-goods-action-button text="加入购物车" type="warning" />
  <smart-ui-goods-action-button text="立即购买" />
</smart-ui-goods-action>
```

### 徽标提示

在图标右上角显示徽标数字，可用于显示购物车里的数字或者消息提醒。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" dot />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" />
  <smart-ui-goods-action-icon icon="shop-o" text="店铺" />
  <smart-ui-goods-action-button text="加入购物车" type="warning" />
  <smart-ui-goods-action-button text="立即购买" />
</smart-ui-goods-action>
```

### 自定义按钮颜色

通过 `color` 属性可以自定义按钮的颜色。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" />
  <smart-ui-goods-action-button color="#be99ff" text="加入购物车" type="warning" />
  <smart-ui-goods-action-button color="#7232dd" text="立即购买" />
</smart-ui-goods-action>
```

### 朴素按钮

通过 `plain` 属性将按钮设置为朴素按钮，朴素按钮的文字为按钮颜色，背景为白色。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" />
  <smart-ui-goods-action-button plain color="#7232dd" text="加入购物车" type="warning" />
  <smart-ui-goods-action-button text="立即购买" />
</smart-ui-goods-action>
```

### 禁用状态

通过 `disabled` 属性来禁用按钮或图标，禁用状态下不会触发点击事件。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" disabled />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" />
  <smart-ui-goods-action-button text="加入购物车" type="warning" disabled />
  <smart-ui-goods-action-button text="立即购买" />
</smart-ui-goods-action>
```

### 加载状态

通过 `loading` 属性设置按钮为加载状态，加载状态下不会触发点击事件。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon icon="shop-o" text="店铺" />
  <smart-ui-goods-action-button text="加入购物车" type="warning" loading />
  <smart-ui-goods-action-button text="立即购买" loading />
</smart-ui-goods-action>
```

### 自定义图标

将图标名称直接传入 icon 属性，如果需要使用自定义图标，请参考 Icon 组件。也可以使用 icon 插槽进行自定义。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon text="自定义">
    <smart-ui-icon slot="icon" name="shop-o" />
  </smart-ui-goods-action-icon>
  <smart-ui-goods-action-button text="加入购物车" type="warning" />
  <smart-ui-goods-action-button text="立即购买" />
</smart-ui-goods-action>
```

### 按钮链接

通过 `url` 属性可以实现跳转至指定页面，通过 `link-type` 属性控制跳转方式。

```html
<smart-ui-goods-action>
  <smart-ui-goods-action-icon icon="chat-o" text="客服" />
  <smart-ui-goods-action-icon icon="cart-o" text="购物车" info="5" url="/pages/cart/index" link-type="switchTab" />
  <smart-ui-goods-action-button text="加入购物车" type="warning" url="/pages/cart/index" link-type="switchTab" />
  <smart-ui-goods-action-button text="立即购买" url="/pages/goods/purchase/index" />
</smart-ui-goods-action>
```

## API

### GoodsAction Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| safe-area-inset-bottom | 是否为 iPhoneX 留出底部安全距离 | *boolean* | `true` |

### GoodsActionIcon Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 按钮文字 | *string* | - |
| icon | 图标 | *string* | - |
| info | 图标右上角徽标的内容 | *string \| number* | - |
| dot | 是否显示图标右上角小红点 | *boolean* | `false` |
| size | 图标大小，如 `20px`，`2em` | *string* | - |
| color | 图标颜色 | *string* | - |
| class-prefix | 图标类名前缀 | *string* | `smart-icon` |
| url | 点击后跳转的链接地址 | *string* | - |
| link-type | 链接跳转类型，可选值为 `navigateTo` `redirectTo` `switchTab` `reLaunch` | *string* | `navigateTo` |
| id | 标识符 | *string* | - |
| disabled | 是否禁用按钮 | *boolean* | `false` |
| loading | 是否显示为加载状态 | *boolean* | `false` |
| open-type | 微信开放能力，具体支持可参考 [微信官方文档](https://developers.weixin.qq.com/miniprogram/dev/component/button.html) | *string* | - |
| app-parameter | 打开 APP 时，向 APP 传递的参数 | *string* | - |
| lang | 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文 | *string* | `en` |
| session-from | 会话来源 | *string* | - |
| send-message-title | 会话内消息卡片标题 | *string* | 当前标题 |
| send-message-path | 会话内消息卡片点击跳转小程序路径 | *string* | 当前分享路径 |
| send-message-img | sendMessageImg | *string* | 截图 |
| show-message-card | 显示会话内消息卡片 | *string* | `false` |

### GoodsActionButton Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| text | 按钮文字 | *string* | - |
| type | 按钮类型，可选值为 `primary` `success` `warning` `danger` | *string* | `danger` |
| size | 按钮尺寸，可选值为 `normal` `large` `small` `mini` | *string* | `normal` |
| color | 按钮颜色，支持传入 `linear-gradient` 渐变色 | *string* | - |
| plain | 是否为朴素按钮 | *boolean* | `false` |
| url | 点击后跳转的链接地址 | *string* | - |
| link-type | 链接跳转类型，可选值为 `navigateTo` `redirectTo` `switchTab` `reLaunch` | *string* | `navigateTo` |
| id | 标识符 | *string* | - |
| disabled | 是否禁用按钮 | *boolean* | `false` |
| loading | 是否显示为加载状态 | *boolean* | `false` |
| open-type | 微信开放能力，具体支持可参考 [微信官方文档](https://developers.weixin.qq.com/miniprogram/dev/component/button.html) | *string* | - |
| app-parameter | 打开 APP 时，向 APP 传递的参数 | *string* | - |
| lang | 指定返回用户信息的语言，zh_CN 简体中文，zh_TW 繁体中文，en 英文 | *string* | `en` |
| session-from | 会话来源 | *string* | - |
| send-message-title | 会话内消息卡片标题 | *string* | 当前标题 |
| send-message-path | 会话内消息卡片点击跳转小程序路径 | *string* | 当前分享路径 |
| send-message-img | sendMessageImg | *string* | 截图 |
| show-message-card | 显示会话内消息卡片 | *string* | `false` |
| custom-style | 自定义样式 | *string* | - |

### GoodsActionIcon Slot

| 名称 | 说明 |
| --- | --- |
| icon | 自定义图标 |

### GoodsActionIcon 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |
| icon-class | 图标样式类 |
| text-class | 文字样式类 |
| info-class | 徽标样式类 |

### GoodsActionButton 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |

### GoodsAction 外部样式类

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |

### CSS 变量

组件提供了下列 CSS 变量，可用于自定义样式。

#### GoodsAction CSS 变量

| 名称 | 说明 | 默认值 |
| --- | --- | --- |
| --goods-action-height | 商品导航高度 | `100rpx` |
| --goods-action-background-color | 商品导航背景颜色 | `#fff` |

#### GoodsActionButton CSS 变量

| 名称 | 说明 | 默认值 |
| --- | --- | --- |
| --goods-action-button-height | 按钮高度 | `80rpx` |

#### GoodsActionIcon CSS 变量

| 名称 | 说明 | 默认值 |
| --- | --- | --- |
| --goods-action-icon-width | 图标宽度 | `100rpx` |
| --goods-action-icon-text-color | 图标文字颜色 | `#646566` |
| --goods-action-icon-font-size | 图标文字大小 | `24rpx` | 