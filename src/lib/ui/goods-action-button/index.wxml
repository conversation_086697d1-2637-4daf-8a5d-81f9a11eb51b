<wxs src="../wxs/ui-utils.wxs" module="utils" />
<smart-button
  id="{{ id }}"
  button-id="{{ buttonId }}"
  lang="{{ lang }}"
  type="{{ type }}"
  size="{{ size }}"
  color="{{ color }}"
  plain="{{ plain }}"
  loading="{{ loading }}"
  disabled="{{ disabled }}"
  open-type="{{ openType }}"
  class="{{ utils.bem('goods-action-button', [type, { first: isFirst, last: isLast, plain: plain }])}}"
  custom-class="smart-goods-action-button__inner custom-class {{ customClass }}"
  custom-style="{{customStyle}}"
  business-id="{{ businessId }}"
  session-from="{{ sessionFrom }}"
  app-parameter="{{ appParameter }}"
  send-message-img="{{ sendMessageImg }}"
  send-message-path="{{ sendMessagePath }}"
  show-message-card="{{ showMessageCard }}"
  send-message-title="{{ sendMessageTitle }}"
  bind:click="onClick"
  binderror="onError"
  bindcontact="onContact"
  bindopensetting="onOpenSetting"
  bindgetuserinfo="onGetUserInfo"
  bindagreeprivacyauthorization="onAgreePrivacyAuthorization"
  bindgetRealTimePhoneNumber="onGetRealTimePhoneNumber"
  bindgetphonenumber="onGetPhoneNumber"
  bindlaunchapp="onLaunchApp"
>
  {{ text }}
  <slot></slot>
</smart-button>
