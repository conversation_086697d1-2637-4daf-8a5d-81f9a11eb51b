import _ from '@wosai/emenu-mini-lodash'
import Logger from './logger'
import Options from './options'
import RequestWorker from './requestWorker'
import Task from './task'
import { isDebug } from './utils'

const __instance = null
const __caches = {}

const composePromise = functions => initialValue =>
  functions.reduce((options, fn) => fn(options), initialValue)

const getCurrentPages = () => {
  return '/page/home'
}

class RequestFailedError extends Error {
  constructor(message) {
    super(message)
    this.name = 'RequestFailedError'
  }
}

// TODO:
// 如果要适应其他框架， 应该重写 getCurrentPages
const getRoute = (index = -1) => _.nth(getCurrentPages(), index)
const getCurrentPath = (route = getRoute()) =>
  `/${_.get(route, 'route') || _.get(route, '$component.$router.path')}`.replace(/\/+/g, '/')

const defaultSuccessHandlers = [
  function handleSuccess(response, task) {
    const { data } = response
    let { status, code = status, includes, data: _data } = data || {}
    if ([200, 10000].includes(Number(code))) {
      let res = data
      res = _.isPlainObject(includes) ? { ...res, includes } : res
      // 由于营销saas组相关服务数据结构与其他服务不一致，所以这里做了兼容
      // 可能会出现其他问题，比如：
      // 如果 _data 中有 code、status 字段，会覆盖掉 res 中的 Code、status 字段
      res = _.isPlainObject(_data) ? { ...res, ..._data } : res
      task.resolve(res)
    }
    return response
  },
  function toCache(response, task) {
    const { data } = response
    let { status, code = status, data: result } = data || {}
    if ([200].includes(Number(code))) {
      if (this.options.isCache) __caches[task.options.url] = result
    }
    return response
  },
  function toRemoveTask(response, task) {
    const idx = _.findIndex(this.tasks, ({ tid }) => tid === task.tid)
    if (idx !== -1) {
      this.tasks.splice(idx, 1)
    }
    return response
  },
  function toLog(response, task) {
    let { status, code = status } = response.data || {}
    const { method: _method, url: _url } = task.options
    const _code = Number(code)
    if ([200].includes(_code)) {
      this.info(`Request[${_method}]: ${_url}`, {
        config: task.options,
        responseData: response.data
      })
    }
    if (![401, 40001, 200].includes(_code)) {
      this.error(`Request[${_method}]: ${_url}`, {
        config: task.options,
        responseData: response.data,
        path: getCurrentPath()
      })
    }

    this.info(_url, { config: task.options, responseData: response.data })
    return response
  }
]

const defaultErrorHandlers = [
  function handleError(error, task) {
    // Taro.request 默认会toJson response， 如果解析出错，会reject
    if (error && error.status && error.status === 200) {
      return error
    }
    this.error('@handleError', error, task.options)

    const message =
      _.get(error, 'message') || _.get(error, 'errorMessage') || _.get(error, 'errMsg')
    _.merge(error, { message })
    // throw new Error(message)
    throw new RequestFailedError(message)
    // return new RequestFailedError(message)
  }
]

const beforeHandlers = []

/**
 * @typedef {object} Request
 * @property {function} addSuccessHandler
 * @property {function} error
 * @property {function} info
 * @property {function} isDebug
 * @property {function} addSuccessHandler
 * @property {function} addErrorHandler
 * @property {function} addBeforeHandler
 */
class Request {
  config = {}
  _successHandlers = []
  _errorHandlers = []
  _beforeHandlers = []
  isRetry = false
  isCacheErrorRequest = true
  /**
   * 重试配置
   * @type {{times: number, delayCoefficient: number, delay: number}}
   */
  retryConfig = {
    delay: 100,
    times: 5,
    delayCoefficient: 2,
    ignore: false
  }
  /**
   * 请求参数
   * @type {{}}
   */
  options = {}
  /**
   * tasks
   * @type {{retryTimes: number, resolve: function, reject:function, setRetryTimes: function }[] }
   */
  tasks = []
  ignoreUrls = []

  constructor(config = {}) {
    if (__instance) throw new Error("Request can't be instantiated more than once")
    this.config = config
    defaultSuccessHandlers.forEach(handler => {
      this.addSuccessHandler(handler.bind(this))
    })
    defaultErrorHandlers.forEach(handler => {
      this.addErrorHandler(handler.bind(this))
    })

    beforeHandlers.forEach(handler => {
      this.addErrorHandler(handler.bind(this))
    })

    _.has(config, 'retry') && (this.isRetry = config.retry)
    _.has(config, 'retryConfig') &&
      (this.retryConfig = { ...this.retryConfig, ...config.retryConfig })
    _.has(config, 'cacheErrorRequest') && (this.isCacheErrorRequest = config.cacheErrorRequest)

    // 如果缓存错误请求，则需要把相应task加入任务队列
    if (this.isCacheErrorRequest) {
      const cacheErrorRequestTask = (error, task) => {
        this.addTask(task)
        throw error
      }
      this.addErrorHandler(cacheErrorRequestTask)
    }

    this.worker =
      RequestWorker &&
      RequestWorker.create({
        beforeHandlers: this._beforeHandlers,
        successHandlers: this._successHandlers,
        errorHandlers: this._errorHandlers,
        config
      })
  }

  get request() {
    // eslint-disable-next-line @typescript-eslint/no-this-alias
    const self = this

    return function (...params) {
      _.has(self.config, 'getIgnoreUrls') &&
        _.isFunction(self.config.getIgnoreUrls) &&
        (self.ignoreUrls = self.config.getIgnoreUrls())

      // eslint-disable-next-line no-async-promise-executor
      return new Promise(async (resolve, reject) => {
        self.options = Options.create(params, self.config)
        // 忽略不必要的请求
        if (self.ignoreUrls.length > 0 && _.includes(self.ignoreUrls, self.options.pathname)) {
          return resolve()
        }
        // 有cache而且是开启cache，则直接返回数据
        if (self.options.isCache && __caches[self.options.url]) {
          return resolve(__caches[self.options.url])
        }

        // 处理请求参数
        let options = self.options.toParams()
        options = composePromise(self._beforeHandlers)(options)
        if (self.config && typeof self.config.getDefaultOptions === 'function') {
          _.merge(options, self.config.getDefaultOptions(this))
        }

        const task = Task.create(options, resolve, reject, self)
        task.setRetryTimes(self.retryConfig.times)
        task.setIgnoreRetry(self.isIgnoreRetry(task))
        // 执行请求
        try {
          const result = await self.worker.run(task)
          if (result instanceof Error) {
            if (_.includes(_.get(task, 'options.url'), 'apply/cancel')) {
              task.rejected = true
              task.reject(new RequestFailedError('网络超时，请稍后再试'))
            } else {
              throw result
            }
          }
        } catch (err) {
          // 重试
          if (self.isRetry && self.config.retryStrategy(err) && !task.ignoreRetry) {
            self.worker
              .poll(task, self.retryConfig)
              .then(result => {
                console.log('poll result', result)
                // task.ignoreRetry = true;
              })
              .catch(err => {
                // task.ignoreRetry = true;
                console.error(err)
              })
          }
        }
      })
    }
  }

  /**
   * 创建request
   *   cacheErrorRequest:boolean,
   *   retry: boolean,
   *   retryConfig: {delay: number, times: number,delayCoefficient: number}
   *   retryStrategy: function,
   *   getBaseUrl: function,
   *   getDebug: function,
   *   isDebug: function | boolean,
   *   getDistance: function,
   *   getIgnoreUrls: function,
   *   getDefaultOptions: function
   * }} config
   * @return {null|Request}
   * @param config
   */
  static create(config) {
    if (__instance) return __instance
    return new Request(config)
  }

  addSuccessHandler(handler) {
    const idx = this._successHandlers.findIndex(_handler => _handler === handler)
    if (idx === -1) {
      this._successHandlers.unshift(handler)
    }
    return this
  }

  addErrorHandler(handler) {
    const idx = this._errorHandlers.findIndex(_handler => _handler === handler)
    if (idx === -1) {
      this._errorHandlers.unshift(handler)
    }
    return this
  }

  addBeforeHandler(handler) {
    const idx = this._beforeHandlers.findIndex(_handler => _handler === handler)
    if (idx === -1) {
      this._beforeHandlers.unshift(handler)
    }
    return this
  }

  /**
   * 重新请求失败的请求
   * @returns {Promise<void>}
   */
  async runFailedTasks() {
    this.info(' start runFailedTasks', this.tasks)
    for (const task of this.tasks) {
      task.setRetryTimes(1)
      await this.worker.run(task)
    }
  }

  isTaskExist(task) {
    return _.findIndex(this.tasks, ({ tid }) => tid === task.tid) !== -1
  }

  isIgnoreRetry(task) {
    return this.retryConfig && this.retryConfig.ignore && this.retryConfig.ignore(task)
  }

  addTask(task) {
    if (task && task.ignoreRetry) return this
    if (!this.isTaskExist(task)) {
      this.info('@addTask', task, this.tasks)
      this.tasks.push(task)
    }
    return this
  }
}

Object.assign(Request.prototype, {
  ...Logger,
  isDebug
})

export default Request
