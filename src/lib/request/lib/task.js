/**
 * 生成task的uuid
 * @return {string}
 */

const guid = () => {
  function S4() {
    return (((1 + Math.random()) * 0x10000) | 0).toString(16).substring(1)
  }
  return S4() + S4() + '-' + S4() + '-' + S4() + '-' + S4() + '-' + S4() + S4() + S4()
}

class Task {
  isCache = false
  ignoreRetry = false
  isRetrying = false
  startAt = null
  endAt = null

  constructor(options) {
    this.options = options
    this.resolve = null
    this.reject = null
    this.retryTimes = 5
  }

  static create(options, resolve, reject, owner) {
    const instance = new Task(options)
    instance.resolve = resolve
    instance.reject = reject
    instance.tid = guid()
    instance.startAt = Date.now()
    instance.owner = owner

    return instance
  }

  setIgnoreRetry(value) {
    this.ignoreRetry = value
  }

  setRetryTimes(time) {
    this.retryTimes = time
  }

  setIsRetrying(value) {
    this.isRetrying = value
  }

  getRequest() {
    return this.owner
  }
}

export default Task
