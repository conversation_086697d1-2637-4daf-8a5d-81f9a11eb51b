var statusWrap
var mapWrap
var statusText
var divisionLine
var statusWrapBlock

var onScroll = function(e, ins) {
  var scrollTop = e.detail.scrollTop
  var instance = ins.selectComponent('.meal-order-details-wrap')
  var dataset = instance.getDataset()
  var transformPosition = dataset.height
  var statusHeight = dataset.statusheight
  var threshold = transformPosition - statusHeight
  var isScroll = dataset.isScroll
  if (!isScroll) return
  if (scrollTop > threshold) {
    statusWrap = ins.selectComponent('.status-wrap')
    mapWrap = ins.selectComponent('.order-detail-map')
    statusText = ins.selectComponent('.result-main-status')
    divisionLine = ins.selectComponent('.division-line')
    statusWrapBlock = ins.selectComponent('.status-wrap-block')
    var translate = scrollTop - threshold
    var translateY = -translate + 'px'
    var fontSize = Math.min(44, parseInt(44 * (translate / statusHeight))) + 'rpx'
    var top = Math.min(10, translate - 15) + 'rpx'
    mapWrap.setStyle({ transform: 'translateY(' + translateY + ')' })
    statusWrap.setStyle({ opacity: Math.min(1, translate / statusHeight) })
    if (translate >= statusHeight) {
      statusWrap.setStyle({ position: 'fixed', opacity: 1 })
      statusWrapBlock.setStyle({ display: 'block' })
    }else{
      statusWrapBlock.setStyle({ display: 'none' })
    }
    statusText.setStyle({ 'font-size': fontSize })
    divisionLine.setStyle({ top: top })
  } else {
    // console.log('statusWrap,,,,statusWrap', statusWrap, mapWrap)
    if (!statusWrap || !mapWrap) return
    statusWrap.setStyle({ opacity: 0,position:'sticky' })
    mapWrap.setStyle({ transform: 'translateY(0)' })
    divisionLine.setStyle({ top: '-15rpx' })

  }
}
module.exports = {
  onScroll: onScroll,
}
