import _ from '@wosai/emenu-mini-lodash'
import BaseComponent from '@BaseComponent'
import { isAuthorized, showToast } from '@utils'

const locationImage = 'https://smart-static.wosaimg.com/99zhe/authorizationLocationIcon.png'

BaseComponent({
  properties: {
    sqbBridge: {
      type: Object,
      value: {}
    },
    show: {
      type: Boolean,
      value: false
    },
    scene: {
      type: String,
      value: ''
    }
  },
  data: {
    msg: '',
    operate: '',
    isManual: false,
    location: null,
    locationImage
  },

  observers: {
    async 'show,scene'(show, scene) {
      if (show) {
        const locationMap = await this.handleLocationMap({ scene })
        const target = _.find(locationMap, { isOpen: true }) || {}
        const { isOpen, msg, operate, onFuc, isManual } = target
        this.onTap = onFuc
        this.setData({ isOpen, msg, operate, isManual })
      }
    }
  },

  methods: {
    async handleLocationMap(payload) {
      try {
        const { onOpenSetting } = this
        const platformLocationEnabled = await isAuthorized('userLocation', {
          userLocationStrict: true
        })

        return [
          {
            isOpen: payload.scene === 'PRIVACY_REFUSE',
            msg: '未获取到您的定位信息，无法为您推荐附近店铺',
            isManual: true
          },
          {
            isOpen: !platformLocationEnabled,
            msg: '未获取到您的定位信息，无法为您推荐附近店铺',
            operate: '开启定位',
            onFuc: onOpenSetting.bind(this),
            isManual: true
          }
        ]
      } catch (error) {
        console.error('error_____error', error)
      }
    },
    async onOpenSetting() {
      const authSetting = await wx.openSetting()
      this.$emit('authorizeChange', authSetting)
    },

    onClose() {
      this.$emit('close')
    },

    onFuc() {
      if (_.isFunction(this.onTap)) this.onTap()
    },
    async onManual() {
      const { sqbBridge } = this.data
      const _getLoc = async () => {
        try {
          const location = await wx.chooseLocation()
          sqbBridge.setProvisionalLocation(location)
          sqbBridge.setMockLocation(location)
          this.onClose()
        } catch (error) {
          console.log(`##error is `, error)
          showToast('未获取位置信息')
        }
      }
      _getLoc()
    }
  }
})
