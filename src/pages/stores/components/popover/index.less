@import (css) '@styles/common';
@import (css) '@styles/flex';
.ad {
  &-box {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }
  &-close {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 4px solid #ffffff;
    box-sizing: border-box;
    display: flex;
    justify-content: center;
    align-items: center;
    &-hover {
      cursor: pointer;
      opacity: 0.7;
    }
    &.top {
      /*@minipack-ignore*/
      top: -100px;
      left: 50%;
      transform: translateX(-50%);
    }
    &.left-top {
      /*@minipack-ignore*/
      top: -100px;
      left: 0px;
    }
    &.right-top {
      /*@minipack-ignore*/
      top: -100px;
      right: 0px;
    }
    &.left {
      /*@minipack-ignore*/
      left: -100px;
      top: 50%;
      transform: translateY(-50%);
    }
    &.right {
      /*@minipack-ignore*/
      right: -100px;
      top: 50%;
      transform: translateY(-50%);
    }
    &.bottom {
      /*@minipack-ignore*/
      bottom: -100px;
      left: 50%;
      transform: translateX(-50%);
    }
    &.left-bottom {
      /*@minipack-ignore*/
      bottom: -100px;
      left: 0px;
    }
    &.right-bottom {
      /*@minipack-ignore*/
      bottom: -100px;
      right: 0px;
    }
  }
  &-content {
    z-index: 1100;
  }
}
