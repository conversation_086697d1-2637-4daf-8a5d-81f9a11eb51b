<wxs src="@wxs/utils.wxs" module="utils" />
<wxs src="@wxs/toCSSVars.wxs" module="computed" />

<view class="smart-page">
  <smart-main-provider id="main-provider" full-height custom-class="h-screen">
    <smart-i18n-provider id="smart-i18n-provider" sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}">
      <smart-full-loading show="{{status === 'loading'}}" sqbBridge="{{sqbBridge}}" errors="{{loadingErrors}}" />
      <scroll-view class="smart-store-home w-full bg-f6 box-border h-screen" enhanced show-scrollbar="{{false}}" scroll-y bindscroll="onPageScroll" style="{{computed.toCSSVars(theme.style)}}">
        <view
          wx:if="{{ status === 'ready' }}"
          class="bg-content box-border {{shouldBrandExpose ? 'brand-bg-content' : ''}}"
          style="padding-bottom:calc({{shouldBrandExpose?(from === 'tab' ? 'var(--safe-bottom) * 2 + 116rpx' : brandStyle.bottom !== '0' ? 'var(--safe-bottom) + 166rpx' : '166rpx'):'var(--safe-bottom) + 20rpx'}})"
        >
          <smart-ui-nav-bar
            id="smart-custom-nav-bar"
            custom-class="nav-bar {{showDefaultNav ? 'smart-nav-bar--pseudo':''}} {{showDefaultNav || isBlackNav ? 'smart-nav-bar--black':''}}"
            left-class="nav-bar-left"
            title-class="nav-title-class"
            leftArrow="{{from === 'tab'? false : true}}"
            placeholder="{{true}}"
            safeAreaInsetTop="{{true}}"
            zIndex="{{1000}}"
            catch:click-left="onLeftClick"
          >
            <view wx:if="{{from === 'tab'}}" slot="left" left-class="{{showDefaultNav || isBlackNav ? 'smart-nav-bar--black':''}}">
              <select-store-entry wx:if="{{isMerchantMp}}" custom-class="select-store-entry" mpBridge="{{mpBridge}}" store="{{store}}" generic:auth-login="auth-login" />
              <view wx:else class="text-30 ml-20 fw-500">{{utils.i18n.t('最近消费', i18nData)}}</view>
            </view>

            <smart-store-collection
              wx:if="{{!isMerchantMp}}"
              slot="right"
              mpBridge="{{mpBridge}}"
              custom-class="collection-class"
              storeId="{{storeId}}"
              i18nData="{{i18nData}}"
              generic:auth-login="auth-login"
              catch:showToast="handleToastMessage"
            />
          </smart-ui-nav-bar>
          <smart-preview mpBridge="{{mpBridge}}" catch:exitPreview="clearCachedData" />
          <view class="px-24 relative">
            <view class="mkss-box mb-20">
              <mkss-store-member-info-card
                config="{{_config}}"
                context="{{context}}"
                theme="{{mkssTheme}}"
                store="{{store}}"
                mpBridge="{{mpBridge}}"
                sqbBridge="{{sqbBridge}}"
                generic:auth-login="auth-login"
              />
            </view>
            <smart-store-basic mpBridge="{{mpBridge}}" custom-class="mb-20" store="{{store}}" i18nData="{{i18nData}}">
              <connect-wifi slot="function-tag-before" custom-class="mb-20" store="{{store}}" mpBridge="{{mpBridge}}" generic:auth-login="auth-login" />
            </smart-store-basic>
            <smart-store-service-types
              mpBridge="{{mpBridge}}"
              custom-class="{{serviceTypesClass}}"
              gather="{{gather}}"
              store="{{store}}"
              extra="{{extra}}"
              i18nData="{{i18nData}}"
              catch:getGatherData="refreshGatherData"
            />
            <mkss-store-activities theme="{{mkssTheme}}" mpBridge="{{mpBridge}}" sqbBridge="{{sqbBridge}}" store="{{store}}" generic:auth-login="auth-login" />
            <mkss-store-member-card theme="{{mkssTheme}}" mpBridge="{{mpBridge}}" sqbBridge="{{sqbBridge}}" store="{{store}}" generic:auth-login="auth-login" />
            <view wx:if="{{ from === 'tab' }}" class="{{shouldBrandExpose ? 'brand-tab-bottom' : 'tab-bottom'}}" />
            <!-- <view wx:else class="color-ccc text-26 mt-20 text-center">{{utils.i18n.t('到底了', i18nData) + '~'}}</view> -->
            <other-stores-entry wx:if="{{ from === 'tab' && !isMerchantMp }}" mpBridge="{{ mpBridge }}" initY="{{-228}}" initMinY="{{65}}" initMaxY="{{-60}}" userId="{{ userId }}" />
          </view>

          <!-- 品牌露出 -->
          <smart-brand-expose wx:if="{{shouldBrandExpose}}" sqbBridge="{{sqbBridge}}" from="{{from}}" brandStyle="{{brandStyle}}" brandConfig="{{brandConfig}}"></smart-brand-expose>
        </view>
        <smart-ui-empty
          wx:elif="{{ status === 'empty' || status === 'error' }}"
          custom-class="h-screen"
          image="default"
          description="{{status === 'empty'?utils.i18n.t('您还没有消费记录', i18nData): utils.i18n.t('网络开小差了', i18nData)}}"
        />
      </scroll-view>
    </smart-i18n-provider>
  </smart-main-provider>
</view>
