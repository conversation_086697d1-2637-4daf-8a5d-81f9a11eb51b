import BaseComponent from '@BaseComponent'
import { useThemeBehavior, useI18nBehavior } from '@behaviors'
import qs from 'qs'
import {
  componentStoreBasicKeys,
  pageStoreHomeKeys,
  componentStoreCollectionKeys,
  componentStoreServiceTypeKeys
} from '@utils/i18n_keys'
import { StorageUtils, shouldUseCache } from '@wosai/emenu-mini-utils'
import { StoreService } from '@wosai/emenu-mini-services'
import { loginByWid, makeShareOptions } from '@utils'
import _ from '@wosai/emenu-mini-lodash'
import { parse } from 'qs'

const { storage } = StorageUtils
// 商家小程序门店首页分享路径是选择门店页，且不带门店参数
const pathShareMerchant = 'EZDGV4K8AG7K/ckam0pu9f8rt/index'
// let GATHER_CACHE_EXPIRED_TIME = 5000 // 3秒的缓存时间
const regStoreCache = /^emenu:[a-zA-Z0-9-]+_.+$/
const regSqb = /^https:\/\/(m\.sqbe\.cn|mp\.iwosai\.com)\/([0-9a-f]+)(\??.*)$/
const regJJZ = /^https:\/\/(99zhe\.com|99zhe\.iwosai\.com)\/[\s\S]+$/
const mkssComponentIds = ['7b3a5e9f2c1d', '1phytnqnsuqm', 'nkjdg45l765g']
const memberCreateQrcodeRegex = /^https:\/\/.*?\/40(\?.*)?$/
const MAX_STORE_CACHE_NUM = 3
function decodeQrParams(decodedQ: string) {
  const matches = regSqb.exec(decodedQ)
  const JJZMatchs = regJJZ.exec(decodedQ)
  if (matches || JJZMatchs) {
    try {
      const qrPageIdHex = matches ? matches[2] : '2d'
      return {
        qrPageIdHex,
        qrParams: { q: decodedQ }
      }
    } catch (e) {
      // @ts-ignore
      console.error('扫码参数解析错误', e.message)
    }
  }
  return false
}
BaseComponent({
  options: {
    // 使支付宝小程序支持 lifetimes 功能
    // @ts-ignore
    lifetimes: true
  },
  behaviors: [
    useI18nBehavior({
      keys: [
        ...componentStoreBasicKeys(),
        ...pageStoreHomeKeys(),
        ...componentStoreCollectionKeys(),
        ...componentStoreServiceTypeKeys()
      ],
      componentId: 'smart-i18n-provider'
    }),
    useThemeBehavior([], 'store-home-page')
  ],
  properties: {
    mpBridge: {
      type: Object,
      value: {}
    },
    query: {
      type: Object,
      value: {}
    },
    sqbBridge: {
      type: Object,
      value: {}
    }
  },
  data: {
    isBlackNav: false,
    serviceTypesClass: 'service-types-class__style1',
    isMerchantMp: false,
    status: 'loading',
    userId: '',
    storeId: '',
    store: null,
    gather: null,
    from: 'page',
    showDefaultNav: false,
    isQrcodeScan: false,
    extra: {
      qOrder: '',
      from: 'page'
    },
    templateParams: {},
    loadingErrors: [],
    brandStyle: {
      bottom: '0'
    },
    isCampusScene: false,
    shouldBrandExpose: false
  },
  observers: {
    query(query) {
      const mpBridge = this.data.mpBridge || this.props.mpBridge
      const { extra } = this.data
      if (query?.__mpci__pageIsTab == true) {
        query.from = 'tab'
        this.setData({ brandStyle: { bottom: '116rpx' } })
      }

      const newExtra = { ...extra }
      if (query?.q) {
        // 是扫码
        let { qQuery } = query
        mpBridge.slsLog?.send(`[store-home] 扫码途径进入门店首页, `)
        const qStr = decodeURIComponent(query.q)
        newExtra.q = qStr
        this.setData({ from: 'share', extra: newExtra, isQrcodeScan: true })
        if (!qQuery) {
          const idxQuery = qStr.indexOf('?')
          if (idxQuery !== -1) {
            qQuery = parse(qStr.slice(idxQuery + 1))
          }
        }
        const decodeResult = decodeQrParams(qStr)
        // 2d 点单落地页
        if (decodeResult?.qrPageIdHex === '2d') {
          mpBridge.slsLog?.send(`[store-home] 点单码跳转门店首页, url: ${qStr}`)
          newExtra.qOrder = this.filterUrlTemplateParams(qStr)
          this.setData({ extra: newExtra })
        } else if (memberCreateQrcodeRegex.test(qStr)) {
          this.setData({ extra: newExtra })
        } else if (!decodeResult) {
          wx.showModal({
            title: '加载失败',
            content: '无效的门店码',
            showCancel: false
          })
          this.setData({ loadingErrors: [{ error: '无效的门店码' }] })
          // this.setData({ status: 'error' })
        }

        query = { ...query, ...(qQuery || {}), from: 'share' }
      }
      this.setData(query)
    },
    store(store) {
      const mpBridge = this.data.mpBridge || this.props.mpBridge

      if (store && Object.keys(store).length && store.storeId) {
        mpBridge.setMeta?.({
          store_name: store.storeName,
          store_id: store.storeId,
          merchant_name: store.merchantName,
          merchant_id: store.merchantId,
          merchant_sn: store.merchantSn
        })
        mpBridge.slsLog?.pv()
      }
    }
  },
  lifetimes: {
    attached() {
      this.initContext()
      /* #ifdef alipay */
      my.hideBackHome()
      /* #endif */
    },
    async ready() {
      this.initData()
      this.registerPageShowCB()
      this.onPageShow()
      this.limitCacheSize()
    },
    /* #ifdef wechat */
    detached() {
      this.onDetached()
    }
    /* #endif */
  },
  /* #ifdef alipay */
  didUnmount() {
    // 支付宝某些基础库版本 bug，由页面关闭导致的组件 detached 不触发，所以加这种调用
    this.onDetached()
  },
  /* #endif */
  // @ts-ignore
  methods: {
    handleShouldBrandExpose() {
      const {
        isBrandExpose,
        isMerchantMp,
        isMerchantMiniShow,
        isCampusScene,
        isCampusSceneShow,
        sqbBridge
      } = this.data
      const shouldBrandExpose =
        isBrandExpose &&
        (!isMerchantMp || isMerchantMiniShow) &&
        (!isCampusScene || isCampusSceneShow)
      this.setData({ shouldBrandExpose })
      if (shouldBrandExpose) {
        sqbBridge.track('SmMpUIViewDisplay', {
          sm_uiview_name: '品牌标识'
        })
      }
    },

    initContext() {
      this.storeSvc = new StoreService()
      const { mpBridge } = this.data
      const ctxMp = mpBridge.getContext?.() || {}
      const isMerchantMp = ctxMp.platform?.type === 'merchant'
      this.setData({ isMerchantMp })
    },
    initData() {
      const { mpBridge } = this.data
      // this.$loading('加载中...')
      this.initStoreAndUser()
        .then(async () => {
          // 成功时埋点，此时可能有门店，或没有最近下单门店
          const { storeId } = this.data
          if (!storeId) {
            // this.$clearToast()
            return
          }
          mpBridge.sensors?.track('ConMiniPageView', {})
          await this.initGatherData(storeId)
          this.handleBrandExpose()
          this.getStoreInCampus().then(() => {
            this.handleShouldBrandExpose()
          })
          this.initShareInfo()
          this.smMpUIViewDisplay()
          // this.$clearToast()
        })
        // @ts-ignore
        .catch(e => {
          // this.$clearToast()
          wx.showModal({
            title: '加载失败，请稍后重试',
            content: e.message || '',
            showCancel: false,
            confirmText: '重试',
            success: res => {
              const { confirm } = res
              if (confirm) {
                this.initData()
              }
            }
          })
          mpBridge.sensors?.track('ConMiniPageView', {})
          this.setData({ status: 'error' })
          console.error(e)
        })
    },

    //获取附近门店数据
    async getRecentStore() {
      const { mpBridge } = this.data
      const res = await mpBridge.request({
        url: '/v3/account/recent-store',
        method: 'POST'
      })
      if (Number(res.data.code) !== 10000) {
        throw new Error(res.data.msg || '')
      }
      const store = res.data.data?.[0]
      return store || {}
    },
    //获取二维码中门店数据
    async getStoreInfoFromOrderQrcode(url) {
      const { mpBridge } = this.data
      const res = await mpBridge.request({
        url: '/parseQrCodeForPageJump',
        method: 'POST',
        data: { url }
      })
      if (Number(res.data.code) !== 10000) {
        throw new Error(res.data.msg || '')
      }
      const storeInfo = res.data.data
      if (!storeInfo) {
        throw new Error('no storeInfo returned')
      }
      const ret = {
        storeId: storeInfo.store_id,
        storeName: storeInfo.store_name,
        merchantId: storeInfo.merchant_id,
        merchantName: storeInfo.merchant_name,
        merchantSn: storeInfo.merchant_sn
      }
      return ret
    },

    async initStoreAndUser() {
      const { mpBridge, storeId, isQrcodeScan, extra } = this.data
      const { wid } = await mpBridge.identify()
      this.setData({ userId: wid })
      console.log(999, storeId)
      if (storeId) {
        this.setData({ storeId })
      } else if (isQrcodeScan) {
        const { qOrder } = extra
        if (!qOrder) return
        try {
          const { storeId } = await this.getStoreInfoFromOrderQrcode(qOrder)
          this.setData({ storeId })
        } catch (e) {
          console.error(e)
          mpBridge.slsLog?.send(`[store-home] 点单码url获取门店数据失败, url: ${qOrder}`)
        }
      } else {
        // 组件没有传入 storeId，查最近下单门店接口
        const { storeId } = await this.getRecentStore()
        if (storeId) {
          this.setData({ storeId })
        } else {
          this.setData({ status: 'empty' })
        }
      }
    },

    //gather数据
    async initGatherData(storeId) {
      console.log('initGatherData', storeId)

      const { mpBridge } = this.data
      const query = {
        storeId,
        serviceTypeName: 'subscribe_order'
      }
      await loginByWid(mpBridge)
      const gather = await this.getGatherData(query)
      this.initTheme(gather.template)
      console.log('gather', gather, storeId, gather.store, storeId)
      this.setData({ gather, store: gather.store })
      setTimeout(() => {
        this.setData({ status: 'ready' })
      })
    },
    refreshGatherData(e) {
      this.getGatherData(e.detail, false)
    },
    async getGatherData(query, isInit = true) {
      const { storeId, serviceTypeName } = query
      const { sqbBridge, mpBridge, extra = {} } = this.data
      const { templateId = '', previewId = '' } = sqbBridge.getMiniProgramQuery()
      const { qOrder } = extra
      let result
      // @ts-ignore
      const cachedPayload = storage(`emenu:${storeId}_${serviceTypeName}`) || {}

      if (!qOrder && !templateId && !previewId && shouldUseCache(cachedPayload)) {
        const data = _.get(cachedPayload, 'data')
        result = await sqbBridge.initByPayload(data, storeId)
      } else {
        const loginInfo = mpBridge.getLoginInfo() || {}
        const _loginInfo = { loginUserId: loginInfo.uc_user_id }
        const { serviceTypeName } = query
        // @ts-ignore
        let qOrder = extra.qOrder
        const pageQuery = mpBridge.getCurrentPageQuery()

        const qStr = _.get(pageQuery, 'q')
        if (!qOrder && qStr && !memberCreateQrcodeRegex.test(decodeURIComponent(qStr))) {
          qOrder = qStr
        }
        if (_.isFunction(mpBridge.getPlatformLoginInfo)) {
          const platformLoginInfo = mpBridge.getPlatformLoginInfo()
          // @ts-ignore
          _loginInfo.openid = _.get(platformLoginInfo, 'openid')
          // @ts-ignore
          _loginInfo.unionid = _.get(platformLoginInfo, 'unionid')
        }
        const templateParams = isInit
          ? this.refreshCacheTemplateParams()
          : this.getCacheTemplateParams()
        const _query = {
          ...templateParams,
          ..._loginInfo,
          ...query,
          compatible: true,
          serviceTypeName,
          /* #ifdef wechat */
          query: qOrder ? `q=${qOrder}` : ''
          /* #endif */
        }
        // delete _query.qOrder;
        result = await sqbBridge.initByQrcodeOrStoreId(_query)
        if (!_.get(result, 'store.storeId')) {
          throw new Error(_.get(result, 'errors.[0].message') || '')
          //return {}
        }
        if (!qOrder) {
          storage(`emenu:${storeId}_${serviceTypeName}`, {
            data: result,
            query: _query,
            timestamp: Date.now()
          })
        }
      }
      return result
    },

    async getStoreInCampus() {
      const pages = getCurrentPages()
      const storeSn = _.get(this.getStore(), 'storeSn')
      let isInCampus: boolean

      if (storeSn) {
        isInCampus = await this.storeSvc.getStoreInCampus({ storeSn }).catch(() => false)
      } else {
        isInCampus = false
      }
      // i6hm9nxuyrd7 校园页面路径
      const hasCampusPage = _.some(pages, page => _.includes(page.route, 'i6hm9nxuyrd7'))
      this.setData({
        isCampusScene: hasCampusPage || isInCampus
      })
    },

    initTheme(template) {
      if (!template) return
      this.updateTheme(template || {})
      this.updateMkssTheme(template, mkssComponentIds)
      this.updateNavTheme(template)
    },
    updateNavTheme(template) {
      // @ts-ignore
      const { theme } = this.data
      const serviceTypesStyle = _.get(theme, 'config.service-type-class')
      serviceTypesStyle &&
        this.setData({ serviceTypesClass: `service-types-class__${serviceTypesStyle}` })

      //原先混搭主题特殊处理导航栏颜色，其余主题均使用通用配置
      const blackNavList = [
        'MDDX开封菜',
        'MDDX经典',
        'MDDX茶饮',
        '茶饮X开封菜',
        '茶饮XMDD',
        '茶饮X经典'
      ]
      const whiteNavList = [
        '经典X开封菜',
        '经典XMDD',
        '经典X茶饮',
        '开封菜XMDD',
        '开封菜X茶饮',
        '开封菜X经典'
      ]
      let backgroundColor = _.get(theme, 'settings.navigationBarColor', '#000000')

      if (blackNavList.indexOf(template?.template_name) > -1) {
        backgroundColor = '#FFFFFF'
      } else if (whiteNavList.indexOf(template?.template_name) > -1) {
        backgroundColor = '#000000'
      }

      if (backgroundColor.toLowerCase() === '#ffffff') {
        this.setData({ isBlackNav: true })
        this.setNavigation('#FFFFFF', '#000000')
      } else {
        this.setNavigation()
      }
    },
    async getStoreRenovation() {
      const { storeId, gather } = this.data
      const data = await this.storeSvc.getShareInfo({
        store_id: storeId,
        template_id: _.get(gather, 'template.template_id')
      })
      return data || null
    },

    async initShareInfo() {
      const { sqbBridge, mpBridge, isMerchantMp, storeId } = this.data
      const data = await this.getStoreRenovation()
      const { title, imageUrl } = makeShareOptions('store', data, this)
      const pages = getCurrentPages()
      const currentPage = pages[pages.length - 1]
      const currentPagePath = currentPage.route
      const path = isMerchantMp
        ? pathShareMerchant
        : `/${currentPagePath}?${storeId ? qs.stringify({ storeId }) : ''}`
      const onShareAppMessage = () => {
        mpBridge.shence.track('ConMiniPageClick', {
          click_name: '分享店铺'
        })
        mpBridge.slsLog.send('share', path, title, imageUrl)
        return {
          imageUrl,
          title,
          path
        }
      }

      // 分享回调
      if (sqbBridge.isJJZ) {
        sqbBridge.setShareAppMessage(onShareAppMessage)
      } else {
        sqbBridge.on('shareAppMessage', onShareAppMessage)
      }
    },

    registerPageShowCB() {
      const { mpBridge } = this.data
      const onPageShow = () => {
        this.onPageShow()
      }
      mpBridge.on.page.show(onPageShow)
      const onPageUnload = () => {
        mpBridge.off.page.show(onPageShow)
        mpBridge.off.page.unload(onPageUnload)
      }
      mpBridge.on.page.unload(onPageUnload)
    },
    onPageShow() {
      const { mpBridge, store, status } = this.data
      mpBridge.setMeta({
        page_name: '商铺首页',
        page_url: mpBridge.getCurrentUrl()
      })
      if (store && Object.keys(store).length) {
        mpBridge.setMeta({
          // @ts-ignore
          store_name: store.storeName,
          // @ts-ignore
          store_id: store.storeId,
          // @ts-ignore
          merchant_name: store.merchantName,
          // @ts-ignore
          merchant_id: store.merchantId,
          // @ts-ignore
          merchant_sn: store.merchantSn
        })
      }
      if (status === 'error') {
        this.initData()
      }
    },
    onDetached() {
      // 防止模板名称在门店首页页面以外的地方被上报，清理一下
      this.data.mpBridge.removeMeta?.('templateName')
      this.clearCachedData()
    },
    clearCachedData() {
      const { storeId } = this.data
      const serviceTypeName = 'subscribe_order'
      storage(`emenu:${storeId}_${serviceTypeName}`, null)
    },
    setNavigation(backgroundColor = '#000000', backColor = '#FFFFFF') {
      // '#FFFFFF', '#000000'
      this.call('setNavigation', {
        backgroundColor,
        custom: true,
        homeButton: false,
        backColor
      })
    },
    //返回
    // @ts-ignore
    onLeftClick(e) {
      const { from } = this.data
      from === 'page' && this.call('navigateBack', { delta: 1 })
    },

    async getAllCacheKeys() {
      try {
        const { keys } = await wx.getStorageInfo()

        return keys.filter(key => regStoreCache.test(key))
      } catch (error) {
        console.error('获取缓存信息失败:', error)
        return []
      }
    },
    async limitCacheSize() {
      try {
        const keys = await this.getAllCacheKeys()
        if (keys.length <= MAX_STORE_CACHE_NUM) return
        const caches = await Promise.all(
          keys.map(async key => {
            const { data } = await wx.getStorage({ key })
            const timestamp = data?.updatedAt || Date.now()
            return { key, timestamp }
          })
        )
        // 按时间戳降序排序
        caches.sort((a, b) => b.timestamp - a.timestamp)
        // 删除旧的缓存
        for (let i = MAX_STORE_CACHE_NUM; i < caches.length; i++) {
          await wx.removeStorage({ key: caches[i].key })
        }
      } catch (error) {
        console.error('缓存清理失败:', error)
      }
    },
    //页面滚动
    onPageScroll: _.debounce(function (e) {
      // @ts-ignore
      const { showDefaultNav, isBlackNav } = this.data
      const scrollTop = Math.floor(_.get(e, 'detail.scrollTop') || 0)
      if (scrollTop > 88) {
        if (!showDefaultNav) {
          // @ts-ignore
          this.setData({ showDefaultNav: true })
          // @ts-ignore
          this.setNavigation('#FFFFFF', '#000000')
        }
      } else {
        if (showDefaultNav) {
          // @ts-ignore
          this.setData({ showDefaultNav: false })
          // @ts-ignore
          if (!isBlackNav) this.setNavigation()
        }
      }
    }, 20),

    // 埋点
    smMpUIViewDisplay() {
      const { sqbBridge, from } = this.data
      sqbBridge.track('SmMpPageDisplay', {
        sm_page_name: from === 'tab' ? '最近消费页' : '商家首页'
      })
    }
  }
})
