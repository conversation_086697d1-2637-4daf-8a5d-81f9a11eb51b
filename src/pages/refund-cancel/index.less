@import (css) '@styles/mixins';
@import (css) '@styles/flex';
@import (css) '@styles/common'; /* css */

.smart-page-cancel-order {
  height: 100vh;
  &.container {
    // --smart-submit-nav-bar__background-image: var(--main__bg-img-3k2l1m0n9o8p, url(https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png));
    --smart-global-primary-theme__color: var(--primary__color, #fd6d05); //#e65245
    --smart-global-secondary-theme__color: var(--primary-foreground__color, #ffffff);
    --smart-global-primary-complementary__color: var(--primary-foreground__color, #000);
    --smart-global-secondary-complementary__color: var(--secondary-foreground__color, #000);
    --smart-global-gradient-theme__color: var(
      --primary-gradient__color,
      linear-gradient(270deg, #ffa331 0%, #ff6a16 100%)
    );
  }
  .s-120 {
    width: 120px;
    height: 120px;
  }

  .nav-bar {
    --icon-color: var(--smart-global-secondary-theme__color);
  }
  .status-color {
    color: var(--smart-global-secondary-theme__color, #ffffff);
  }
  .card-var {
    --card-border-radius: 12px;
    --card-background-color: #ffffff;
    --card-padding-bottom: 30px;
    --card-padding-right: 30px;
    --card-padding-left: 30px;
    --card-padding-top: 30px;
  }

  .icon-color {
    --icon-color: var(--smart-global-secondary-theme__color, #ffffff);
    --icon-background-color: var(--smart-global-primary-theme__color, #fd6d05);
  }

  .submit-btn {
    --button-default-background-color: var(--smart-global-primary-theme__color, #fd6d05);
    --button-default-color: var(--smart-global-secondary-theme__color, #ffffff);
    --button-normal-font-size: 34px;
    --button-font-weight: 500;
    width: calc(100vw - 40px);
    margin: 0 20px;
  }
  // .footer {
  //   box-shadow: 0px -1px 14px 0px rgba(0, 0, 0, 0.08);
  //   padding: 32px 30px 30px 30px;
  //   padding-bottom: constant(safe-area-inset-bottom);
  //   padding-bottom: ~'max(38px,calc(env(safe-area-inset-bottom,38px)))';
  // }

  .w-210 {
    width: 210px;
  }
  .h-80 {
    height: 80px;
  }
  .h-90 {
    height: 90px;
  }

  /* prettier-ignore */
  .uncheck {
/* prettier-ignore */
    border: 1PX solid #CCC;
/* prettier-ignore */
    width: 20PX;
/* prettier-ignore */
    height: 20PX;
    box-sizing: border-box;
    border-radius: 50%;
  }

  .h-90 {
    height: 90px;
  }

  .is-valid {
    background-color: #fd6d05;
    pointer-events: auto;
  }
  // .container {
  //   &.loading {
  //     display: none;
  //   }
  //   &.skeleton {
  //     height: 100%;
  //     background-repeat: no-repeat;
  //     background-size: contain;
  //     width: 100%;
  //     background-image: url('https://smart-static.wosaimg.com/themes/cancel-order-skeleton.png');
  //   }
  // }
}
