{
  "component": true,
  "usingComponents": {
    "smart-main-provider": "@components_v2/main-provider/index",
    "smart-main-provider-theme": "@components_v2/main-provider-theme/index",
    "smart-full-loading":"@components_v2/full-loading/index",
    "smart-ui-cell": "@wosai/smart-mp-ui/cell/index",
    "smart-ui-divider": "@wosai/smart-mp-ui/divider/index",
    "smart-goods-item": "@components_v2/goods/basic/index",
    "smart-cashier": "@components_v2/cashier/index",
    "smart-ui-icon": "@wosai/smart-mp-ui/icon/index",
    "smart-alert": "@components_v2/alert/index",
    "smart-hbfq-modal": "@components_v2/hbfq/index",
    "smart-ui-nav-bar": "@wosai/smart-mp-ui/nav-bar/index",
    "smart-adsense": "@components_v2/adsense/index",
    "smart-payment-summary": "@components_v2/payment-summary/index",
    "smart-edit-people-num": "@components_v2/group-meal-edit-people/index",
    "smart-ui-image": "@wosai/smart-mp-ui/image/index",
    "smart-card": "@components_v2/card/index",
    "smart-button-group": "@components_v2/group-meal-btn-group/index",
    "smart-i18n-provider": "@components_v2/i18n-provider/index",
    "smart-ui-empty": "@wosai/smart-mp-ui/empty/index",
    "smart-price": "@components_v2/price/index",
     "smart-ads": "@components_v2/ads/index"
  },
 
  "componentGenerics": {
    // #ifdef wechat
    "auth-login": true,
    "auth-phone": true,
    "mkss-stored-activity-dialog": true,
    "mkss-member-points-discount": true,
    "mp-pay-sodexo-card": true,
    "mkss-discount-detail": true,
     "ads-multiple-banner": true,
    // #endif
    // #ifdef alipay
    "auth-user": {
      "default": "@components_v2/generics-default/index"
    },
    "auth-login": {
      "default": "@components_v2/generics-default/index"
    },
    "auth-phone": {
      "default": "@components_v2/generics-default/index"
    },
    "mkss-stored-activity-dialog": {
      "default": "@components_v2/generics-default/index"
    },
    "mkss-member-points-discount": {
      "default": "@components_v2/generics-default/index"
    },
    "mp-pay-sodexo-card": {
      "default": "@components_v2/generics-default/index"
    },
     "ads-multiple-banner": {
      "default": "@components_v2/generics-default/index"
    },
    "mkss-discount-detail": {
      "default": "@components_v2/generics-default/index"
    }
    // #endif
  }
}
