import { getDataset } from '@utils'
import CONFIG from '@wosai/emenu-mini-config'
import _, { isEmpty } from '@wosai/emenu-mini-lodash'
import { getActionsByOrder } from '../../../order-detail/lib/actions'
import BaseComponent from '@BaseComponent'
const { WAIT_PAYED_STATUS, ORDER_TYPE_TEXT, EAT_FIRST_ORDER } = CONFIG

BaseComponent({
  externalClasses: ['custom-class'],
  properties: {
    isShowMap: {
      type: Boolean,
      value: false
    },
    order: {
      type: Object,
      value: {}
    },
    config: {
      type: Object,
      value: {}
    },
    onlyOrder: {
      type: Boolean,
      value: false
    },
    i18nData: {
      type: Array,
      value: []
    }
  },
  data: {
    ORDER_TYPE_TEXT,
    WAIT_PAYED_STATUS,
    btnList: null
  },
  observers: {
    order: function (order) {
      if (isEmpty(order)) return
      const { status, merchantPhone, isOnlyInStore } = order || {}
      let btnList = getActionsByOrder(
        {
          ...order,
          status,
          merchantPhone,
          isOnlyInStore
        },
        'ORDER_LIST'
      )

      if (this.data.onlyOrder) {
        btnList = _.reject(btnList, item => item.value === 'pay' && order.type === EAT_FIRST_ORDER)
      }
      this.setData({ btnList })
    }
  },
  methods: {
    onLink(e) {
      const { order, btnList } = this.data
      const { value, item } = getDataset(e)
      const action = _.find(btnList, item => item.value === value)

      action && action.do.bind(this)({ ...order, btnItem: item, trackName: 'SmMpUIViewClick' })
    }
  },
  created() {}
})
