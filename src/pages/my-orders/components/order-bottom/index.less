@import (css) '@styles/const';
@import (css) '@styles/mixins';
@import (css) '@styles/flex';
@import (css) '@styles/common';
@import (css) '@styles/theme';

.order-bottom-wrap {
  padding: 20px 27px 20px 30px;

  .order-bottom-left {
    line-height: 32px;
    flex: initial;
  }

  .order-bottom-right {
    right: 0;
    z-index: 101;

    .order-bottom-btn {
      min-width: 160px;
      width: fit-content;
      height: 54px;
      color: #666666;
      border: 2px solid #666666;
      padding: 0 !important;
      --button-normal-font-size: 26px;

      &.highlight {
        border-color: var(--smart-my-order-button-border-color, --primary-second-color);
        color: var(--smart-my-order-button-color, --primary-second-color);
      }

      &.pay {
        background: var(--smart-my-order-pay-button-background-color, --primary-second-color);
        color: var(--smart-my-order-pay-button-color, --white-color);
        border: 0;
      }
    }
  }

  &.heytea {
    .order-number {
      color: #333;
    }
  }
}
