import BaseComponent from '@BaseComponent'
import CONFIG from '@wosai/emenu-mini-config'
import { CART_CHANGE_EVENT } from '@utils'
import Helpers from '@utils/alertHelpers'

import { isRoundMeal, showStoredPay, useTrack } from '@utils/helper'
import _ from '@wosai/emenu-mini-lodash'

import { isWeixin } from '@wosai/emenu-mini-utils'
import { handleCartByDiscountWithOff, loadDiscount, setDiscountCtx } from '@utils/discount'

import {
  ThemeBehavior,
  CartBehavior,
  BaseBehavior,
  RemarkBehavior,
  SvcBehavior,
  EventBehavior,
  OrderBehavior,
  MaterialBehavior,
  CashierBehavior
} from '@utils/mixins/roundMeal'

import { useI18nBehavior, useThemeBehavior } from '@behaviors'
import {
  pageCartKeys,
  componentPaidAmountKeys,
  componentUserCountDialogKeys
} from '@utils/i18n_keys'

const { MCC_TAKEOUT_PROFIT_SHARING, MCC_HIDE_TOTAL_PRICE } = CONFIG

BaseComponent({
  behaviors: [
    BaseBehavior,
    SvcBehavior,
    OrderBehavior,
    RemarkBehavior,
    EventBehavior,
    ThemeBehavior,
    CartBehavior,
    MaterialBehavior,
    CashierBehavior,
    useI18nBehavior({
      keys: [...pageCartKeys(), ...componentPaidAmountKeys(), ...componentUserCountDialogKeys()],
      componentId: 'smart-i18n-provider'
    }),
    useThemeBehavior([], 'group-meal-post-cart-page')
  ],

  options: {
    // 使支付宝小程序支持 lifetimes 功能
    lifetimes: true
  },
  lifetimes: {
    async attached() {
      const { sqbBridge } = this.data
      const { storeId, merchantId } = this.store
      const serviceType = sqbBridge.getServiceTypes('serviceType')

      this.setData({
        bulletinPayload: {
          merchantId,
          storeId,
          serviceType
        }
      })

      this.trackIns = useTrack(this)
      setDiscountCtx(this)
      await this.getCartData()
      // 监听页面show事件
      // this.call('on', 'show', this.onShow.bind(this))
    },
    async ready() {
      this.initPageStyle()
      if (await this.isCashierMode()) this.checkCashier()
      const { sqbBridge } = this.data

      if (sqbBridge) {
        const hideTotalPrice = await sqbBridge.getMcc(MCC_HIDE_TOTAL_PRICE || 'hide_total_price')
        hideTotalPrice && this.setData({ hideTotalPrice })
      }
    }
  },
  // behaviors: [
  // useI18nBehavior({
  //   keys: pageCartKeys(),
  //   componentId: 'page-cart',
  // }),
  // ],
  properties: {
    config: {
      type: Object,
      value: {}
    }
  },
  td: {
    continueOrder: {
      type: 'throttle'
    },

    placeOrder: {
      type: 'throttle'
    },

    editPeople: {
      type: 'throttle'
    },

    transformMaterialData: {
      type: 'debounce',
      delay: 200
    }
  },

  data: {
    componentName: 'page-cart',
    discount: {},
    cart: {},
    canPlaceOrder: true,
    tableName: '',
    remark: '',
    loaded: false,
    loadingErrors: [],
    showDiscountRow: false,
    canBack: false,
    isWeixin: isWeixin(),
    // 拥有推荐加料的购物记录ID
    recommendMaterialRecordIds: [],
    isWeapp: isWeixin(),
    invalidGoodsList: [],
    bulletinPayload: {}, // 传给合规提示栏的参数
    hideTotalPrice: false
  } as any,

  methods: {
    ...Helpers,

    onShow() {
      this.syncRemark()
      this.refreshData()
    },
    async afterCartChanged(cart, discount) {
      this.getDiscountData(cart, discount)
    },

    async refreshData() {
      this.getCartData()
    },

    getCartData() {
      return this.cartSvc
        .fetchCartData({
          service_type: 2,
          fill_recommend_materials: true
        })
        .then(async data => {
          this.setData({
            cart: data,
            loaded: true
          })
          this.bus.emit(CART_CHANGE_EVENT, data)
        })
        .catch(e => {
          console.log(e)
          this.setData({ loaded: true, loadingErrors: [{ error: e?.toString() }] })
        })
    },

    async getDiscountData(_cart, _discount) {
      let { sqbBridge, cart: prevCart } = this.data
      const self = this
      const service_type_name = sqbBridge.getServiceTypes(`serviceTypeName`)
      const total_amount = _.get(_cart, 'total_price') || 0
      const takeoutProfitSharing = await sqbBridge.getMcc(MCC_TAKEOUT_PROFIT_SHARING)
      const supportCardPay = sqbBridge.getSupportCardPay()

      loadDiscount(
        {
          cart: _cart,
          discount: _discount,
          payload: {
            service_type_name,
            from_cart: showStoredPay(
              service_type_name,
              takeoutProfitSharing,
              isRoundMeal(this),
              supportCardPay
            ),
            sn: _cart.sn,
            from_submit_order: true
          }
        },
        discount => handleCartByDiscountWithOff.call(this, _cart, discount)
      )
        .then(async ({ cart, discount }) => {
          cart.records = _.filter(cart.records, record => !!record.num)
          const cartLengthChanged =
            _.size(_.get(prevCart, 'records')) !== _.size(_.get(cart, 'records'))
          if (cartLengthChanged) {
            this.setData({ recommendMaterialRecordIds: [] })
          }

          self.setData({
            discount,
            cart: _.cloneDeep(cart),
            amountLimit: total_amount > 0,
            paidAmount: cart.sn
              ? cart.total_price || 0
              : (cart.total_price || 0) - (discount.total_discount || 0)
          })
        })
        .catch(err => {
          self.setData({
            cart: _.cloneDeep(_cart),
            amountLimit: total_amount > 0
          })
          return console.error('优惠加载错误', err)
        })
    },

    async checkCondition() {
      let { sqbBridge } = this.data

      const onBusiness = await this.storeSvc.checkOpeningTime(
        sqbBridge.getServiceTypes(`serviceType`)
      )
      if (!onBusiness) {
        this.$toast(this.t('商家打烊了～'))
        this.setData({ canPlaceOrder: false })
        return
      }
      const cashierOnline = await this.checkCashier()
      if (!cashierOnline && (await this.isCashierMode())) {
        this.$toast(this.t('收银机不在线~'))
        this.setData({ canPlaceOrder: false })
        return
      }
      return true
    },

    async placeOrder() {
      const condition = await this.checkCondition()
      let { sqbBridge, no_check_must_order_goods } = this.data

      if (!condition) return
      const serviceType = sqbBridge.getServiceTypes(`serviceType`)

      const { records = [] } = this.data.cart

      const { remark } = this.data

      if (!records.length) return
      const tableOrder = (await this.orderSvc.getTableOrder()) || {}
      const { sn = '' } = tableOrder
      const params: any = { service_type: serviceType }
      params.items = _.map(records, record => {
        return {
          item_uid: record.id,
          number: record.num
        }
      })

      let user = await sqbBridge.getMiniProgramUser()

      params.user_icon = user.avatarUrl
      params.user_name = user.nickName

      if (sn) params.order_sn = sn
      if (remark) params.remark = remark

      // 不检查必点商品
      if (no_check_must_order_goods) {
        params.check_must_order_exist = false
      }

      this.createOrder(!!sn, params).then(result => {
        if (
          result &&
          result.data &&
          result.data.cart_check_result &&
          !result.data.cart_check_result.success
        ) {
          this.processOrderFail(result, {
            whenErrorCodeIsConscurrentConfirmOkCallback: () => {
              this.redirect('page-submit-order')
            },
            whenErrorCodeIsConscurrentConfirmCancelCallback: () => {
              this.goBack()
            },
            whenErrorCodeIsNoMustOrderGoodsCallback: data => {
              // 50015:未选择必选品,此异常码执行回调的时候去点单首页
              this.redirect('page-home', { fromPaySubmitOrder: 'batchNoMustOrderGoods' })
            },
            // 去调整
            whenErrorCodeIsMustGoodsExistsConfirmOkCallback: data => {
              this.setData({
                no_check_must_order_goods: true
              })
              // 设置可编辑
              this.orderSvc
                .setCartMustOrderEditable({ table_id: _.get(this.terminal, 'tableId') })
                .then(() => {
                  this.refreshData()
                })
            },
            // 继续下单
            whenErrorCodeIsMustGoodsExistsConfirmCancelCallback: () => {
              this.setData({
                no_check_must_order_goods: true
              })
              this.placeOrder()
            },
            whenErrorIsOtherCallback: () => {
              this.refreshData()
            }
          })
        } else {
          setTimeout(() => {
            this.bus.emit(CART_CHANGE_EVENT, {})
          }, 1000)
          this.redirect('page-submit-order')
        }
      })
    },

    createOrder(order_sn = false, params = {}) {
      return order_sn ? this.orderSvc.addOrderGoodsV2(params) : this.orderSvc.initOrderV2(params)
    }
  }
})
