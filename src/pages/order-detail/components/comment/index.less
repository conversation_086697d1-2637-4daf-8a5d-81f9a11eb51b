@import (css) '@styles/flex'; /* css */
@import (css) '@styles/common'; /* css */

.comment-wrap {
  --cell-height: 132px;
  width: auto;
  max-width: 715px;
  background: #ffffff;
  border-radius: 12px;

  .comment-title {
    flex: none;
  }

  .status-item {
    width: 204px;
    height: 72px;
    padding: 10px 20px;
    background-color: #f5f5f5;
    border-radius: 40px;

    &:first-child {
      margin-right: 20px;
    }
  }

  .icon-img {
    width: 52px;
    height: 52px;
    display: block;
  }
}
