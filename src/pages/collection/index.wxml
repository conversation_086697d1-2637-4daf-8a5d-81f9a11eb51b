<wxs src="@wxs/utils.wxs" module="utils" />
<wxs src="@wxs/toCSSVars.wxs" module="computed" />
<smart-i18n-provider id="provider-collection" sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}">
  <smart-main-provider id="main-provider" full-height>
    <smart-full-loading show="{{isLoading}}" sqbBridge="{{sqbBridge}}" errors="{{loadingErrors}}" custom-error-class="loading-error" />
    <view class="smart-page h-screen" wx:if="{{!isLoading}}">
      <scroll-view class="smart-user-collection w-full h-full bg-f6 box-border pb-30" enhanced show-scrollbar="{{false}}" scroll-y wx:if="{{(campusCollections.length) || (stores.length)}}">
        <view class="campus-warp" wx:if="{{campusCollections && campusCollections.length}}">
          <view class="campus-name text-24 color-lightgray mb-20">{{utils.i18n.t('校园', i18nData)}}</view>
          <smart-card custom-class="campus-warp-content bg-white rounded-12 overflow-hidden">
            <smart-ui-cell
              custom-class="campus-item text-30 overflow-hidden flex-center-y"
              title="{{item.campusName}}"
              isLink
              wx:for="{{campusCollections}}"
              wx:key="index"
              wx:for-item="item"
              bind:click="goCampusHome"
              data-item="{{item}}"
              border="{{campusCollections.length-1==index?false:true}}"
              iconRotate="180"
            />
          </smart-card>
        </view>
        <view class="stores-warp mb-30" wx:if="{{stores && stores.length}}">
          <view class="stores-name text-24 color-lightgray mb-20">{{utils.i18n.t('店铺', i18nData)}}</view>
          <view class="stores-content-wrap" wx:for="{{stores}}" wx:key="index">
            <smart-store-item store="{{item}}" sqbBridge="{{sqbBridge}}" catch:click="onLinkStoreHome" custom-class="mb-20" />
          </view>
        </view>
      </scroll-view>
      <smart-ui-empty custom-class="w-f100 h-screen" image="default" description="{{utils.i18n.t('您还没有关注的店铺', i18nData) + '~'}}" wx:else />
    </view>
  </smart-main-provider>
</smart-i18n-provider>
