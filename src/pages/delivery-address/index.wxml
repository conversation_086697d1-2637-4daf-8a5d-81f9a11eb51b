<wxs src="@wxs/utils.wxs" module="utils" />
<wxs src="@wxs/toCSSVars.wxs" module="computed" />

<view class="smart-page page-delivery-address theme-style h-full bg-f6" style="{{computed.toCSSVars(theme.style)}}">
  <smart-i18n-provider id="smart-i18n-provider" sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}">
    <smart-main-provider id="main-provider" theme="theme-style" full-height>
      <view class="pt-20"></view>
      <smart-card class="w-auto mx-20 mt-20 rounded-12 overflow-hidden bg-white">
        <smart-ui-cell
          title="{{areas.type === 'campus' ? item.campus_page_name : item.name}}"
          label="{{item.address}}"
          center
          data-id="{{item.id}}"
          catch:click="onSelected"
          wx:for="{{areas.areas}}"
          wx:key="id"
          custom-class="px-30 pt-30 pb-30"
          title-class="text-30 color-black"
          label-class="text-24 color-999 mt-10"
        >
          <view slot="right-icon" class="checkbox {{selectedId === item.id ? 'checked' : ''}}"></view>
        </smart-ui-cell>
      </smart-card>
    </smart-main-provider>
  </smart-i18n-provider>
</view>
