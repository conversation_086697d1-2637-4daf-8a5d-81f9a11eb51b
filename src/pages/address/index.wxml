<wxs src="@wxs/utils.wxs" module="utils" />
<wxs src="@wxs/toCSSVars.wxs" module="computed" />

<view class="page-address smart-page theme-style bg-f6 h-full" style="{{computed.toCSSVars(theme.style)}}">
  <smart-main-provider id="main-provider" theme="theme-style" full-height>
    <smart-i18n-provider id="smart-i18n-provider" sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}">
      <smart-full-loading show="{{loading}}" sqbBridge="{{sqbBridge}}" errors="{{loadingErrors}}" />
      <view class="address-container bg-f6 pt-30">
        <form bind:submit="onSubmit">
          <view class="w-auto bg-white mx-20 rounded-12 overflow-hidden">
            <smart-ui-cell title="{{utils.i18n.t('联系人',i18nData)}}" center custom-class="pd-30" title-class="color-black text-30">
              <input
                placeholder="{{utils.i18n.t('请填写联系人姓名',i18nData)}}"
                disabled="{{disabled}}"
                value="{{currentAddress.userName || ''}}"
                maxlength="10"
                type="text"
                name="userName"
                class="text-30 color-black block pl-0"
                placeholder-class="text-30 color-ccc"
              />
            </smart-ui-cell>
            <smart-ui-divider custom-class="divider-class" />
            <smart-ui-cell name="gender" class="gender" custom-class="pd-30 text-32 color-black" value-class="flex-row flex-right" center>
              <block wx:for="{{GENDERS}}" wx:key="value" wx:for-item="item">
                <view wx:key="value" class="flex-row gender-item flex-center-y color-black">
                  <view class="radio-wrap mr-20" catch:tap="onSelectedGender" data-value="{{item.value}}">
                    <smart-ui-icon class="checked-radio" wx:if="{{gender===item.value}}" name="tick" custom-class="checked-radio" />
                    <view class="radio" wx:else></view>
                  </view>
                  <text class="text">{{item.name}}</text>
                </view>
              </block>
            </smart-ui-cell>
            <smart-ui-divider custom-class="divider-class" />
            <smart-ui-cell title="{{utils.i18n.t('手机号',i18nData)}}" center custom-class="pd-30 text-30 color-black">
              <input
                placeholder="{{utils.i18n.t('请填写联系人手机号',i18nData)}}"
                class="text-30 color-black block pl-0"
                placeholder-class="text-30 color-ccc"
                type="number"
                value="{{phone}}"
                bindinput="onInputPhone"
                name="cellphone"
                controlled
              />
            </smart-ui-cell>
            <block wx:if="{{!isOnlySchool}}">
              <smart-ui-divider custom-class="divider-class" />
              <smart-ui-cell
                title="{{utils.i18n.t('地址',i18nData)}}"
                catch:click="onChooseAddress"
                value="{{location.name || currentAddress.address || utils.i18n.t('请选择收货地址',i18nData)}}"
                isLink
                iconRotate="180"
                custom-class="pd-30"
                title-class="text-30 color-black"
                value-class="text-30 choose-address-value"
                right-icon-class="choose-address"
                center
              ></smart-ui-cell>
            </block>
            <block wx:if="{{(currentAddress.type === 1 || currentAddress.type === undefined) && isInCampus}}">
              <smart-ui-divider custom-class="divider-class" />
              <smart-ui-cell
                title="{{utils.i18n.t('选择区域',i18nData)}}"
                catch:click="onChooseAddress"
                data-jump="{{false}}"
                value="{{currentAddress.campusName || utils.i18n.t('请选择所在学校',i18nData) }}"
                isLink="{{isOnlySchool}}"
                iconRotate="180"
                custom-class="pd-30"
                value-class="text-30 choose-address-value"
                title-class="text-30 color-black"
                right-icon-class="choose-address"
                center
              ></smart-ui-cell>
              <smart-ui-divider custom-class="divider-class" />
              <smart-ui-cell
                title="{{utils.i18n.t('选择楼栋',i18nData)}}"
                isLink="{{useStructuredAddress}}"
                custom-class="pd-30"
                value-class="text-30 choose-address-value"
                title-class="text-30 color-black"
                iconRotate="180"
                right-icon-class="choose-address"
                center
                catch:click="{{useStructuredAddress ? 'onSelectstructAddress' : ''}}"
              >
                <view wx:if="{{useStructuredAddress}}" class="text-30 {{addressName ? 'color-black' : 'color-ccc'}}">{{addressName ? (area + ',' + addressName) : '请选择楼栋'}}</view>
                <input
                  wx:else
                  name="buildingNumber"
                  placeholder="{{utils.i18n.t('请填写宿舍楼/教学楼号',i18nData)}}"
                  placeholder-class="color-ccc text-30"
                  class="text-30 color-black block pl-0"
                  maxlength="20"
                  bindinput="onBuildingNumber"
                  show-count="{{false}}"
                  showCount="{{false}}"
                  value="{{schoolInfoMap['buildingNumber' + currentAddress.campusId] || ''}}"
                />
              </smart-ui-cell>
              <smart-ui-divider wx:if="{{displayHouseNumber}}" custom-class="divider-class" />
              <smart-ui-cell wx:if="{{displayHouseNumber}}" title="{{utils.i18n.t('门牌号',i18nData)}}" custom-class="pd-30" title-class="text-30 color-black" value-class="text-30 color-black" center>
                <input
                  name="houseNumber"
                  showCount="{{false}}"
                  show-count="{{false}}"
                  maxlength="20"
                  class="text-30 color-black block pl-0"
                  placeholder-class="text-30 color-ccc"
                  value="{{schoolInfoMap['houseNumber' + currentAddress.campusId] || ''}}"
                  bindinput="onHouseNumber"
                  placeholder="{{utils.i18n.t('详细地址,例:1号2003室',i18nData)}}"
                />
              </smart-ui-cell>
            </block>
            <block wx:elif="{{!isMerchantAddress}}">
              <smart-ui-divider custom-class="divider-class" />
              <smart-ui-cell title="{{utils.i18n.t('门牌号',i18nData)}}" custom-class="pd-30" title-class="text-30 color-black" center>
                <input
                  name="houseNumber"
                  showCount="{{false}}"
                  show-count="{{false}}"
                  class="color-black text-30 block pl-0"
                  placeholder-class="text-30 color-ccc"
                  value="{{currentAddress.houseNumber || ''}}"
                  placeholder="{{utils.i18n.t('详细地址,例:1号2003室',i18nData)}}"
                  maxlength="20"
                />
              </smart-ui-cell>
            </block>
          </view>
          <smart-ui-button class="w-full" custom-class="save" form-type="submit" hover-class="pointer">{{utils.i18n.t('保存',i18nData)}}</smart-ui-button>
          <smart-ui-button wx:if="{{isEdit}}" class="w-full" custom-class="delete" catch:click="onDel">{{utils.i18n.t('删除',i18nData)}}</smart-ui-button>
        </form>
      </view>
      <!-- 是否删除地址弹窗 -->
      <smart-ui-dialog
        message="{{utils.i18n.t('确认删除该收货地址吗?',i18nData)}}"
        show="{{isConfirm}}"
        catch:close="onCancel"
        catch:confirm="onConfirm"
        showConfirmButton
        showCancelButton
        confirmButtonText="{{utils.i18n.t('确定',i18nData)}}"
        cancelButtonText="{{utils.i18n.t('取消',i18nData)}}"
        custom-class="suggestion-phone"
        confirm-button-class="suggestion-phone-confirm"
        cancel-button-class="suggestion-phone-cancel"
      />
      <!-- 选择楼栋action-sheet -->
      <smart-ui-action-sheet
        show="{{showStructuredAddress}}"
        catch:close="onCloseStructuredAddress"
        zIndex="1000002"
        title="{{utils.i18n.t('请选择楼栋',i18nData)}}"
        rightTitle="{{utils.i18n.t('确定',i18nData)}}"
        right-title-class="text-26 address-action-sheet-right-title-class"
        title-class="px-30 text-32"
        custom-class="address-action-sheet-class"
        catch:click-right-title="onEnsureStructuredAddress"
      >
        <scroll-view scroll-y="{{true}}" class="scroll flex-item px-30 box-border text-28">
          <view wx:for="{{campusAddresses}}" wx:key="index" wx:for-item="areaListItem" wx:for-index="areaListIndex" class="mb-18">
            <view class="text-26 color-666 mb-24 font-regular">{{areaListItem.area}}</view>
            <view class="flex-row flex-wrap">
              <view
                wx:for="{{areaListItem.buildings}}"
                wx:key="code"
                catch:tap="onSelectedArea"
                data-area="{{areaListItem.area}}"
                data-code="{{item.code}}"
                data-name="{{item.name}}"
                class="area-item ellipsis {{item.name.length<6 ? 'limit-6':''}} {{(selectedCode || areaListIndex===0 && areaListItem.buildings[0]['code'])===item.code ? 'area-item-selected':''}}"
              >
                {{item.name}}
              </view>
            </view>
          </view>
        </scroll-view>
      </smart-ui-action-sheet>
    </smart-i18n-provider>
  </smart-main-provider>
</view>
