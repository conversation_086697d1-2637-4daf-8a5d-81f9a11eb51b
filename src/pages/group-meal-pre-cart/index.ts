import { useTrack } from '@utils/helper'
import _ from '@wosai/emenu-mini-lodash'
import Helpers from '@utils/alertHelpers'
import { isWeixin, getTerminalName, getPayway } from '@wosai/emenu-mini-utils'

import {
  StoredCardBehavior,
  ThemeBehavior,
  PayBehavior,
  CartBehavior,
  BaseBehavior,
  RemarkBehavior,
  SvcBehavior,
  EventBehavior,
  BatchBehavior,
  OrderBehavior
} from '@utils/mixins/roundMeal'

import { emenuChannel } from '@utils'
import { updateMkCustomInfoByDiscount } from '@utils/discount'
import { useI18nBehavior, useThemeBehavior } from '@behaviors'
import {
  pageSubmitFirstpayRoundKeys,
  componentUserCountDialogKeys,
  componentBatchListKeys,
  componentCartListKeys,
  componentPaidAmountKeys
} from '@utils/i18n_keys'
import BaseComponent from '@BaseComponent'

BaseComponent({
  options: {
    // 使支付宝小程序支持 lifetimes 功能
    lifetimes: true
  },

  behaviors: [
    useI18nBehavior({
      keys: [
        ...pageSubmitFirstpayRoundKeys(),
        ...componentUserCountDialogKeys(),
        ...componentBatchListKeys(),
        ...componentCartListKeys(),
        ...componentPaidAmountKeys()
      ],
      componentId: 'smart-i18n-provider'
    }),
    useThemeBehavior([], 'group-meal-pre-pay-page'),
    StoredCardBehavior,
    ThemeBehavior,
    PayBehavior,
    CartBehavior,
    BaseBehavior,
    RemarkBehavior,
    SvcBehavior,
    EventBehavior,
    BatchBehavior,
    OrderBehavior
  ],
  properties: {
    config: {
      type: Object,
      value: {}
    }
  },
  td: {
    continueOrder: {
      type: 'throttle'
    },

    placeOrder: {
      type: 'throttle'
    },

    editPeople: {
      type: 'throttle'
    }
  },

  data: {
    componentName: 'page-submit-firstpay-round',
    discount: {},
    cart: {},
    batchLists: [],
    canPlaceOrder: true,
    tableName: '',
    remark: '',
    loaded: false,
    loadingErrors: [],
    showDiscountRow: false,
    canBack: false,
    isWeixin: isWeixin(),
    isWeapp: isWeixin(),
    invalidGoodsList: [],
    paytype: getTerminalName(),
    payway: getPayway(),
    hasDataFlag: false,
    hasCartDataFlag: false,
    goodsCouponCount: 0, // 商品抵用券数量
    memberPointsValue: 0,
    pointsRedeemDetail: null,
    emenuChannel,
    bulletinPayload: {} // 传给合规提示栏的参数
  } as any,
  lifetimes: {
    async attached() {
      const { sqbBridge } = this.data
      const { storeId, merchantId } = this.store
      const serviceType = sqbBridge.getServiceTypes('serviceType')
      this.setData({
        bulletinPayload: {
          merchantId,
          storeId,
          serviceType
        }
      })
      wx.hideShareMenu()
      this.trackIns = useTrack(this)

      this.createOrderPaySvc('api/v4/orders/payfirst/round/addAndPay')
    },

    async ready() {
      await this.initData()
      this.initPageStyle()
      // 更新营销组件主题
      this.updateMkssTheme()
      this.$track('SmMpPageDisplay', {})
    }
  },

  methods: {
    ...Helpers,

    // 积分弹窗点击确定
    onPointsComfirm() {
      this.setData({
        isShowPointsPopup: false
      })
      this.refreshData()
    },
    async initData() {
      try {
        // this.$loading('加载中')
        await this.getStoreCardInfo()
        emenuChannel.setMkCustomInfo({ member_points: this.data.memberPointsValue })
        await this.refreshData()
        // this.$clearToast()

        this.setLoaded()
      } catch (e) {
        // this.$clearToast()
        this.setLoaded()
        console.error(e)
        this.setData({ loadingErrors: [{ error: e?.toString() }] })
      }
    },

    setLoaded() {
      let { cart, batchLists } = this.data
      this.setData({
        loaded: true,
        hasCartDataFlag: cart && cart.records && cart.records.length,
        hasDataFlag:
          (cart && cart.records && cart.records.length) || (batchLists && batchLists.length)
      })
    },

    async refreshData() {
      await this.getData()
      this.setLoaded()
    },

    async getData() {
      let body = {
        table_id: this.terminal.tableId,
        pay_way: this.data.payway,
        mk_custom_info: emenuChannel.getMkCustomInfo()
      }
      let res = await this.orderSvc.getOrderOrBatchDetail(body)

      let { items, goods_batch_infos, redeem, cart, need_pay_amount } = res
      const disableRedeem = _.get(res, 'disable_redeem')

      // 获取积分抵扣项的数据,传给积分组件
      const _pointRedeem =
        _.find(_.get(redeem, 'redeem_details', []), item => item.sub_type === 35) || {}

      if (cart && cart.records && cart.records.length) {
        cart.records = cart.records.filter(item => !!item.num)
      }

      let _goodsLists = this.processOrderGoodsItems(items)
      let goodsLists = this.processOrderGoodsWidthDiscount(
        _goodsLists,
        redeem || {},
        goods => goods.__discount_tag
      ) // 后端返回了tag_name则用后端的

      let batchLists = this.aggrateRoundMealBatchLists(goodsLists, goods_batch_infos)
      updateMkCustomInfoByDiscount(redeem)
      const { cart: _cart, discount: _discount } = this.processCartGoodsWidthDiscount(cart, redeem)
      this.setData({
        cart: _cart || {},
        batchLists,
        discount: _discount || {},
        paidAmount: need_pay_amount,
        disableRedeem,
        pointsRedeemDetail: _pointRedeem
      })
    },

    // 兼容先吃的围餐，一直在check购物车
    // 先付的围餐，接口改变，这里需要重新拉下
    async afterCartChanged() {
      this.refreshData()
    },

    onShow() {
      this.syncRemark()
    },

    async placeOrder() {
      let { sn, sqbBridge, cart, remark, payway, discount, paidAmount, no_check_must_order_goods } =
        this.data

      let body: Record<any, any> = {
        store_id: this.store.storeId,
        table_id: this.terminal.tableId,
        pay_way: paidAmount === 0 ? getPayway() : payway,
        type: 'PAY_FIRST_TABLE_ORDER',
        _is_zero_pay: false
      }

      body.items = _.map(cart.records, record => {
        return {
          item_uid: record.id,
          number: record.num
        }
      })

      let user = await sqbBridge.getMiniProgramUser()

      body.user_icon = user.avatarUrl
      body.user_name = user.nickName

      if (sn) body.order_sn = sn
      if (remark) body.remark = remark

      let { terminal_sn } = this.terminal || {}

      if (terminal_sn) {
        body.terminal_sn = terminal_sn
      }

      // 增加_is_zero_pay的参数作为支付接口返回参数的参考依据
      if (paidAmount === 0) {
        body._is_zero_pay = true
      }

      // 不检查必点商品
      if (no_check_must_order_goods) {
        body.check_must_order_exist = false
      }

      // 优惠相关请求接口
      body.total_discount = _.get(discount, 'total_discount', 0)
      body.redeem_digest = _.get(discount, 'redeem_digest')
      body.mk_custom_info = emenuChannel.getMkCustomInfo()

      this.call('track', 'SmMpOrderPayClick', { click_name: '下单并支付' })

      this.requestPay({
        body,
        hooksMap: {
          orderCreate: () => {
            this.$loading('')
          },
          orderFail: result => {
            console.error(`##order fail is`, result)

            // 锁取消，防止超时的时候，单子被锁了，无法进行再支付
            this.orderSvc.payLockCancel({ table_id: this.terminal.tableId })
            this.$clearToast()
            this.processOrderFail(result, {
              whenErrorCodeIsConscurrentConfirmOkCallback: data => {
                let { client_sn } = data
                this.redirect('page-firstpay-round-batch-list', { client_sn })
              },
              whenErrorCodeIsConscurrentConfirmCancelCallback: data => {
                this.redirect('page-home')
              },
              whenErrorCodeIsNoMustOrderGoodsCallback: data => {
                // 50015:未选择必选品,此异常码执行回调的时候去点单首页
                this.redirect('page-home', { fromPaySubmitOrder: 'batchNoMustOrderGoods' })
              },
              // 去调整
              whenErrorCodeIsMustGoodsExistsConfirmOkCallback: data => {
                this.setData({
                  no_check_must_order_goods: true
                })
                // 设置可编辑
                this.orderSvc
                  .setCartMustOrderEditable({ table_id: _.get(this.terminal, 'tableId') })
                  .then(() => {
                    this.refreshData()
                  })
              },
              // 继续下单
              whenErrorCodeIsMustGoodsExistsConfirmCancelCallback: () => {
                this.setData({
                  no_check_must_order_goods: true
                })
                this.placeOrder()
              },
              whenErrorIsOtherCallback: data => {
                let { client_sn } = data
                if (client_sn) {
                  this.redirect('page-firstpay-round-batch-list', { client_sn })
                } else {
                  this.refreshData()
                }
              }
            })
          },

          orderSuccess: () => {
            this.$clearToast()
          },

          paySuccess: (result, order) => {
            console.error(`pay success is `, result, order)
            let { client_sn } = order
            this.redirect('page-firstpay-round-batch-list', { client_sn: client_sn })
          },
          payFail: result => {
            console.error(`###payfail res is`, result)
            // TODO: 检查
            this.$toast(this.t('支付失败'))
            this.refreshData()
          },
          payCancel: result => {
            let { client_sn } = result
            this.orderSvc.payLockCancel({ table_id: this.terminal.tableId })
            this.redirect('page-firstpay-round-batch-list', { client_sn })
          }
        }
      })
    }
  }
})
