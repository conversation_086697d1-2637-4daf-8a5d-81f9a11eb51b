@import (css) '@styles/flex';
@import (css) '@styles/common';
@import (css) '@styles/theme';

:host {
  height: 100%;
}

.page-content {
  height: 100%;
  --main-provider-height: 100%;
}

.smart-package-goods-detail {
  --basic-goods-item-height: 100%;

  .combo-default-img {
    --image-width: 100%;
    --image-height: 596px;
    display: block;
  }

  .selected-goods-container {
    .selected-goods-item {
      &:last-child {
        margin-bottom: 0;
      }
    }
  }

  .combo-counter-class {
    /* prettier-ignore */
    --icon-size: 18PX;
    /* prettier-ignore */
    --icon-font-size: 18PX;
    justify-content: flex-end;
    gap: 0;
    --counter-count-margin-x: 12px;
  }

  .goods-detail-icon {
    height: 50px;
    --icon-background-color: rgba(0, 0, 0, 0.3);

    .icon {
      width: 50px;
      height: 50px;
    }

    .share-icon-class {
      border: none;
      padding: 0;
      --button-default-background-color: transparent;
    }
  }

  .sub-title {
    font-size: 26rpx;
    font-weight: 400;
    color: #666666;
  }

  .hidden-holder {
    position: absolute;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    box-sizing: border-box;
    padding: 0;
    border: none;
    border-radius: 0;
    background: transparent !important;
    color: transparent !important;
    z-index: 99;

    &.hide {
      width: 0;
      height: 0;
    }

    &::after,
    &::before {
      display: none !important;
      border: none !important;
    }
  }

  &.container {
    width: 100%;
    display: flex;
    flex-direction: column;
    // prettier-ignore
    height: 100%;
    // position: fixed;
    // prettier-ignore
    // top: 88PX;
  }

  .combo-image-swiper {
    height: 596px;
  }

  .goods-list-container {
    grid-template-columns: repeat(3, 1fr);

    &::after {
      content: '';
      display: block;
      width: 220px;
      height: 1px;
    }

    .goods-item-container {
      width: 220px;
      position: relative;

      .goods-info-container {
        height: calc(100% - 164px);
      }

      .combo-goods-name {
        min-height: 74px;
        margin-bottom: 4px;
        word-break: break-all;
      }

      .combo-goods-price {
        color: var(--secondary__color, #f24839);
      }

      .combo-goods-out-of-stock {
        color: #ff4d4d;
      }

      .combo-goods-footer {
        // margin-top: -4px;
        // position: absolute;
        // bottom: 12px;
        // right: 12px;
      }
    }
  }

  .goods-item-class {
    --image-width: 220px;
    --image-height: 165px;
    --basic-goods-item-image-radius: 0;
    --goods-counter-justify-content: flex-end;
    border: 2rpx solid #ebebeb;
    border-radius: 12rpx;
    min-height: 288px;
    // 加减号
    --goods-item-counter-icon-plus-color: var(--primary-second-foreground-color);
    --goods-item-counter-icon-minus-color: var(--primary-second-color);
    --goods-item-counter-icon-border-color: var(--primary-second-color);
    --goods-item-counter-icon-background-color: var(--primary-second-color);
    --goods-item-counter-icon-plus-background-color: var(--primary-second-color);
    --goods-item-minus-icon-border-color: var(--primary-second-foreground-color);
    --goods-item-minus-icon-color: var(--primary-second-foreground-color);
    --goods-title-webkit-box: -webkit-box;
    --goods-title-font-size: 26px;
    --goods-title-line-height: 1.2;
    --goods-title-vertical-align: top;

    width: 220px;
    position: relative;

    &.goods-item-class-selected {
      border-radius: 12rpx;
      border: 2rpx solid var(--primary__color);
      background-color: var(--primary-selected__color);
    }
  }

  .no-image-mode {
    min-height: 135px;
    height: 100%;
  }

  .must-goods-item-class {
    --goods-title-max-width: 476px;
    --image-width: 80px;
    --image-height: 80px;
    --image-radius: 10rpx;
    --goods-title-font-size: 28px;
    --goods-title-line-height: 1.2;
    --goods-title-vertical-align: top;
    // --goods-item-info-content-min-height: 80px;
    // --goods-item-height: 80px;

    --goods-title-line-clamp: 1;
    --goods-desc-max-width: 217px;

    --goods-title-margin-bottom: 0;
    gap: 20px;
  }

  .must-goods-item-desc {
    --goods-desc-max-width: 446px;
    flex: 0 0 446rpx;
    white-space: normal;
  }

  .desc-out-of-stock {
    color: #666;
    opacity: 0.5;
  }

  .combo-goods-description {
    color: #999;
    font-size: 28px;
    padding: 30px 30px 0;
    background: #fff;
  }

  .combo-card {
    --card-padding-top: 30px;
    --card-padding-bottom: 30px;
    --card-padding-left: 30px;
    --card-padding-right: 30px;
    --card-background-color: #fff;
    --card-title-margin-bottom: 0;
    --card-title-font-size: 30px;
    --card-title-color: #000;
    display: grid;
    // flex-direction: column;
    gap: 20px;

    &.optional-goods-card {
      margin-top: 16px;
    }

    // &.last-combo-card {
    //   --card-padding-bottom: 220px;
    // }

    .must-goods-options {
      width: auto;
      min-width: 80px;
      position: absolute;
      top: 0;
      right: 0;
    }

    .must-goods-out-of-stock {
      color: #ff4d4d;
      min-width: 80px;
    }

    .goods-attached-info {
      width: 446px;
    }
  }

  .header.icons {
    position: absolute;
    top: 36px;
    right: 30px;
    display: flex;
    z-index: 10;

    &--icon {
      width: 50px;
      height: 50px;
      margin-left: 30px;
      border-radius: 50%;
      background: rgba(33, 33, 33, 0.41);
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 0;
    }
  }
}

.combo-page {
  width: 100%;
  height: 100%;
}

.combo {
  width: 100%;
  background: #f6f6f6;
  padding-bottom: 172px;
  // box-sizing: border-box;

  &-header {
    position: relative;

    &__icon {
      width: 50px;
      height: 50px;
      border-radius: 50%;
      background-color: rgba(0, 0, 0, 0.3);
      font-size: 0;

      &s {
        position: absolute;
        top: 20px;
        right: 30px;
      }
    }
  }

  &-main {
    padding: 0 0 24px;
  }

  &-desc {
    line-height: 42px;
    padding: 28rpx 30rpx 5rpx;
    background: #fff;
    white-space: pre-wrap;
  }

  &-group {
    &:not(:last-child) {
      border-bottom: 1px solid #efefef;
    }
  }

  &-footer {
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}

.footer-wrap-block {
  // height: calc(176px + env(safe-area-inset-bottom) / 2);
}

.footer-wrap-fixed {
  box-shadow: 0 -4rpx 8rpx 0 rgba(0, 0, 0, 0.08);
  position: fixed;
  z-index: 999;
  border-radius: 32px 32px 0px 0px;
  bottom: 0;
  left: 0;
  width: 100%;
  background: #fff;
  overflow: hidden;

  &.hide {
    z-index: -999;
  }
}
