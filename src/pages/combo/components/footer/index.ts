import BaseComponent from '@BaseComponent'

BaseComponent({
  externalClasses: ['custom-class'],
  options: {
    multipleSlots: true
  },
  properties: {
    buttonText: {
      type: String,
      value: '加入购物车'
    },
    disabledBtnText: {
      type: String,
      value: '抢光了'
    },
    disabled: {
      type: Boolean,
      value: false
    },
    showFooterFlag: {
      type: Boolean,
      value: true
    },
    sumPrice: {
      type: Number,
      value: 0
    },
    origin_amount: {
      type: Number,
      value: 0
    }
  },
  // @ts-ignore
  methods: {
    onSubmit() {
      this.$emit('submit')
    }
  }
})
