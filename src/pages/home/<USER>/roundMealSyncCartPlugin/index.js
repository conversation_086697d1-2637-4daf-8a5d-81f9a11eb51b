import _ from '@wosai/emenu-mini-lodash'
import Timer from '@utils/timer'
import { CART_CHANGE_EVENT } from '@utils'
import { getQueryOpts } from '@utils/helper'
import { CartService } from '@wosai/emenu-mini-services'

export default function apply(ctx) {
  let handler = null
  let cartSrv = null
  let cartData = ''
  let { sqbBridge } = this.data
  function onTick() {
    const bus = ctx.bus
    let serviceType = sqbBridge.getServiceTypes(`serviceType`)

    if (serviceType !== 2) return Promise.resolve()
    return cartSrv
      .fetchCartData({
        service_type: serviceType
      })
      .then(data => {
        if (cartData && _.isEqual(data, cartData)) {
          return
        }
        cartData = _.cloneDeep(data)
        ctx.bus.emit(CART_CHANGE_EVENT, data)
        return
      })
  }

  function createService() {
    let options = getQueryOpts.bind(ctx)()
    cartSrv = new CartService(options)
  }

  function createTimer() {
    handler = ctx.roundMealSyncTimer
    if (!handler) {
      handler = new Timer({
        tick: 3000,
        onTick: onTick
      })
      ctx.roundMealSyncTimer = handler
    }
  }
  function destroy() {
    if (handler) {
      handler.stop()
      ctx.roundMealSyncTimer = null
      handler = null
    }
  }
  function run(action) {
    const startAt = Date.now()

    if (action === 'end') {
      destroy()
      return
    } else if (action === 'start') {
      cartData = ''
      createService()
      createTimer()
      handler.start()
    }
    const endAt = Date.now()
    // ctx.call('sls', 'INFO', { type: 'roundMealSyncCartPlugin', duration: endAt - startAt, startAt, endAt });
  }
  ctx.hooks.roundMealSyncCart.tap('roundMealSyncCart', run)
}
