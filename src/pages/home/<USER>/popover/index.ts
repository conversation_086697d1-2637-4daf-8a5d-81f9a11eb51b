import BaseComponent from '@BaseComponent'
import _ from '@wosai/emenu-mini-lodash'
import { AdsenseService } from '@wosai/emenu-mini-services'
import { oss } from '@wosai/emenu-mini-utils/lib/cdn'
import { operationJump } from '@utils/business'

BaseComponent({
  properties: {
    payload: {
      type: Object,
      value: {}
    },
    storeId: {
      type: String
    },
    serviceType: {
      type: Number
    },
    requestType: {
      type: String,
      value: ''
    }
  },
  lifetimes: {
    attached() {
      this.__key = `ad_${this._generateUniqueKey()}`
      console.log('组件已创建，唯一标识：', this.__key)
      this.adSvc = new AdsenseService()
    },
    ready() {
      const { payload, requestType } = this.data
      const merchantId = _.get(payload, 'merchantId')
      const storeId = _.get(payload, 'storeId')

      if (merchantId && storeId && requestType) {
        this.fetchPopoverContent(merchantId, storeId, requestType)
      }
    }
  },
  data: {
    content: null,
    showPopup: true
  },
  methods: {
    /**
     * 获取弹窗AOP相关数据
     * @param {*} account_id
     * @param {*} campus_id
     */
    fetchPopoverContent(merchantId, storeId, requestType) {
      const { sqbBridge, serviceType } = this.data
      let isMerchant = false

      if (_.isFunction(sqbBridge.getContext)) {
        const { platform } = sqbBridge.getContext() || {}
        isMerchant = _.get(platform, 'type') === 'merchant' ? true : false
      }

      this.adSvc
        .fetchAopResource({
          requestType,
          merchantId,
          serviceType,
          storeId,
          sole: isMerchant ? 2 : 1
        })
        .then(data => {
          const lists = _.get(data, 'content') || []
          const popup_list = _.first(lists)
          if (popup_list) {
            popup_list.image = oss(_.get(popup_list, 'pictureUrl'), {
              mode: 'lfit',
              format: 'webp',
              width: 300,
              quality: 85
            })
            popup_list.url = _.get(popup_list, 'jumpUrl')
            const content = {
              ...popup_list
            }

            this.smMpUIViewDisplay(content)

            this.setData({ content })
            this.requestComponentShow(1)
          } else {
            this.requestComponentShow(0)
          }
        })
        .catch(e => {
          this.requestComponentShow(0)
        })
    },
    /**
     * 点击弹窗跳转，为防止用户多次连续点击导致埋点事件误差，增加了debounce
     */
    onClick: _.debounce(function () {
      const { content, sqbBridge } = this.data
      const url = _.get(content, 'url')
      const title = _.get(content, 'publishName')
      if (url) {
        this.setData({ showPopup: false })
        sqbBridge.track('SmMpUIViewClick', {
          activity_title: title,
          activity_url: url,
          sm_page_name: '点单首页',
          sm_uiview_name: '点单首页平台运营弹窗'
        })
        operationJump.call(this, { url })
      }
    }, 500),
    /**
     * 埋点
     */
    smMpUIViewDisplay(content) {
      const { sqbBridge } = this.data
      sqbBridge.track('SmMpUIViewDisplay', {
        activity_title: _.get(content, 'publishName'),
        activity_url: _.get(content, 'url'),
        sm_page_name: '点单首页',
        sm_uiview_name: '点单首页平台运营弹窗'
      })
    },
    /**
     * 弹窗关闭
     */
    onClose() {
      this.setData({ showPopup: false })
      this.$emit('close')
    },
    /**
     * 弹窗点击
     */
    onClickPopup() {
      this.onClick()
      //this.onClose()
    },
    _generateUniqueKey() {
      return Date.now().toString(36) + Math.random().toString(36).substr(2, 5)
    },
    //result:1成功，0失败
    requestComponentShow(result = 1) {
      this.triggerEvent('requestShow', {
        result,
        componentKey: this.__key,
        timestamp: Date.now()
      })

      console.log('已发送显示请求：', this.__key)
    }
  }
})
