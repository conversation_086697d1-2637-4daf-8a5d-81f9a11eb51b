@import (css) '@styles/flex';
@import (css) '@styles/common';

.recommend {
  // position: relative;
  // // background-image: linear-gradient(122deg, #ffd896 0%, #fff3d6 100%);
  // background-repeat: no-repeat;
  // top: 26px;
  // z-index: 1;
  // margin: 0 31px 27px;
  // border-radius: 20px;

  &-scroll {
    --_recommend-goods-width: 194px;
    --_recommend-goods-height: 348px;
    --_recommend-goods-image-height: 194px;
    margin: 0 5px 20px 20px;
    width: calc(100% - 20px);
    transform-origin: 100% 50%;
    animation: scaleY 300ms ease-in-out;
    overflow-y: initial;
    .recommend-badge-class {
      --badge-icon-size: 26px;
      --badge-icon-font-size: 26px;
      --badge-info-font-size: 18px;
      --badge-icon-border-width: 1px;
      --badge-icon-size-more: 30px;
      --badge-icon-border-radius-more: 12px;
      transform: translate(40%, -64%) !important;
    }

    .plus-icon-class {
      --icon-size: 36px !important;
      --icon-font-size: 36px !important;
    }

    .recommend-goods-item {
      --_radius: 14px;
      --goods-price-original-size: 18px;
      --goods-title-max-width: var(--_recommend-goods-width);
      --goods-height: var(--_recommend-goods-height);
      --goods-content-padding: 0 8px 12px 8px;
      --goods-row-gap: 2px;
      --goods-title-font-size: 26px;
      --goods-title-webkit-box: -webkit-box;
      --goods-width: var(--_recommend-goods-width);
      --goods-bg-color: #fff;
      // --goods-title-line-clamp: 2;
      --goods-box-shadow: 0rpx 7rpx 17rpx 0rpx rgba(0, 0, 0, 0.09);
      --goods-image-height: var(--_recommend-goods-image-height);
      --goods-image-width: var(--_recommend-goods-width);
      --goods-tag-font-size: 20px;
      --goods-border-radius: var(--_radius) !important;
      --goods-image-radius: var(--_radius) var(--_radius) 0 0 !important;
      --goods-footer-margin-top: 0 !important;
      --goods-header-flex: 1 !important;
      --goods-discount-gap: auto;
      --goods-title-line-height: 1.4;
      --goods-second-text-size: 18px;
      max-height: 350px;
      margin-right: 20px;
    }

    .recommend-goods-image {
      /* prettier-ignore */
      --image-height: var(--_recommend-goods-image-height);
      /* prettier-ignore */
      --image-width: var(--_recommend-goods-width);
    }

    // background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, #ffffff 50%);
    &::after {
      content: '';
      display: table;
      height: 0;
      visibility: hidden;
    }
  }

  &-tag {
    // width: 100%;
    // height: 100%;
    margin: 22px 0 14px 24px;
  }

  .big-zan {
    position: absolute;
    width: 122px;
    height: 127px;
    right: 13px;
    top: 7px;
  }

  &-goods-wrap {
    box-sizing: border-box;
    //padding: 0 0 0 20px;
    width: max-content;
    border-radius: 20px;
  }
}

@keyframes scaleY {
  from {
    transform: translateX(100px);
    opacity: 0;
  }

  to {
    transform: translateX(0);
    opacity: 1;
  }
}
