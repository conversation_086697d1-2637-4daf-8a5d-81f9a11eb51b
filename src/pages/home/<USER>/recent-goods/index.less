@import (css) '@styles/flex';
@import (css) '@styles/common';

/* prettier-ignore */
.recent-goods {
  --smart-counter-min-width: auto;

  .pic-wrap {
    width: 48px;

    .recent-goods-pic {
      --image-width: 48px;
      --image-height: 48px;
      --image-radius: 6px;
      display: block;
    }
  }

  .custom-counter-class {
    --icon-font-size: 38px;
    --icon-size: 38px;
  }

  .counter-icon-class {}

  /* prettier-ignore */
  height: 36PX;
  /* prettier-ignore */
  padding: 0 10PX;
  /* prettier-ignore */
  font-family: 'Lato',
  PingFang SC,
  Helvetica Neue,
  Arial,
  sans-serif;

  &-name {
    font-size: 28px;
    color: #000000;
    margin-right: 5px;
    margin-left: 13px;
  }

  .smart-price-class {
    &__recent {
      margin-right: 10px;
      --display-price-font-size: 28px;
      --display-currency-font-size: 18px;
      --line-price-font-size: 24px;
    }
  }

  // &-price {
  //   margin-right: 10px;
  //   align-items: baseline;
  // }

  &-display-price {
    font-size: 28px;
    font-weight: bold;
    color: #000;
    margin-right: 4px;

    &::before {
      font-size: 18px;
    }
  }

  &-original-price,
  &-suffix {
    font-size: 16px;
    font-weight: 500;
    color: #999999;
  }

  &-original-price {
    text-decoration: line-through;

    &:before {
      content: '¥';
      font-size: 90%;
    }
  }

  .badge-wrap {
    position: absolute;
    right: -16px;
    top: -24px;

    .recent-badge {
      --badge-info-font-size: 24px;
      --badge-icon-size: 32px;
      --info-size: 32px;
      --info-font-weight: 500;
      --badge-icon-border-width: 1px;
      --badge-icon-size-more: 46px;
      --badge-icon-border-radius-more: 16px;
    }
  }
}
.recent-goods:first-child {
  margin-top: 10px;
}
