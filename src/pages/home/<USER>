import { setDiscountCtx, updateMkCustomInfoByDiscount } from '@utils/discount'
import { onSwitchLogin } from '@utils/userSwitch'
import {
  aliAuthGetPhone,
  canSubmitOrder,
  checkSession,
  getDataset,
  handleHashKey,
  initAuthUserData,
  isPayFirstRoundMeal,
  isRoundMeal,
  onAddSpuToCart,
  onAddToCart,
  onBindFocusClick,
  onCancelCallback,
  onEnterCallback,
  selectUserCountCallback,
  transformGoods,
  weixinAuthPhoneTimeRecord,
  weixinAuthUserTimeRecord,
  calcRemainNumAndOutOfStock,
  useLocalCart,
  getStatusBarHeight,
  createImageDisplayConfig
} from '@utils/helper'
// import useBusinessCheck from '@utils/useBusinessCheck'
// @ts-ignore
import _ from '@wosai/emenu-mini-lodash'

import {
  ActivityService,
  AdsenseService,
  CartService,
  CashierService,
  GoodsService,
  OrderService,
  SlsLogService,
  StoreService,
  TableService
  // @ts-ignore
} from '@wosai/emenu-mini-services'
// @ts-ignore
import {
  ECHO,
  isAlipay,
  isWeixin,
  shouldUseCache,
  StorageUtils,
  StringUtils
} from '@wosai/emenu-mini-utils'
import CONFIG from '@wosai/emenu-mini-config'
import {
  makeShareOptions,
  getDistance,
  getPageUrl,
  emenuChannel,
  requestPolicyWithBridge,
  CART_CHANGE_EVENT
} from '@utils'
import { callPlugin, HooksManager } from './utils/HooksManager'
import {
  checkShareGoods,
  countDistance,
  createEventBus,
  offEventBus,
  fetchLatestOrder,
  setCanBack,
  setDataMealType,
  setDataPlatform,
  setDataSubmitText,
  setMethodCtx,
  setNavigation,
  slsTerminal,
  toSimplePages,
  uploadSLSLog
} from './utils/Methods'
import { TPayload } from '@types'
import NetworkSpeed from './utils/networkSpeed'
// @ts-ignore

import { BaseMixins } from '@utils/mixins'
import { BadgeStore, CartStore, HomeStore, StoreConstants, StoreHelper } from '@sqb/smart-mp-stores'
import AlertHelpers from '@utils/alertHelpers'
import {
  useThemeBehavior,
  useAopBehavior,
  useI18nBehavior,
  useRenderBehavior,
  usePerformanceBehavior,
  useReTailBehavior,
  useStoreHelperBehavior,
  useBusinessCheckBehavior
} from '@behaviors'
import {
  pageHomeKeys,
  componentGoodsIemKeys,
  componentMerchantDetailKeys,
  componentOrderMessageKeys,
  componentGoodsDetailKeys,
  componentSubmitBarKeys,
  componentUserCountDialogKeys
} from '@utils/i18n_keys'
import { post } from '@utils/request'
import BaseComponent from '@BaseComponent'

const {
  AUTH_EXPIRE_TIME,
  DISCOUNT_ICON,
  HORIZONTAL,
  HOT_SALE_ICON,
  MCC_MUST_ORDER_ENABLE,
  MCC_RECOMMEND_SHOW_WAY,
  PLUGIN_HOME_PATH,
  RECENT_ICON,
  RECOMMEND_ICON,
  RECOMMEND_TAG_IMAGE,
  VERTICAL,
  JJZ_ORDER_HOME_AD_PID,
  SQB_ORDER_HOME_AD_PID,
  AOP_WX_HOME_FLOAT_CODE,
  AOP_MY_HOME_FLOAT_CODE,
  SUB_PAY_WAY,
  MCC_GOODS_DISPLAY_MODE,
  MY_CAMPUS_APP_ID,
  TAKE_OUT_ORDER,
  MCC_PRODUCT_IMAGE_DISPLAY_SIZE
} = CONFIG

const {
  // HEADER_BAR_HEIGHT,
  STATUS_BAR_HEIGHT,
  STORE_DISTANCE_TO_TOP,
  CATEGORY_NAME_HEIGHT,
  GOODS_ITEM_HEIGHT,
  GOODS_ITEM_MARGIN_BOTTOM,
  RECOMMEND_CAT_ID,
  LARGE_GOODS_ITEM_HEIGHT,
  LARGE_GOODS_ITEM_MARGIN_BOTTOM
} = StoreConstants

type THomePayload = Partial<TPayload & { serviceTypeChanged: boolean }>
type TServiceTypeList = TPayload['serviceTypes']['serviceTypeList']
type GoodsLayoutConfig = {
  imgWidth: number
  imgHeight: number
  layout: 'horizontal' | 'vertical'
  itemBottom: number
  minItemHeight: number
  customStyle: Record<string, string>
}

const TABLE_SCENES = ['t', 'b', 'c']
const { storage } = StorageUtils
// storage('headerBarHeight', HEADER_BAR_HEIGHT)
// 商品列表是否滑动
let prevScrolled = false
// let prevCategoryScrollTo = 0;
// @ts-ignore
const isPackageType = spu_type => spu_type === 'PACKAGE'
const { camelCase } = StringUtils
const systemInfo = wx.getSystemInfoSync()
const menuButtonInfo = wx.getMenuButtonBoundingClientRect()
const headerBarPaddingRight = systemInfo.windowWidth - menuButtonInfo.left + 14
const HEADER_BAR_HEIGHT = getStatusBarHeight()
// 特殊商品分类配置
const SPECIAL_CATEGORIES = {
  recent: {
    name: '点过',
    type: 'text',
    icon: RECENT_ICON,
    // @ts-ignore
    condition: show_way => show_way === VERTICAL
  },
  recommend: {
    name: '推荐',
    type: 'pic',
    icon: RECOMMEND_ICON,
    pic: 'https://smart-static.wosaimg.com/themes/default/iconRecommend.webp',
    // @ts-ignore
    condition: show_way => show_way === VERTICAL
  },
  hotSale: {
    name: '热销',
    type: 'pic',
    icon: HOT_SALE_ICON,
    pic: 'https://smart-static.wosaimg.com/themes/default/iconHotSale.webp',
    condition: () => true
  },
  discount: {
    name: '优惠',
    type: 'text',
    icon: DISCOUNT_ICON,
    condition: () => true
  }
}

// @ts-ignore
function updateServiceTypesData(payload: THomePayload, serviceTypeName, from, cb = _.noop) {
  const serviceTypes = _.pick(payload.serviceTypes, [
    'serviceType',
    'currentServiceTypeIndex',
    'selectServiceTypeShow',
    'serviceTypeName',
    'serviceTypeList'
  ])

  const activeItem = _.find(_.get(serviceTypes, 'serviceTypeList'), { active: true })
  // 后端默认选中
  let backendSelectServiceTypeItem = null
  if (activeItem) {
    backendSelectServiceTypeItem = _.cloneDeep(activeItem)
  }

  // @ts-ignore
  function update(_serviceTypeName) {
    // @ts-ignore
    _.forEach(serviceTypes, (val: TServiceTypeList, key) => {
      if (key === 'serviceTypeList') {
        let selectServiceTypeShow = false
        _.forEach(val, (serviceTypeItem, idx) => {
          serviceTypeItem['active'] = false
          // 确定serviceTypeName 不需要弹框
          if (serviceTypeItem.serviceTypeName === _serviceTypeName) {
            serviceTypeItem['active'] = true

            _.merge(serviceTypes, {
              serviceType: serviceTypeItem.serviceType,
              currentServiceTypeIndex: idx,

              serviceTypeName: serviceTypeItem.serviceTypeName
            })

            // 如果当前是扫一扫，则需要弹框
            if (serviceTypeItem['action'] === 'scan') {
              selectServiceTypeShow = true
            }

            _.merge(serviceTypes, { selectServiceTypeShow })
          }
        })
      }
    })
  }

  update(serviceTypeName)

  // 没有选中， 且是校园
  const _activeItem = _.find(_.get(serviceTypes, 'serviceTypeList'), { active: true })

  if (!_activeItem) {
    let serviceTypeNameToBeUpdated
    // 如果后端有默认选中
    if (backendSelectServiceTypeItem) {
      serviceTypeNameToBeUpdated = backendSelectServiceTypeItem.serviceTypeName
    } else {
      // 如果是校园
      if (from === 'campus') {
        // 或者使用自取或者外卖
        serviceTypeNameToBeUpdated = _.get(
          _.find(_.get(serviceTypes, 'serviceTypeList', []), { serviceType: 1 }),
          'serviceTypeName'
        )
      } else {
        // 非校园选中第一个
        serviceTypeNameToBeUpdated = _.get(serviceTypes, 'serviceTypeList[0].serviceTypeName')
      }
    }

    if (serviceTypeNameToBeUpdated) {
      update(serviceTypeNameToBeUpdated)
    }
  }

  cb(serviceTypes)
}

// 收集各个节点的耗时
let end: number | null = 0
const PERFORMANCE_DATA = {}

let useCached = false
// 商品显示模式
const defaultGoodsDisplayMode = 0 // list
const GOODS_DISPLAY_MODE_MAP: Record<number, string> = {
  0: 'list',
  1: 'grid',
  2: 'large-list'
}
const ALIPAY_MARKETING_PLUGIN_ID = '2021003142617780'
let alipayPluginTimer: number | null = null

const home_default_aop_track_params = { placement: '点单页', sm_uiview_name: '活动banner' }

const LAYOUT_CONFIG = {
  LIST: 0,
  GRID: 1,
  LARGE_LIST: 2
} as const

const goodsLayoutConfigs: Record<number, GoodsLayoutConfig> = {
  [LAYOUT_CONFIG.LIST]: {
    // 列表模式
    imgWidth: 79,
    imgHeight: 79,
    layout: 'horizontal',
    customStyle: {},
    itemBottom: GOODS_ITEM_MARGIN_BOTTOM,
    minItemHeight: GOODS_ITEM_HEIGHT
  },
  [LAYOUT_CONFIG.GRID]: {
    // 网格模式
    imgWidth: 175,
    imgHeight: 133,
    layout: 'vertical',
    customStyle: {},
    itemBottom: GOODS_ITEM_MARGIN_BOTTOM,
    minItemHeight: GOODS_ITEM_HEIGHT
  },
  [LAYOUT_CONFIG.LARGE_LIST]: {
    // 大图模式
    imgWidth: 514,
    imgHeight: 289,
    layout: 'vertical',
    customStyle: {
      '--goods-item-min-height': '289rpx',
      '--goods-item-min-width': '514rpx',
      '--goods-image-radius': '12rpx',
      '--goods-sales-info-layout': 'column',
      '--goods-sales-info-justify': 'space-between',
      '--_goods-footer-margin-top': '20rpx !important'
    },
    itemBottom: LARGE_GOODS_ITEM_MARGIN_BOTTOM,
    minItemHeight: LARGE_GOODS_ITEM_HEIGHT
  }
}

// const options = ;
BaseComponent({
  // @ts-ignore
  options: { lifetimes: true },
  // @ts-ignore
  // mixins: [storeHelper(), performance(), render(), BaseMixins, AopMixins, RetailMixins],
  behaviors: [
    useThemeBehavior(
      [
        '1a2b3c4d5e6f', // 头部
        '1w2x3y4z5a6b', //  推荐
        '1y2z3a4b5c6d', // submitBar
        '3m2n1o0p9q8r', // 商品
        '7l7kmqaopfpl', //服务类型弹窗
        '7q6r5s4t3u2v', // 服务类型按钮
        '7s6t5u4v3w2x', // 推荐商品
        '9g8h7i6j5k4l' // 商品分类
      ],
      'home-page'
    ),
    BaseMixins,
    usePerformanceBehavior(),
    useRenderBehavior(),
    useAopBehavior(),
    useStoreHelperBehavior(),
    useReTailBehavior(),
    useI18nBehavior({
      // @ts-ignore
      keys: [
        ...pageHomeKeys(),
        ...componentGoodsIemKeys(),
        ...componentMerchantDetailKeys(),
        ...componentOrderMessageKeys(),
        ...componentGoodsDetailKeys(),
        ...componentSubmitBarKeys(),
        ...componentUserCountDialogKeys()
      ],
      componentId: 'smart-i18n-provider'
    }),
    useBusinessCheckBehavior()
  ],
  // td: {
  //   // clickSearch: {
  //   //   type: 'throttle',
  //   // },
  //   onPlaceOrder: {
  //     type: 'throttle'
  //   },
  //   authPhoneClick: {
  //     type: 'throttle'
  //   }
  // },
  isHeaderHeightChanged: false,
  pages: [],
  mainHeight: 0,
  scrollHeight: 0,
  prevServiceType: null,
  prevServiceTypeName: null,
  roundMealPurchaseTime: [],
  properties: {
    sqbBridge: {
      type: Object,
      value: {}
    },
    mpBridge: {
      type: Object,
      value: {}
    },
    query: {
      type: Object,
      value: {}
    },
    config: {
      type: Object,
      value: {}
    },
    // 解决收钱吧扫码有其他参数时，不通过query获取
    // 收钱吧默认为首页时不做跳转
    homeQuery: {
      type: Object,
      value: null
    },
    isReady: {
      type: Boolean,
      value: false
    }
  },
  data: {
    hotSaleRankingText: '销量第##名',
    systemInfo,
    needAuthPrivacyList: [
      {
        agreementType: 'sqb_c_privacy',
        signObjId: '',
        // force: true,
        once: true
      },
      {
        once: true,
        agreementType: 'sqb_c_service',
        signObjId: ''
        // force: true,
      }
    ],
    // 商品显示模式
    goodsDisplayMode: defaultGoodsDisplayMode,
    goodsDisplayModeText: GOODS_DISPLAY_MODE_MAP[defaultGoodsDisplayMode],
    categoryScrollLeft: 0,
    init: true,
    componentName: 'page-home',
    syncWechatDevCode: PLUGIN_HOME_PATH,
    headerBarPaddingTop: STATUS_BAR_HEIGHT,
    headerBarHeight: HEADER_BAR_HEIGHT,
    RECOMMEND_TAG_IMAGE,
    SPECIAL_CATEGORIES,
    headerBarPaddingRight,
    store_position_top: STORE_DISTANCE_TO_TOP,
    header_bar_height: HEADER_BAR_HEIGHT,
    status_bar_height: STATUS_BAR_HEIGHT, //手机状态栏高度
    headStyles: {
      height: HEADER_BAR_HEIGHT + 'PX',
      paddingTop: STATUS_BAR_HEIGHT + 'PX',
      paddingRight: headerBarPaddingRight + 'px'
    },
    couponIconStyle: {
      transform: 'scale(0)'
    },
    stickyStyle: {
      top: `${HEADER_BAR_HEIGHT}PX`
    },
    categoryWrapStyle: {
      top: `${HEADER_BAR_HEIGHT - 1}PX`
    },
    backToTopStyle: {
      top: `${HEADER_BAR_HEIGHT + 10}PX`
    },
    stickyRatio: 0,
    // 首页刚进入页category-wrap面需要loading
    loading: true,
    // 首页商品列表加载
    goodsLoading: true,
    couponSheetShow: false,
    currentGood: null,
    goodDetailShow: false,
    scrollTop: 0,
    freeCoupons: [],
    combinedActivities: [],
    currentCouponIndex: 0,
    discountTag: [],
    currentDiscount: '',
    otherDiscounts: [],
    store: {},
    merchant: {},
    storeId: null,
    merchantId: null,
    activeCategoryId: null,
    intoCategoryId: null,
    bulletinExpand: true,
    bulletinExpandShow: false,
    // service type
    selectServiceTypeShow: false,
    serviceTypeList: [], //SERVICE_TYPES,
    serviceTypes: [],
    currentServiceTypeIndex: 0,
    _selectedServiceTypeIndex: 0,
    serviceType: null,
    serviceTypeName: null,
    serviceTypeItem: null,
    // 购物车
    cart: null,
    discount: null,
    isCartShow: false,
    // 商品列表滚动相关
    goodsListScrollTop: 0,
    scrollEnabled: false,
    categoriesWithGoodList: [],
    pages: [],
    categories: [],
    renders: [],
    titleHeight: CATEGORY_NAME_HEIGHT,
    itemHeight: GOODS_ITEM_HEIGHT + GOODS_ITEM_MARGIN_BOTTOM,
    minItemHeight: GOODS_ITEM_HEIGHT,
    itemBottom: GOODS_ITEM_MARGIN_BOTTOM,
    pageHeight: 0,
    viewPort: 500,
    // 围餐
    canPlaceOrder: true,
    payway: 1,
    // 是否被收收藏
    isCollected: false,
    tableId: '',
    mealType: '',
    // 是否是再来一单
    isReOrder: false,
    /**
     * 结算逻辑相关
     */
    // 是否高亮显示
    isHighLight: false,
    // 是否有必购商品分类
    isHaveMustGoods: false,
    // 是否已选必购商品分类
    isSelectedMustGoods: true,
    // 必购商品分类类ID
    mustCategoryId: null,
    // 是否有最低价
    isHaveMinPrice: false,
    // 最低价格
    minDeliveryPrice: 0,
    // 是否低于起送价
    isLowerMinDeliveryPrice: false,
    // 差 X 元起送
    minusPrice: 0,
    // 是否显示订单提醒
    orderMessageShow: false,
    // 订单提醒是否已经展示过
    isOrderMessageShowed: false,
    // 页面是否滚动到一定距离
    isScrolled: false,
    // 订单提醒数据
    orderMessage: null,
    isWeapp: isWeixin(),
    // 除去商家优惠后的总价
    actualPrice: 0,
    // 左边分类滚动高度
    categoryScrollTo: 0,
    //支付宝授权信息
    aliUserIsAuth: true,
    aliPhoneIsAuth: true,
    hasAuthedPhone: false, // 是否已经点过授权，不管拒绝还是统一
    apolloIsOn: false,
    aliShouldAuthPhone: false, //支付宝是否开启授权
    // b端设置授权开关
    // 弹出用户授权框周期，单位为秒
    authPhoneProps: {
      cycle: AUTH_EXPIRE_TIME // 1天
    },
    authUserProps: {
      cycle: AUTH_EXPIRE_TIME // 1天
    },
    weixinAuthData: {
      shouldAuthUser: false,
      shouldAuthPhone: false
    },
    // 推荐商品展示方式
    recommendShowWay: VERTICAL,
    recommends: [],
    // 广告
    adsense: {},
    // 是否显示微信活动
    isShowWxAct: false,
    // 简化pages,包含分类id和offset
    simplePages: [],
    scrollable: false,
    // 头部导航按钮是否可见
    canBack: false,
    // 修复支付宝小程序scroll-top不生效的问题
    toTopView: '',
    noticeShowType: 0,
    showSelectUserCount: false,
    mustOrderTip: false,
    tableName: '',
    enterCountShow: false,
    currentEnterGoods: {},
    bulletin: '',
    ranking: 6,
    homeTabs: [
      {
        label: '点单',
        key: 'order',
        id: 1
      },
      {
        label: '商家',
        key: ' merchant',
        id: 2
      }
    ],
    currentHomeTabId: 1,
    // MKSS
    // 是否展示营销弹窗
    activityDialogShow: false,
    // 是否已经展示过营销弹窗
    activityDialogShowed: false,
    // 是否勾选购物车储值组件
    isStoredPaySelected: false,
    // 储值弹窗场景参数
    mkssActivityDialogParams: {},
    // 商品列表组件渲染完成
    isGoodsListRendered: false,
    // 处理麦当劳头部icons
    headerIconVersion: 'v1',
    headerIconUrls: [],
    isMkssCouponTagShow: true,
    // 是否是预览模式
    isPreview: false,
    // 主要解决麦当劳上拉icon不一致的问题
    btnServiceTypeTextStyle: null,
    // 头部我的订单icon样式
    headerMyOrderIconStyle: { width: '100rpx', height: '40rpx' },
    // banner 埋点数据
    bannerTrackParams: { placement: '点单页', sm_uiview_name: '活动banner' },
    showAddressSheet: false,
    // 可配送地址
    areas: {},
    // 商品是否处于可售状态：是否可展示弹窗
    isSaleDialogShow: false,
    // 商品处于不可售状态时，弹窗展示的内容信息
    salesTimeInfo: {},
    // 分类scrollView滚动子元素id
    categoryScrollIntoView: null,
    // 判断是否为商家小程序
    isMerchant: false,
    // 滚动时是否隐藏
    hideOnScroll: false,
    // 浮窗AOP数据
    floatingBubbleData: {},
    floatingBubbleVisible: true,
    // 点单业务emenuChannel
    emenuChannel,
    isUseLocalCart: false, // 是否使用本地购物车
    // 支点活动相关参数
    alipayShopId: '',
    initializedMustCaPosition: false, // 必选分类位置是否初始化过
    TAKE_OUT_ORDER,
    // 这个数据只在商品详情页使用
    // 其他地方要使用先讨论
    cartDataForGoodsDetail: null,
    aopDialogShow: true,
    aopDialogShowed: false,
    storeShareData: {},
    // 商品详情页展示的规格
    imageDisplayConfig: {
      goodsImageSizeIndex: 1,
      goodsImageDisplaySize: '750rpx'
    },
    goodsLayoutConfigs
  },
  // observers: {
  //   isReady: function (isReady) {
  //     if (!isReady) return
  //     console.log('@home #isReady', { isReady }, Date.now())
  //     this.onAttached()
  //   }
  // },
  lifetimes: {
    created() {
      // 测试内存泄露
      this.__key = 'homeComponent'
      // TODO: 临时解决
      // 需要删除
      if (global.pageSet && global.pageSet instanceof Set) {
        global.pageSet.add(this)
      }
      this.bindOnShow = this.onShow.bind(this)
      this.bindOnHide = this.onHide.bind(this)
      this.bindAfterCartChanged = this.afterCartChanged.bind(this)
    },
    async attached() {
      this.onAttached()
    },
    ready() {
      this.onReady()
    },
    async detached() {
      console.log('@home - detached')
      const self = this
      // 清空优惠请求 mk_custom_info
      emenuChannel.clearMkCustomInfo()
      // 取消监听加购红点变化
      // 清理首页内存泄漏
      // offCartChangedEvent(this.onCartChanged);
      // clearTimeout(scrollingTimerId);
      self.call('off', 'show', this.bindOnShow)
      self.call('off', 'hide', this.bindOnHide)
      self.cashierSvc.removeCallback(this.listenCashierOnline)
      self.cashierSvc.stop()
      offEventBus(this.bindAfterCartChanged)

      // 清除绑定函数的引用，防止内存泄漏
      this.bindOnShow = null
      this.bindOnHide = null
      this.bindAfterCartChanged = null

      // 解绑监听用户重新登录
      const { sqbBridge } = self.data
      const unsubscribe = _.get(sqbBridge, 'observers.authLogin.unsubscribe')
      if (_.isFunction(unsubscribe)) {
        // 使用保存的 switchLoginHandler 取消订阅，避免内存泄漏
        if (self.switchLoginHandler) {
          unsubscribe(self.switchLoginHandler)
        }
        // 取消 onReLogin 的订阅
        unsubscribe(this.onReLogin)
        self.switchLoginHandler = null
        self.onReLogin = null
      }
      storage('floatingBubbleVisible', true)
      callPlugin('roundMealCheckVersion', 'end')
      // 获取当前页面栈
      const pages = getCurrentPages() || []
      // 扫码点单的有些场景会再次push一个点单的页面,在返回的时候从第一个点单页到第二个点单页会触发destroyed生命周期函数,这个时候不能直接执行以下代码,否则会出现加减商品的时候购物车状态没有变化
      const homePages = _.filter(pages, page => page.route.includes('s11gfxefx0j6'))
      // 如果首页只有一个时，才触发释放
      if (homePages.length === 1) {
        self.cashierSvc = null
        self.bus = null
        setMethodCtx(null)
        setDiscountCtx(null)
        NetworkSpeed.destroy()
        HomeStore.destroy()
        self.homeStore = null
        CartStore.destroyInstance()
        self.cartStore && self.cartStore.destroy()
        self.cartStore = null
        BadgeStore.destroy()
        HooksManager.destroy()
        self.hooksManager = null
        self.badgeStore = null // Add this line if you have a reference
        this.onSwitchLoginFailCallback = null
        this.onSwitchLoginSuccessCallback = null
        // 清除selectUserCountCallback相关引用
        this.originalSelectUserCountCallback = null
        // 重置被重写的selectUserCountCallback方法，避免闭包引起的内存泄漏
        this.selectUserCountCallback = null
      }
      // 清除终端信息
      storage('smart-mp:terminal', null)
    }
  },
  // @ts-ignore
  methods: {
    ...AlertHelpers,
    async onAttached() {
      const { isReady } = this.properties
      if (!isReady) return
      this.logMetrics('home')
      end = Date.now()
      // @ts-ignore
      const { sqbBridge, homeQuery } = this.data
      const queryParams = sqbBridge.getMiniProgramQuery()
      // 监听用户是否重新登录
      storage('floatingBubbleVisible', true)
      storage(`cart_version`, 0)
      storage('smart:home_query', queryParams) // 主要为重新跳转到首页使用

      const cacheTemplateParams = this.getCacheTemplateParams()
      // _.assign(queryParams, cacheTemplateParams)
      // }
      sqbBridge.logger.time()
      this.initAopDefaultTrackParams({ ...home_default_aop_track_params })
      if (homeQuery) {
        _.assign(this.query, homeQuery)
      }
      this.updateSubmitText()
      // 内存警告
      // if (_.isFunction(wx.onMemoryWarning)) {
      //   wx.onMemoryWarning(() => {
      //     sqbBridge.sls('PERFORMANCE', {
      //       type: 'MemoryWarning',
      //       data: {}
      //     })
      //   })
      // }

      // 处理预览模式
      // if (_.get(queryParams, 'draftId')) {
      //   this._render({ isPreview: true })
      // }

      this._render({ SPECIAL_CATEGORIES })

      // useBusinessCheck(this)

      this.init()

      this.homeStore = HomeStore.getInstance(this, this.data)

      // 开始执行主流程
      const storeId = _.get(queryParams, 'storeId')
      const qrCode = _.get(queryParams, 'qrCode')
      const serviceTypeName = _.get(queryParams, 'serviceTypeName')
      const q = _.get(queryParams, 'q')
      const buyAgainOrderType = _.get(queryParams, 'buyAgainOrderType')
      const from = _.get(queryParams, 'buyAgainOrderType') // campus or manual
      if (storeId || buyAgainOrderType || (from && !sqbBridge.isJJZ && (!qrCode || !q))) {
        // @ts-ignore
        const cachedPayload = storage(`emenu:${storeId}_${serviceTypeName}`) || {}
        useCached = shouldUseCache(cachedPayload)
        ECHO('命中聚合接口cache', { useCached })

        if (useCached) {
          const data = _.get(cachedPayload, 'data')
          sqbBridge.initByPayload(data, storeId)
        } else {
          await sqbBridge.initByQrcodeOrStoreId({ ...queryParams, ...cacheTemplateParams })
        }
        this.clearCachedData()
      }

      sqbBridge.getPayload(storeId).then(async (payload: TPayload) => {
        // 依赖payload数据，可在此处执行

        if (_.isEmpty(payload)) {
          const _payload: TPayload = await sqbBridge.initByQrcodeOrStoreId({
            ...queryParams,
            ...cacheTemplateParams
          })
          return this.mainProcess(_payload)
        }

        return this.mainProcess(payload)
      })
      // 设置返回icon
      setCanBack()
      // 设置payway、aliUserIsAuth、aliPhoneIsAuth、hasAuthedPhone、apolloIsOn
      setDataPlatform()
      // 设置mealType
      // setDataMealType()
      this.setData({
        mealType: _.get(this.getTerminal(), 'mealType')
        // tableId,
      })
      // 监听页面show事件
      this.call('on', 'show', this.bindOnShow)
      this.call('on', 'hide', this.bindOnHide)
      // 监听加购红点变化
      // onCartChangedEvent(this.onCartChanged);

      // 广告预加载 广告组有bug 删除预加载
      //sqbBridge.setAdsPreRender && sqbBridge.setAdsPreRender(this.store);
      // 设置banner广告参数
      // this.setAdsBannerQuery();
      // PERFORMANCE_DATA.push({ name: '#created end', duration: Date.now() - end });
      _.assign(PERFORMANCE_DATA, {
        created: {
          duration: Date.now() - end
        }
      })
      try {
        const { platform } = sqbBridge.getContext()
        const isMerchant = _.get(platform, 'type') === 'merchant'
        this._render({ isMerchant })
      } catch (e) {}

      sqbBridge.track('SmMpPageDisplay')
      if (!this.__init) this.init()
      // 上报终端日志
      // slsTerminal()
      // C端小程序上报日志
      // uploadSLSLog()

      // 设置下单文本
      setDataSubmitText()

      this._render({ isLoad: true })
    },
    async onReady() {
      const { sqbBridge } = this.data
      // 如果要与release/build代码对齐，
      // 则久久折主体授权抽象节点做相应的跳转
      initAuthUserData(this) //初始化支付宝和微信的授权数据
      checkSession(this)

      /**************************用户网络数据******************************/
      try {
        const { q = 'unknown', storeId = 'unknown' } = this.getQuery() || {}
        const openid = await sqbBridge.getUser('thirdpartyUserId')
        NetworkSpeed.start({
          // @ts-ignore
          onStatus: res => {
            const { status } = res
            sqbBridge.sls('NETWORK', {
              type: 'network',
              startAt: Date.now(),
              storeId,
              query: this.getQuery(),
              q,
              openId: openid,
              data: { status, res }
            })
          },
          onDownload: (res: { status: string; cost: number }) => {
            const { status } = res
            sqbBridge.sls('NETWORK', {
              type: 'network',
              startAt: Date.now(),
              storeId,
              query: this.getQuery(),
              q,
              openId: openid,
              duration: _.get(res, 'cost'),
              data: { status, res }
            })
          }
        })
      } catch (error) {
        const openid = sqbBridge.getUser('thirdpartyUserId')
        sqbBridge.sls('NETWORK', {
          startAt: Date.now(),
          query: this.getQuery(),
          openId: openid,
          error
        })
      }

      this.loadFloatAOP()
      _.assign(PERFORMANCE_DATA, { mounted: { duration: Date.now() - end! } })
      this.roundMealPurchaseTime = []
    },
    reload(qrcode, opts = {}) {
      const self = this
      // 处理 "扫一扫"更新数据
      const { sqbBridge } = this.data
      return sqbBridge
        .initByQrcodeOrStoreId({ q: encodeURIComponent(qrcode), refresh: true })
        .then(async (payload: TPayload) => {
          if (payload) {
            const options = this.getQueryOpts()
            self.initServices(options)
            setMethodCtx(this)
            setDiscountCtx(this)

            const page = _.get(payload, 'extra.page')
            const serviceTypes = _.get(payload, 'serviceTypes')
            const serviceType = _.get(serviceTypes, 'serviceType') || 1
            const currentServiceTypeIndex = _.get(serviceTypes, 'currentServiceTypeIndex') || 0
            const serviceTypeList = _.get(serviceTypes, 'serviceTypeList') || []
            const serviceTypeName = _.get(serviceTypes, 'serviceTypeName')

            // @ts-ignore
            const { isShowWxActFlag } = this.data

            sqbBridge.sls('INFO', {
              type: 'reload:start',
              data: {
                options,
                qrcode,
                page,
                serviceType,
                currentServiceTypeIndex,
                serviceTypeList,
                serviceTypeName
              }
            })

            // 如果支持跳转到pay页面， 返回点单会报错
            // 因此不支持扫一扫跳转到pay页面
            if (page && (page === 'page-closing' || page === 'pay')) {
              // if (page && page !== 'page-home') {
              if (page === 'pay') {
                return this.redirect('page-closing', { closing: 6 })
              }
              return this.redirect(page)
            }

            // if (page && page !== 'page-home') {
            //   if (page === 'page-submit-order' || page === 'page-firstpay-round-batch-list') {
            //     return this.go(page)
            //   } else {
            //     return this.redirect(page)
            //   }
            // }

            updateServiceTypesData(payload, serviceTypeName, 'reload', newServiceTypes => {
              this._render({ ...newServiceTypes })
            })

            this._render(
              {
                selectServiceTypeShow: false,
                currentServiceTypeIndex,
                serviceTypeList
              },
              () => {
                // TODO：如果店铺没有正常营业，是否不应该显示
                if (isShowWxActFlag === true) {
                  this._render({ isShowWxActFlag: false, isShowWxAct: true })
                }
              }
            )
            // HooksManager.destroy()
            // TODO: 验证是否需要初始化hooksManager
            // this.hooksManager = HooksManager.init(this)
            const serviceTypeChanged = this.prevServiceType && this.prevServiceType !== serviceType
            payload = _.assign({}, payload, { serviceTypeChanged, ...opts })
            // 更新 NoticeBarStatus
            // if (serviceTypeName !== this.data.serviceTypeName) { // onServiceSwitchEnter 已经更新serviceTypeName, 外卖切自取时，则不会更新
            // this.updateNoticeBarStatus()
            // }
            // 更新emenu的serviceTypes
            sqbBridge.updateServiceTypes(serviceTypes)
            this._render(serviceTypes)

            // 主要需要计算最低价
            // 本地购物车只需要更新优惠
            // 不需要更新购物车
            await this.initLocalCart({ initLocal: false })

            if (isRoundMeal(this)) {
              const cart = this.getCartData()
              console.log('@reload #cart', cart)
              this.checkMustOrder(cart)
            }

            // TODO: 处理reorder时, 购物车弹出的问题
            // TODO: 扫码为什么不会触发选择人数弹窗
            // TODO: 验证优惠查询参数
            this.mainProcess(payload).then(payload => {
              // const handleRedirect = (payload: any) => {
              // 1. handle closing
              const page = _.get(payload, 'extra.page')
              const query = _.get(payload, 'extra')
              // if (page && page === 'page-closing') {
              //   // if (page && page !== 'page-home') {
              //   return this.redirect(page)
              // }
              if (page && page !== 'page-home') {
                if (page === 'page-submit-order' || page === 'page-firstpay-round-batch-list') {
                  this.go(page, query)
                } else {
                  return this.redirect(page, query)
                }
              }
              return payload
              // }
            })
          }
        })
        .catch((err: any) => {
          console.error(err)
          this.$toast(this.t('扫码失败，请重试'))
        })
        .finally(() => {
          this.$clearToast()
          // this.setData({ isLoad: true })
        })
    },
    getCartData() {
      return this.cartStore.getData()?.cart
    },
    getDiscountData() {
      const { discount } = this.cartStore.getData()
      return discount
    },
    /**
     * 获取并处理售卖时段要展示的数据以及商品是否在可售时间内
     * @param e
     */
    onGoodsClick(e) {
      this._render({ salesTimeInfo: e.detail, isSaleDialogShow: true })
    },
    /**
     * 售卖周期弹窗的"我知道了"的点击功能
     */
    dialogClick() {
      this._render({ isSaleDialogShow: false })
    },
    onHomeTabChange(e) {
      const { id: currentHomeTabId } = getDataset(e)
      this._render({ currentHomeTabId })
      if (currentHomeTabId === 1) this.onScrollHomePage({ detail: { scrollTop: 0 } })
    },
    async onServiceTypesChanged(payload) {
      const { sqbBridge } = this.data
      sqbBridge.logger.info('@home - #onServiceTypesChanged start', payload)
      const { serviceType, currentServiceTypeIndex, selectServiceTypeShow, storeId } =
        payload as any

      const serviceTypes = _.pick(payload, [
        'serviceType',
        'currentServiceTypeIndex',
        'selectServiceTypeShow',
        'serviceTypeName',
        'serviceTypeList'
      ])

      sqbBridge.logger.info('@home - #onServiceTypesChanged serviceTypes:', serviceTypes)

      // this.isHeaderHeightChanged = false
      let serviceTypeChanged = true
      // const needReload = true;
      const notNeedReload = this.prevServiceType && this.prevServiceType === serviceType

      if (notNeedReload) {
        serviceTypeChanged = false
        // 更新 NoticeBarStatus
        // if (serviceTypeName !== this.data.serviceTypeName) { // onServiceSwitchEnter 已经更新serviceTypeName, 外卖切自取时，则不会更新
        this.updateNoticeBarStatus()
        // }
        // 更新emenu的serviceTypes
        sqbBridge.updateServiceTypes(serviceTypes)
        this._render(serviceTypes)

        // 主要需要计算最低价
        // 本地购物车只需要更新优惠
        // 不需要更新购物车
        await this.initLocalCart({ initLocal: false })
        if (isRoundMeal(this)) {
          const cart = this.getCartData()
          this.checkMustOrder(cart)
        }
        return
      }
      // 切换服务类型时， 需初始化购物车为空
      // 否则用户快速点击时，到结算页会因为服务类型不一致导致报错
      const initCart = {
        records: [],
        total: 0,
        total_price: 0,
        spu_count_map: {}
      }
      this._render({ cart: initCart })
      // this._render({ activityDialogShow: false });
      // 1. reload data;
      // const query = this.query

      // TODO： 临时调试， 后面要删除
      // const themeParams = {
      //   templateId:
      //     _.get(query, 'templateId') || _.get(storage('smart:home_query'), 'templateId') || 100000,
      //   templateVersion:
      //     _.get(query, 'templateVersion') ||
      //     _.get(storage('smart:home_query'), 'templateVersion') ||
      //     'v1.0'
      // }
      // await sqbBridge.initByQrcodeOrStoreId({ serviceType, storeId, ...themeParams })

      const cacheTemplateParams = this.getCacheTemplateParams()
      await sqbBridge.initByQrcodeOrStoreId({ serviceType, storeId, ...cacheTemplateParams })
      sqbBridge.getPayload(storeId).then((payload: any) => {
        const { serviceTypeList, serviceTypes } = this.data
        const serviceTypeItem = serviceTypeList[currentServiceTypeIndex]
        const serviceTypeName = _.get(serviceTypeItem, 'serviceTypeName')

        // 设置默认选中serviceType
        _.forEach(serviceTypeList, (item: any, idx: number) => {
          item.active = idx === currentServiceTypeIndex
        })

        _.assign(serviceTypes, {
          serviceType,
          currentServiceTypeIndex,
          serviceTypeList,
          selectServiceTypeShow,
          serviceTypeName
        })

        // 主要为判断是否需要重新切换serviceType
        this.prevServiceType = serviceType
        this.prevServiceTypeName = serviceTypeName

        this._render({
          serviceType,
          currentServiceTypeIndex,
          storeId
        })

        // 更新emenu的serviceTypes
        sqbBridge.updateServiceTypes({
          currentServiceTypeIndex,
          selectServiceTypeShow,
          serviceType,
          serviceTypeList,
          serviceTypeName
        })

        // const { serviceTypes } = this.data;
        this.mainProcess({
          ...payload,
          serviceTypes,
          serviceTypeItem,
          notNeedReload,
          serviceTypeChanged
        })
      })
    },
    async beforeRender(payload) {
      const { retailStore } = _.get(payload, 'store')
      // 零售模式处理逻辑开始
      // 处理零售商品分类
      const handleRetailCategory = (payload: any) => {
        if (retailStore) {
          this._render({
            isRetail: retailStore
          })
          const { id } = _.get(payload, 'category[0]')
          _.forEach(payload.category, ca => {
            if (_.isEmpty(ca.sub_categories)) {
              ca.sub_categories = []
            }
          })
          this._render(
            {
              categories: payload.category,
              activeCategoryId: id,
              retailActiveSubCategoryId: id
            },
            () => {
              setTimeout(() => {
                this.getSubCategoryData(id)
              }, 0)
            }
          )
        }
        return payload
      }

      // 处理聚合接口返回的零售商品
      const initRetailGoods = (payload: any) => {
        if (retailStore) {
          const { id } = _.get(payload, 'category[0]')
          const { goods: categoryGoods, cursor, total } = _.get(payload, 'goods')
          const resGoods = categoryGoods[0].items
          const _resGoodsLen = _.size(resGoods)
          const noMore = _.size(resGoods) === total
          // 当前一级分类下的全部分类商品
          const allCategoryGoods = {
            category_id: id,
            category_name: '全部',
            items: resGoods,
            cursor,
            noMore
          }
          // 首次需要渲染的数据,page存的是一级分类id
          const retailCategoryGoods = [{ page: id, categories: [allCategoryGoods] }]
          // setTimeout(() => {
          //   this._render(
          //     {
          //       retailGoods: _.cloneDeep(retailCategoryGoods)
          //     },
          //     () => this.handleFillGoods(_resGoodsLen, 0)
          //   )
          // }, 1000)

          this._render(
            {
              retailGoods: isAlipay() ? _.cloneDeep(retailCategoryGoods) : retailCategoryGoods
            },
            () => this.handleFillGoods(_resGoodsLen, 0)
          )
          this.handleNoMoreRetailGoods(id, noMore)
        }
        return payload
      }
      // 零售模式处理逻辑结束

      // 处理详情页&套餐页图片展示规格
      const handleImageDisplaySize = (payload: any) => {
        if (retailStore) {
          return payload
        }
        const goodsImageSizeIndex = _.get(payload, `mcc.${MCC_PRODUCT_IMAGE_DISPLAY_SIZE}`)
        const imageDisplayConfig = createImageDisplayConfig(goodsImageSizeIndex)

        this._render({ imageDisplayConfig })
        return payload
      }

      const startAt = Date.now()
      const { sqbBridge } = this.data

      const p = Promise.resolve(payload)
      // 处理页面跳转
      const handleRedirect = (payload: any) => {
        // 1. handle closing
        const page = _.get(payload, 'extra.page')
        if (page && page === 'page-closing') {
          // if (page && page !== 'page-home') {
          return this.redirect(page)
        }
        // if (page && page !== 'page-home') {
        //   if (page === 'page-submit-order' || page === 'page-firstpay-round-batch-list') {
        //     this.go(page)
        //   } else {
        //     return this.redirect(page)
        //   }
        // }
        return payload
      }
      // 处理serviceTypes
      const handleServiceTypes = (payload: any) => {
        if (!payload) throw Error('payload 为空')

        const {
          selectServiceTypeShow,
          currentServiceTypeIndex,
          serviceType,
          serviceTypeList,
          serviceTypeName
        } = _.get(payload, 'serviceTypes', {})

        const { mcc, store } = payload

        // 签署隐私协议
        this.requestPolicy(serviceType)

        if (serviceTypeList) {
          let bulletin = _.get(mcc, 'store_bulletin')
          if (serviceType === 1) {
            bulletin = _.get(mcc, 'store_takeout_bulletin')
          }
          _.assign(payload, { serviceTypeList, serviceType })

          this.prevServiceType = serviceType
          this.prevServiceTypeName = serviceTypeName

          this._render({
            store,
            bulletin,
            serviceTypeList,
            serviceType,
            serviceTypeName
          })

          const serviceTypeItem = serviceTypeList[currentServiceTypeIndex]

          _.assign(payload, { serviceTypeItem })
        }

        selectServiceTypeShow && this._render({ selectServiceTypeShow })
        return { ...payload, currentServiceTypeIndex }
      }

      const preLoadGoods = (payload: any) => {
        if (retailStore) {
          return payload
        }
        const IS_ENABLE_GOODS_PRELOAD = sqbBridge.getConfig('IS_ENABLE_GOODS_PRELOAD')

        this.homeStore.setPreloadStatus(IS_ENABLE_GOODS_PRELOAD)

        if (!IS_ENABLE_GOODS_PRELOAD) return payload

        const {
          goods,
          serviceType: service_type,
          store: { storeId: store_id }
        } = payload

        const page_size = sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || 30
        // 预拉取商品
        const GOODS_PRELOAD_PAGE_COUNT = sqbBridge.getConfig('GOODS_PRELOAD_PAGE_COUNT')
        const GOODS_PRELOAD_MAX_COUNT = sqbBridge.getConfig('GOODS_PRELOAD_MAX_COUNT')
        this.homeStore.pollAllGoods(goods, GOODS_PRELOAD_PAGE_COUNT, GOODS_PRELOAD_MAX_COUNT, {
          page_size,
          service_type,
          store_id,
          page: 1
        })
        return payload
      }

      const handleCategory = (payload: any) => {
        const { buyAgainOrderType, source } = sqbBridge.getMiniProgramQuery() || {}
        const { mcc, currentServiceTypeIndex, goodsDisplayModeText } = payload
        let { category: categories } = payload

        // 后端"买过的菜"改成"点过"
        _.forEach(categories, cat => {
          if (cat.id === 'recent') {
            cat.name = '点过'
          }
        })

        if (retailStore) {
          return payload
        }

        // 网格模式
        if (goodsDisplayModeText === 'grid') {
          categories = _.map(categories, cat => {
            cat.shortName = _.truncate(cat.name, { length: 11, omission: '...' })
            return cat
          })
        }

        // 分享， 会带参数buyAgainOrderType， 主要为默认选择serviceType, 是一种变通方法
        if (buyAgainOrderType && currentServiceTypeIndex >= 0 && source !== 'share') {
          // payload =
          _.assign(payload, {
            ...payload,
            isReOrder: true,
            _selectedServiceTypeIndex: currentServiceTypeIndex
          })
        }
        // this.loadActivities(serviceType);

        // 推荐商品是否橱窗展示
        let recommends = []
        const recommendShowWay = mcc && mcc[MCC_RECOMMEND_SHOW_WAY] // this.__bridge__.getMcc(MCC_RECOMMEND_SHOW_WAY, VERTICAL);
        this._render({ recommendShowWay })

        // const _categories = _.cloneDeep(categories);

        // 处理橱窗推荐商品
        // const categories = this.__bridge__.getCategory();
        // TODO: 在EMENU处理
        const recommendCatIdx = _.findIndex(categories, { id: RECOMMEND_CAT_ID })
        // const recommendCatIdx = _.findIndex(_categories, { id: RECOMMEND_CAT_ID });

        if (
          recommendShowWay === sqbBridge.getConfig('HORIZONTAL', HORIZONTAL) &&
          recommendCatIdx > -1
        ) {
          // recommends = _.get(_categories, `[${recommendCatIdx}].items`, []);
          recommends = _.get(categories, `[${recommendCatIdx}].items`) || []

          recommends = _.map(recommends, recommend => {
            // recommend['key'] = `${recommend.id}-${this.data.serviceTypeName}`;
            calcRemainNumAndOutOfStock(recommend, this.getCartData())
            // @ts-ignore
            return transformGoods(recommend, this.data.serviceType)
          })
          // this._render({ recommends });
          categories.splice(recommendCatIdx, 1)
          // categories.splice(recommendCatIdx, 1)
          // 标记推荐分类为隐藏而不是删除
          // categories = _.map(categories, (cat, index) => {
          //   if (index === recommendCatIdx) {
          //     cat.isHidden = true
          //   }
          //   return cat
          // })
        }
        // if (recommends) {
        // _.assign(payload, { recommends, categories: _categories });
        _.assign(payload, { recommends, categories })
        // }
        // else {
        //   _.assign(payload, {  categories: _categories });
        // }
        return payload
      }

      const handleGoodsList = async (payload: any) => {
        if (retailStore) {
          return payload
        }
        const { category: categories, goods, goodsDisplayModeText, goodsDisplayMode } = payload
        // const { categories, goods, goodsDisplayModeText } = payload;
        const { serviceType } = _.get(payload, 'serviceTypes', {})
        const _categories = _.cloneDeep(categories)

        let pages = sqbBridge.getPages()
        if (!pages) {
          _.assign(PERFORMANCE_DATA, {
            buildPagesLayoutStart: { duration: Date.now() - (end || 0) }
          })

          const pageSize = sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || 30

          pages = this.homeStore.buildPagesLayout(_categories, pageSize, {
            mode: goodsDisplayModeText
          }) // .then((pages) => {
          _.assign(PERFORMANCE_DATA, {
            buildPagesLayoutEnd: { duration: Date.now() - (end || 0) }
          })
        }
        const simplePages = toSimplePages(pages)

        this.update({ simplePages })
        const event = { detail: { scrollTop: 0 } }
        // 加载商品
        const { init } = this.data
        this._render({ loadedGoods: goods })

        const viewportOffset = sqbBridge.getConfig('PLUGIN_GOODS_VIEWPORT_OFFSET')
        let firstPageRenderGoodsCount = sqbBridge.getConfig(
          'PLUGIN_GOODS_FIRST_PAGE_RENDER_GOODS_COUNT_V2'
        )
        // @ts-ignore
        const debugEnable = storage('emenu:debugEnable')
        // @ts-ignore
        const debugFirstPageRenderGoodsCount = storage('emenu:firstPageRenderGoodsCount')
        if (debugEnable && debugFirstPageRenderGoodsCount) {
          firstPageRenderGoodsCount = debugFirstPageRenderGoodsCount
        }

        const { pages: updatedPages, renders } = await this.homeStore.loadGoods(
          event,
          pages,
          serviceType,
          {
            categories: _categories,
            loadedGoods: goods,
            viewportOffset,
            firstPageRenderGoodsCount,
            init,
            display_image_size: goodsDisplayMode + 1
          }
        )

        _.assign(payload, { pages: updatedPages, renders, categories: _categories, simplePages })
        // const renderData = { loading: false, goodsLoading: false, store, mcc, merchant, pages, categories };
        _.assign(PERFORMANCE_DATA, { handleGoodsListEnd: { duration: Date.now() - (end || 0) } })
        _.assign(PERFORMANCE_DATA, {
          handleGoodsListEnd: {
            duration: Date.now() - (end || 0),
            handleGoodsListDuration: Date.now() - startAt
          }
        })

        return payload
      }

      // 更新头部icon样式
      // const updateHeaderIconsVersion = async (payload: any) => {
      //   this.updateHeaderIconsVersion('v1')
      //   return payload
      // }
      // 处理商品显示模式
      const handleGoodsDisplayMode = (payload: any) => {
        // "MCC_GOODS_DISPLAY_MODE" 有可能为空字符串
        // _.get(payload, `mcc.${MCC_GOODS_DISPLAY_MODE}`, defaultGoodsDisplayMode) 不能解决空字符串或者null的问题
        const goodsDisplayMode =
          _.get(payload, `mcc.${MCC_GOODS_DISPLAY_MODE}`) || defaultGoodsDisplayMode

        const goodsDisplayModeText =
          GOODS_DISPLAY_MODE_MAP[goodsDisplayMode as keyof typeof GOODS_DISPLAY_MODE_MAP]

        _.assign(payload, { goodsDisplayMode, goodsDisplayModeText })
        this._render({ goodsDisplayMode, goodsDisplayModeText })

        return payload
      }
      // 本地购物车的初始化
      // 本地购物车的加购流程必须依赖购物车的初始化
      const handleInitCart = async (payload: any) => {
        const { serviceTypeChanged } = payload
        // const storeId = this.store.id
        const storeId = _.get(payload, 'store.storeId')
        this.initLocalCart({ serviceTypeChanged, storeId })
        return payload
      }

      const handleUseLocalCart = (payload: any) => {
        const { sqbBridge } = this.data
        // const isEnableLocalCart = sqbBridge.getConfig('ENABLE_LOCAL_CART');
        // storage('emenu:userLocalCart', isEnableLocalCart);
        const isUseLocalCart = useLocalCart(this)
        sqbBridge.sls('INFO', {
          type: 'isUseLocalCart',
          data: { data: { isUseLocalCart } }
        })
        this._render({ isUseLocalCart })
        return payload
      }

      // 解决工单 #2025040713122329
      const handleTerminal = (payload: any) => {
        const { terminal } = payload
        storage('smart-mp:terminal', terminal || null)

        return payload
      }

      return (
        p
          .then(handleRedirect)
          .then(handleServiceTypes)
          .then(handleUseLocalCart)
          .then(handleInitCart)
          .then(preLoadGoods)
          // .then(setMkssActivityDialogParams)
          // .then(updateText)
          .then(handleGoodsDisplayMode)
          .then(handleCategory)
          .then(handleRetailCategory)
          // .then(handleActivity)
          // .then(handleDeliveryArea)
          // .then(handleAdContent)
          .then(handleGoodsList)
          // .then(updateHeaderIconsVersion)
          .then(initRetailGoods)
          .then(handleImageDisplaySize)
          // .then(fetchCashierWhiteList)
          // .then(handleTableInfo)
          // .then(handlePivotInfo)
          .then(payload => {
            _.assign(PERFORMANCE_DATA, {
              dataReady: {
                duration: Date.now() - (end || 0)
              }
            })
            return payload
          })
          .then(handleTerminal)
      )
    },
    async initLocalCart(opts = {}) {
      console.log('Cart Store initLocalCart', opts)
      // @ts-ignore
      const { serviceTypeChanged } = opts
      const cartStore = CartStore.getInstance(this, this.data)
      const badgeStore = BadgeStore.getInstance(this, this.data)
      this.cartStore = cartStore
      // 加购,并保存到本地，但是未同步到后端
      // 包括初始化和商品增加
      if (!this.localCartInited) {
        cartStore.hooks.addSuccess.tap(
          'cartStore:addSuccess',
          ({
            cart,
            discount,
            refreshAll = true
          }: {
            cart: any
            discount: any
            refreshAll: boolean
          }) => {
            const { categories } = this.data
            StoreHelper.log('cartStore:addSuccess 更新分类红点、下单条件、否选人数等验证', {
              cart,
              discount
            })

            // 更新购物车数量
            badgeStore.updateCategoriesBadge(cart, categories, categoriesWithBadge => {
              this.update({ categories: categoriesWithBadge })
            })
            if (refreshAll) {
              // // 购车是否满足下单条件
              sqbBridge.sls('INFO', {
                type: 'cartStore:addSuccess',
                data: { data: { cart, discount } }
              })
              this.handleSettlement({ cart, discount })

              this.checkMustOrder(cart)
            }
            setTimeout(() => {
              // 仅给商品详情页使用
              this.update({ cartDataForGoodsDetail: _.cloneDeep(cart) })
            }, 0)
          }
        )
        // 同步后端购物车成功
        cartStore.hooks.syncSuccessEnd.tap(
          'cartStore:syncSuccessEnd',
          ({ cart, discount, localCart }: { cart: any; discount: any; localCart: any }) => {
            // this.update({ cart, discount });
            StoreHelper.log('cartStore:syncSuccessEnd 下单条件、否选人数等验证等', {
              cart,
              discount,
              localCart
            })
            this.handleSettlement({ cart, discount })
            sqbBridge.sls('INFO', {
              type: 'cartStore:syncSuccessEnd',
              data: { data: { cart, discount, localCart } }
            })
            this.checkMustOrder(cart)
          }
        )

        cartStore.hooks.syncFail.tap(
          'cartStore:syncFail',
          ({ remoteCart, localCart, error }: { remoteCart: any; localCart: any; error: any }) => {
            // 删除代码，临时测试
            // wx.showToast({ title: error.message });
            sqbBridge.sls('INFO', {
              type: 'cartStore:syncFail',
              data: { data: { remoteCart, localCart, error } }
            })
          }
        )

        cartStore.hooks.roundMealSyncSuccessEnd.tap('cartStore:roundMealSyncSuccessEnd', () => {
          if (!_.isEmpty(this.roundMealPurchaseTime) && this.roundMealPurchaseTime.length === 1) {
            sqbBridge.sls('INFO', {
              type: 'add_to_cart:analyze',
              data: { duration: new Date().getTime() - Number(this.roundMealPurchaseTime[0]) }
            })
          }
          this.roundMealPurchaseTime = []
        })
        this.localCartInited = true
      }

      // 初始化购物车
      const { payway, isStoredPaySelected, sqbBridge } = this.data
      // const supportCardPay = sqbBridge.getSupportCardPay();
      // const takeoutProfitSharing = await sqbBridge.getMcc(MCC_TAKEOUT_PROFIT_SHARING);

      cartStore.hooks.init.callAsync(
        {
          ...opts,
          payway,
          recharge_and_pay: isStoredPaySelected,
          // service_type_name: serviceTypeName,
          // from_cart: showStoredPay(serviceTypeName, takeoutProfitSharing, isRoundMeal(this), supportCardPay),
          recharge_interest_id: this.interestId
        },
        (err: Error, { cart }: { cart: any }) => {
          if (err) {
            // wx.showToast({
            //   title: err.message,
            //   icon: 'none',
            // });
            sqbBridge.sls('INFO', { type: 'cartStore:init:error', data: { data: { err, cart } } })
            return
          }

          sqbBridge.sls('INFO', { type: 'cartStore:init', data: { data: { cart } } })

          if (serviceTypeChanged && cart && cart.records && cart.records.length) {
            this.$toast('购物车商品有变动，请确认')
          }

          // 再来一单
          const { isReOrder } = this.data
          if (isReOrder) {
            // 延缓加载，优化体验
            const timeout = 1000
            if (cart && cart.records.length) {
              const timerId = setTimeout(() => {
                this._render({ isCartShow: true })
                clearTimeout(timerId)
              }, timeout)
            }
            this._render({ isReOrder: false })
          }
        }
      )
    },
    async render(payload) {
      const startAt = Date.now()
      const self = this
      const { sqbBridge } = this.data
      _.assign(PERFORMANCE_DATA, { renderMainStart: { duration: Date.now() - (end || 0) } })
      const renderMainData = (payload: any) => {
        const ranking = sqbBridge.getConfig('HOTSALE_RANKING_COUNT')
        const ENABLE_MKSS_DIALOG = sqbBridge.getConfig('ENABLE_MKSS_DIALOG') // 是否显示储值弹窗
        const IS_ENABLE_AUTH_PHONE = sqbBridge.getConfig('IS_ENABLE_AUTH_PHONE') // 是否开启手机号授权
        _.assign(payload, {
          ranking,
          ENABLE_MKSS_DIALOG,
          loading: false,
          goodsLoading: false,
          IS_ENABLE_AUTH_PHONE
        })
        const shouldRenderData = _.pick(payload, [
          'recommendShowWay',
          'loading',
          'goodsLoading',
          'store',
          'recommends',
          'pages',
          'categories',
          'isReOrder',
          '_selectedServiceTypeIndex',
          'serviceTypeList',
          'goodsDisplayMode',
          'ENABLE_MKSS_DIALOG',
          'IS_ENABLE_AUTH_PHONE'
        ])

        this._render(shouldRenderData, () => {
          const { init } = this.data
          sqbBridge.sls('INFO', { type: 'renderMainDataCallBack', data: { data: { init } } })
          if (init) {
            this._render({ init: false })
            // 补偿渲染首屏的商品
            // this.loadGoodsListV2({ detail: { scrollTop: 0 } });
            const event = {
              detail: {
                scrollTop: 0
              }
            }
            // @ts-ignore
            const { pages, categories, loadedGoods, sqbBridge } = this.data
            const { serviceType, init, goodsDisplayModeText, goodsDisplayMode } = this.data

            const viewportOffset = sqbBridge.getConfig('PLUGIN_GOODS_VIEWPORT_OFFSET')
            const firstPageRenderGoodsCount = sqbBridge.getConfig(
              'PLUGIN_GOODS_FIRST_PAGE_RENDER_GOODS_COUNT_V2'
            )
            this.homeStore
              .loadGoods(event, pages, serviceType, {
                categories,
                loadedGoods,
                init: false,
                viewportOffset,
                firstPageRenderGoodsCount,
                mode: goodsDisplayModeText,
                display_image_size: goodsDisplayMode + 1
              })
              .then(async ({ pages, renders }: { pages: any; renders: any }) => {
                sqbBridge.sls('INFO', {
                  type: 'renderMainDataCallBack-1',
                  data: { data: { pages, renders } }
                })
                this.afterGoodsListChanged(pages, renders)
              })
            // 补偿渲染首屏的商品 - end

            const goodsCount = _.flatMap(_.flatMap(pages, 'categories'), 'data').length
            const now = Date.now()
            _.assign(PERFORMANCE_DATA, {
              renderedMainEnd: {
                duration: now - (end || 0),
                renderMainDuration: now - startAt,
                init,
                goodsCount,
                useCached
              }
            })
            const renderDuration = _.get(PERFORMANCE_DATA, 'renderedMainEnd.duration') || 0

            sqbBridge.sls('PERFORMANCE', {
              type: 'page-home-render',
              duration: renderDuration,
              data: PERFORMANCE_DATA
            })
            // @ts-ignore
            if (storage('emenu:debugEnable')) {
              // @ts-ignore
              const existPerformanceData = storage('emenu:performance') || {}
              storage('emenu:performance', {
                ...existPerformanceData,
                [goodsCount]: PERFORMANCE_DATA
              })
            }
            ECHO('性能数据汇总🔽', JSON.stringify(PERFORMANCE_DATA, null, '\t'))

            if (goodsDisplayModeText === 'grid') {
              const query = wx.createSelectorQuery().in(this)
              query.selectAll('.category-item').boundingClientRect()
              query.exec(function (res) {
                const categoriesWithOffset = self.homeStore.calcCategoryOffsetGrid(
                  categories,
                  pages,
                  res[0]
                )
                self._render({ categories: categoriesWithOffset, categoriesWithOffset })
              })
            }
          }
          // await this.afterGoodsListChanged(pages, renders, categories);
          self.$emit('ready')
        })

        setTimeout(() => {
          this.onGoodsListRendered()
        }, 0)

        return payload
      }
      const p = Promise.resolve(payload)
      return p
        .then(payload => {
          renderMainData(payload)
          // clearTimeout(timerId);
          return payload
        })
        .then(payload => {
          const shouldRenderData = _.pick(payload, [
            'mcc',
            'serviceTypes',
            'currentServiceTypeIndex',
            'serviceType'
          ])
          this._render(shouldRenderData, () => {
            _.assign(PERFORMANCE_DATA, {
              renderedOther: { duration: Date.now() - (end || 0) }
            })
          })
          return payload
        })
    },
    afterRender(payload) {
      const { sqbBridge } = this.data
      const p = Promise.resolve(payload)
      // const shouldRenderData = _.pick(payload, [
      //     'loading',
      //     'goodsLoading',
      //     'store',
      //     'pages',
      //     'categories',
      //     'combinedActivities',
      //     'freeCoupons',
      //     'currentDiscount',
      //     'otherDiscounts',
      //     'discountTag',
      //     'recommends',
      //     'recommendShowWay',
      //     'isReOrder',
      //     '_selectedServiceTypeIndex',
      //     'serviceTypeList',
      //     'ENABLE_MKSS_DIALOG',
      //     'activity',
      //     'areas',
      //     'mkssActivityDialogParams',
      //     'jjzBusinessType',
      //     'tableName',
      //     'goodsDisplayMode',
      // ]);
      // 处理桌台信息
      const handleTableInfo = (payload: any) => {
        const jjzBusinessType = _.get(payload, 'terminal.jjz_business_type')
        const tableName = _.get(payload, 'terminal.tableName')

        _.assign(payload, { jjzBusinessType, tableName })

        this._render({ jjzBusinessType, tableName })

        return payload
      }

      // 加载广告 banner
      const handleAdContent = async (payload: any) => {
        // TODO: 这里会导致内存泄漏，需要优化
        this.loadAd().then((res: any) => {
          if (res) console.log('Home Banner Get Success!')
        })
        return payload
      }

      // 处理支点活动需要使用到的参数
      // 是一个临时活动
      const handlePivotInfo = (payload: any) => {
        const $$appInfo = _.get(sqbBridge, '$$appInfo', {})
        if (!isAlipay() || $$appInfo.appId === MY_CAMPUS_APP_ID) return payload

        const waitForTruthy = async (valueFn: () => boolean) => {
          return new Promise(resolve => {
            const checkCondition = () => {
              if (valueFn()) {
                clearTimeout(alipayPluginTimer as number)
                resolve(true)
              } else {
                alipayPluginTimer = setTimeout(checkCondition, 0) as unknown as number
              }
            }
            checkCondition()
          })
        }

        const outerActivity = _.get(payload, 'outerActivity')
        const alipayMerchantActivity = _.get(outerActivity, 'alipayMerchantActivity')
        if (!_.isEmpty(outerActivity) && !_.isEmpty(alipayMerchantActivity)) {
          const activities = _.get(alipayMerchantActivity, 'activities')
          const shopId = _.get(alipayMerchantActivity, 'shopId')
          if (_.includes(activities, 'PIVOT') && shopId) {
            // @ts-ignore
            my.loadPlugin({
              plugin: `${ALIPAY_MARKETING_PLUGIN_ID}@*`, // 指定要加载的插件 ID 和版本号，为 * 时自动选择版本
              success: () => {
                waitForTruthy(() => {
                  const { selectServiceTypeShow, activityDialogShowed, activityDialogShow } =
                    this.data
                  return !selectServiceTypeShow && activityDialogShow && activityDialogShowed
                }).then(result => {
                  if (shopId) {
                    this.setData({ isAlipayCouponDialogShow: result, alipayShopId: shopId })
                  }
                })
              }
            })
          }
        }
        return payload
      }

      const handleActivity = (payload: any) => {
        const { activity: activities } = payload
        const { serviceType } = _.get(payload, 'serviceTypes', {})

        // 计算优惠券
        const data = {
          combinedActivities: this.activitySvc.getNeedBuyCoupons(activities),
          freeCoupons: this.activitySvc.getFreeCoupons(activities),
          discountTag: this.activitySvc.getDiscountTag({ activities, serviceType }),
          currentDiscount: this.activitySvc.getCurrentDiscount(activities),
          otherDiscounts: this.activitySvc.getOtherDiscounts(activities)
        }

        _.assign(payload, data)
        this._render({ activity: activities, ...data })
        return payload
      }

      // 处理可配送地址相关逻辑
      const handleDeliveryArea = (payload: any) => {
        let { areas } = payload
        // const { lat, lon } = _.get(this.store, 'location') || {}
        const { lat, lon } = _.get(this.getStore(), 'location') || {}
        const { serviceType } = _.get(payload, 'serviceTypes', {})
        areas = serviceType === 1 ? areas : {}
        const { areas: list } = areas || {}
        const type = _.get(areas, 'type')
        _.forEach(list || [], item => {
          const { latitude, longitude } = item
          if (lat && lon && latitude && longitude) {
            const distance = getDistance(lat, lon, latitude, longitude)
            item.distance = countDistance(distance)
          }

          const typeMapping = {
            address: {
              nameKey: 'address',
              title: '支持配送地址',
              bubble: '注意商家支持的配送地址哦'
            },
            campus: {
              nameKey: 'campus_page_name',
              title: '支持配送区域',
              bubble: '注意商家支持的配送区域哦'
            },
            default: {
              nameKey: 'name',
              title: '支持配送区域',
              bubble: '注意商家支持的配送区域哦'
            }
          }

          const typeInfo = typeMapping[type as keyof typeof typeMapping] || typeMapping['default']

          item.name = item[typeInfo.nameKey]
          areas.title = typeInfo.title
          areas.bubble = typeInfo.bubble
        })
        _.assign(payload, { areas })
        this._render({ areas })
        return payload
      }

      // 处理营销弹窗相关参数
      const setMkssActivityDialogParams = (payload: any) => {
        const takeoutProfitSharing = _.get(payload, 'mcc.takeoutProfitSharing', false)
        _.assign(payload, { mkssActivityDialogParams: { takeoutProfitSharing } })
        this._render({ mkssActivityDialogParams: { takeoutProfitSharing } })
        return payload
      }

      const updateText = (payload: any) => {
        this.updateSubmitText()
        return payload
      }

      // 处理拼团活动用户渲染参数
      const handleGroupBuyInfo = (payload: any) => {
        if (!isAlipay()) {
          let { campus_activity: campusActivity } = this.getQuery() || {}
          if (campusActivity) {
            // 对编码数据两次解码防止乱码
            campusActivity = JSON.parse(decodeURIComponent(decodeURIComponent(campusActivity)))
            post('/dyeingGroupBuyingCustomer', campusActivity)
          }
        }
        return payload
      }

      p.then(setMkssActivityDialogParams)
      p.then(handleDeliveryArea)
      p.then(handleActivity)
      p.then(handlePivotInfo)
      p.then(handleAdContent)
      p.then(handleTableInfo)
      p.then(updateText)
      p.then(handleGroupBuyInfo)

      p.then(async payload => {
        const { serviceType } = _.get(payload, 'serviceTypes', {})
        await this.updateNoticeBarStatus(serviceType)
        return payload
      })
      // p.then(payload => {
      //   calcBulletExpand().then(bulletinExpandShow => this._render({ bulletinExpandShow }))
      //   return payload
      // })

      p.then(async payload => {
        const {
          store: { merchantSn }
        } = payload
        const { selectServiceTypeShow } = _.get(payload, 'serviceTypes', {})

        if (isWeixin()) {
          const isShowWxAct = await this.isShowWxDialog(merchantSn)
          if (isShowWxAct) {
            selectServiceTypeShow === false && this._render({ isShowWxAct: true })
            selectServiceTypeShow === true && this._render({ isShowWxActFlag: true })
          }
        }
        return payload
      })
      p.then(async payload => {
        const { isUseLocalCart } = this.data
        if (!isUseLocalCart) {
          const { serviceTypeChanged } = payload
          const cart = this.getCartData()
          this.checkMustOrder(cart)
          // 购物车有数据 toast 提示
          if (serviceTypeChanged && cart && cart.records && cart.records.length) {
            this.$toast(this.t('购物车商品有变动，请确认'))
          }
        }
      })

      p.then(async payload => {
        const { serviceType } = _.get(payload, 'serviceTypes', {})
        const { isOrderMessageShowed } = this.data
        const checkAcceptedOrder = async () => {
          // 店内点单
          if (serviceType === 2) {
            const shouldShowNotifyBar =
              // !isOrderMessageShowed && _.get(this, 'terminal.mealType') !== 'round_meal'
              !isOrderMessageShowed && _.get(this.getTerminal(), 'mealType') !== 'round_meal'
            if (shouldShowNotifyBar) {
              await fetchLatestOrder((data: any) => {
                const timeout = 2000
                if (data) {
                  setTimeout(
                    () =>
                      this._render({
                        isOrderMessageShowed: true,
                        orderMessageShow: true,
                        orderMessage: data
                      }),
                    timeout
                  )
                }
              })
            }
          } else {
            this._render({ orderMessageShow: false, isOrderMessageShowed: false })
          }
        }

        await checkAcceptedOrder()
        return payload
      })

      // p.then(payload => {
      //   const { setShareAppMessage, isJJZ } = this.data.sqbBridge
      //   const { store } = payload
      //   const { storeId } = store
      //   const params = {
      //     page_id: 'xgaugooilxg7',
      //     query_store_id: storeId
      //   }
      //   const getShareData = async (params: any, isJJZ: boolean, sqbBridge: any) => {
      //     try {
      //       if (isJJZ) {
      //         return await this.storeSvc.getShareMessage(params)
      //       } else {
      //         const { data } = await sqbBridge.request({
      //           url: '/v3/storeRenovation/getStoreRenovation',
      //           method: 'POST',
      //           data: params
      //         })
      //         return data.data
      //       }
      //     } catch (error) {
      //       return ''
      //     }
      //   }

      //   getShareData(params, isJJZ, this.data.sqbBridge)
      //     .then(shareData => {
      //       console.log(99991, shareData, makeShareOptions('meal', shareData, this))
      //       setShareAppMessage(makeShareOptions('meal', shareData, this))
      //     })
      //     .catch(() => {
      //       setShareAppMessage(makeShareOptions('meal', '', this))
      //     })

      //   return payload
      // })

      p.then(payload => {
        const { setShareAppMessage } = this.data.sqbBridge
        this.storeSvc
          .getShareInfo({
            store_id: _.get(payload, 'store.storeId'),
            template_id: _.get(payload, 'template.template_id')
          })
          .then(data => {
            setShareAppMessage(makeShareOptions('store', { ...data, page: 'meal' }, this))
            data && this._render({ storeShareData: data })
          })
          .catch(() => {
            setShareAppMessage(makeShareOptions('store', { page: 'meal' }, this))
          })

        return payload
      })

      p.then(payload => {
        checkShareGoods()
          .then(data => {
            this._onTapGoodItem(data)
            this._render({ needShowShareGoods: false })
          })
          .catch(() => {
            console.log('checkShareGoods no goodsId')
          })
        return payload
      })

      p.then(async payload => {
        await this.syncCartData()
        return payload
      })

      p.then(payload => {
        const {
          store: { storeId }
        } = payload
        !this.notNeedFetchIsCollection &&
          sqbBridge.fetch('isCollection', { store_id: storeId }).then((resp: any) => {
            this.notNeedFetchIsCollection = true
            this._render({
              isCollected: resp.data
            })
          })
        return payload
      })

      return p
    },
    async checkMustOrder(cart) {
      const { canPlaceOrder, selectServiceTypeShow, sqbBridge } = this.data
      const serviceType = sqbBridge.getServiceTypes(`serviceType`)

      // 店内点单的围餐
      if (selectServiceTypeShow || !canPlaceOrder || serviceType !== 2) {
        this.handleMkssActivityDialog(serviceType)
        return
      }

      // const { terminal } = this
      const terminal = this.getTerminal()

      const { tableStatus } = terminal || {}

      let flg = false

      if (isRoundMeal(this)) {
        if (tableStatus === 'WAIT_CLEAN' || tableStatus === 'FREE') {
          this._render({
            showSelectUserCount: true
          })
          flg = true
        } else {
          this._render({
            showSelectUserCount: false
          })
        }
      } else {
        const must_order_enable = await sqbBridge.getMcc(MCC_MUST_ORDER_ENABLE)

        if (must_order_enable && !_.get(cart, 'people_num')) {
          this._render({
            showSelectUserCount: true
          })
          flg = true
        } else {
          this._render({
            showSelectUserCount: false
          })
        }
      }

      this.handleMkssActivityDialog(serviceType)
      return flg
    },
    requestPolicy(serviceType) {
      // @ts-ignore
      requestPolicyWithBridge
        .call(this.data.sqbBridge, [
          // {
          //   agreementType: 'sqb_c_privacy',
          //   signObjId: '',
          //   // independent: true,
          //   //   force: true
          // },
          {
            agreementType: 'sqb_c_auth',
            once: true
            // signObjId: '',
            // force: true
            // independent: true,
          }
        ])
        .then(e => {
          if (_.get(e, 'status') === 'REFUSE' && serviceType === 1) {
            wx.showModal({
              title: '',
              content: '只有您同意相关协议和政策并提供必要信息的前提下，我们才能继续为您提供服务',
              confirmText: '我知道了',
              showCancel: false,
              success: res => {
                // @ts-ignore
                if ((isWeixin() && res.confirm) || (isAlipay() && res.success)) {
                  this.requestPolicy(serviceType)
                }
              }
            })
          }
        })
    },
    handleMkssActivityDialog() {
      const { showSelectUserCount } = this.data
      if (showSelectUserCount) {
        this.update({ activityDialogShow: false })
        // 不使用绑定函数，避免内存泄漏
        this.selectUserCountCallback = async (e: any) => {
          // 直接调用原方法并传入this上下文，而不是使用bind
          await selectUserCountCallback(e, this, () => {
            this._render({
              mustOrderTip: true,
              showSelectUserCount: false
            })
            this.initLocalCart()
          })
          this.update({ activityDialogShow: true })
        }
      } else {
        this.update({ activityDialogShow: true })
      }
    },
    onCloseMkssActivityDialog() {
      this.update({ activityDialogShowed: true })
    },
    updateSubmitText({
      buttonText
    }: {
      buttonText?: string
      isSelected?: boolean
      depositAmountLevel?: number
    } = {}) {
      this.update({
        submitText: buttonText ? buttonText : isRoundMeal(this) ? '去下单' : '去结算'
      })
    },
    /**
     * 购物车储值勾选回调函数
     */
    onStoredRuleSelectedChange(e) {
      const { isWeapp } = this.data
      const mkssData = _.get(e, 'detail', {})
      const { isSelected, ruleId, interestId } = mkssData
      const storedPaySelectedChanged =
        _.isBoolean(isSelected) && this.data.isStoredPaySelected !== isSelected
      console.log(
        'onStoredRuleSelectedChange',
        mkssData,
        storedPaySelectedChanged,
        this.data.isStoredPaySelected,
        isSelected
      )

      // 保存购物车储值组件回调参数
      this.ruleId = ruleId
      this.interestId = interestId
      this._render({
        payway: isSelected ? 101 : isWeapp ? 3 : 1,
        isStoredPaySelected: !!isSelected
      })
      this.updateSubmitText(mkssData)

      const cart = this.getCartData()

      if (storedPaySelectedChanged && cart) this.bus.emit(CART_CHANGE_EVENT, cart)
    },
    selectUserCountCallback(e) {
      return new Promise(resolve => {
        selectUserCountCallback(e, this, () => {
          this._render({
            mustOrderTip: true,
            showSelectUserCount: false
          })
          this.initLocalCart()
          // @ts-ignore
          resolve()
        })
      })
    },
    bindCartFocusClick(e) {
      onBindFocusClick(e, this)
    },
    bindListfocusClick(e) {
      onBindFocusClick(e, this)
    },
    confirmCountChange(e) {
      const { count } = e.detail
      if (count > 0) {
        onEnterCallback(this, count)
      } else if (count === 0) {
        this.$alert({
          title: this.t('提示'),
          showCancelButton: true,
          message: this.t('确定删除该商品吗?'),
          confirmButtonText: this.t('确定'),
          cancelButtonText: this.t('取消')
        }).then(() => {
          onEnterCallback(this, count)
        })
      }
    },
    cancelCountChange() {
      onCancelCallback(this)
    },
    // onCartChanged(cart) {
    //   this.afterCartChanged(cart);
    // },
    async mainProcess(payload) {
      // 处理query带参数 serviceTypeName
      const { sqbBridge, needAuthPrivacyList } = this.data
      // 分享， 会带参数buyAgainOrderType， 主要为默认选择serviceType, 是一种变通方法
      const { serviceTypeName, buyAgainOrderType, hideServiceType, from } = this.getQuery() || {}
      const queryServiceTypeName = buyAgainOrderType || serviceTypeName
      const { serviceTypeChanged } = payload as THomePayload

      if (
        queryServiceTypeName &&
        (hideServiceType === 'true' || hideServiceType === true) &&
        !serviceTypeChanged
      ) {
        // 如果带serviceTypeName， 但是服务已经关闭
        // 则需要跳转至异常页
        // const isServiceTypeEnable = _.find(_.get(payload, 'serviceTypes.serviceTypeList'), { serviceTypeName });
        // if (!isServiceTypeEnable) {
        //   return this.redirect('page-closing');
        // }
        // 存在 serviceTypeName 参数，切换服务方式时无需更新
        updateServiceTypesData(payload, queryServiceTypeName, from, serviceTypes => {
          sqbBridge.updateServiceTypes(serviceTypes)
          _.assign(payload, { serviceTypes })
        })
      }
      // 更新协议signObjId
      if (needAuthPrivacyList) {
        const merchantId = _.get(payload, 'store.merchantId')
        _.forEach(needAuthPrivacyList, (item: any) => {
          item.signObjId = merchantId
        })
        this._render({ needAuthPrivacyList })
      }

      // 更新主题
      this.updateTheme()
      // 更新营销组件主题
      this.updateMkssTheme(_.get(payload, 'template'))

      return this.beforeRender(payload)
        .then((payload: any) => {
          return this.render(payload)
        })
        .then((payload: any) => {
          setTimeout(() => {
            this.afterRender(payload)
          }, 0)
          return payload
        })
    },
    onAuthPhoneCallback(evt) {
      const { sqbBridge } = this.data
      const { type } = evt
      weixinAuthPhoneTimeRecord(this, type || 'success')
      initAuthUserData(this) // re init if auth is clicked !!
      this.onPlaceOrder()
      sqbBridge.sls('INFO', { type: 'authPhone:success', data: {} })
    },
    onAuthPhoneCallbackError(evt) {
      const { sqbBridge } = this.data
      const { type } = evt
      weixinAuthPhoneTimeRecord(this, type || 'error')
      initAuthUserData(this) // re init if auth is clicked !!
      this.onPlaceOrder()
      sqbBridge.sls('INFO', { type: 'authPhone:error', data: {} })
    },
    onAuthPhoneCallbackDeny(evt) {
      const { sqbBridge } = this.data
      const { type } = evt
      weixinAuthPhoneTimeRecord(this, type || 'deny')
      initAuthUserData(this) // re init if auth is clicked !!
      this.onPlaceOrder()
      sqbBridge.sls('INFO', { type: 'authPhone:deny', data: {} })
    },
    onAuthOnceUser() {
      weixinAuthUserTimeRecord(this)
      initAuthUserData(this) // re init if auth is clicked !!
    },
    onBackToTop() {
      this._render({
        goodsListScrollTop: 0,
        toTopView: 'top-view',
        currentCategoryIdAnchor: 'top-view'
      })

      // this.onScrollHomePage({detail: {scrollTop: 0}})
    },
    initServices(options) {
      this.storeSvc = new StoreService(options)
      this.cartSvc = new CartService(options)
      this.cashierSvc = new CashierService(options)
      this.goodsSvc = new GoodsService(options)
      this.orderSvc = new OrderService(options)
      this.slsLogSvc = new SlsLogService(options)
      this.activitySvc = new ActivityService({})
      // @ts-ignore
      this.adsenseSvc = new AdsenseService()
      this.tableSvc = new TableService(options)
    },
    init() {
      // @ts-ignore
      const { sqbBridge, theme } = this.data
      // 设置bus
      this.bus = createEventBus(this.bindAfterCartChanged)
      // 初始化services
      const options = this.getQueryOpts()
      this.initServices(options)
      // @ts-ignore
      // 设置方法集的context
      setMethodCtx(this)
      setDiscountCtx(this)
      // 设置导航栏
      // backButtonColor 仅对支付宝有效
      // 颜色需要设置为6位，否则支付宝不生效
      // setNavigation(config.bgNavigationColor || '#000000', config.backButtonColor)
      // setNavigation(_.get(theme, 'settings.navigationBarColor'), '#000000')
      setNavigation(_.get(theme, 'settings.navigationBarColor'), '#000000')
      //初始化hooks
      this.hooksManager = HooksManager.init(this)
      // 设置商品列表滚动方法集合
      // setGoodsScrollerCtx(this);
      // 设置列表滚动方法集合
      // CategoryScroller.setCtx(this);

      // 当用户切换时
      this.checkUserIsSwitch()
      const subscribe = _.get(sqbBridge, 'observers.authLogin.subscribe')
      if (_.isFunction(subscribe)) {
        // 创建绑定了this的回调函数，避免箭头函数捕获this
        this.onSwitchLoginSuccessCallback = this.onSwitchLoginSuccess.bind(this)
        this.onSwitchLoginFailCallback = this.onSwitchLoginFail.bind(this)

        // 存储订阅的处理函数，在组件销毁时使用
        this.switchLoginHandler = onSwitchLogin({
          bridge: sqbBridge,
          context: this,
          onSuccess: this.onSwitchLoginSuccessCallback,
          onFail: this.onSwitchLoginFailCallback,
          cartSvc: this.cartSvc
        })

        // 订阅登录事件
        subscribe(this.switchLoginHandler)
      }

      this.__init = true
    },
    // async loadCart(serviceType) {
    //   const { sqbBridge, categories } = this.data;
    //   if (!serviceType) {
    //     serviceType = sqbBridge.getServiceTypes(`serviceType`);
    //   }

    //   return callPlugin('loadCart', { serviceType }).then((data) => {
    //     const { cart } = data || {};
    //     if (cart) {
    //       this.bus.emit(CART_CHANGE_EVENT, cart);
    //       this.setData({ cart: _.cloneDeep(cart) });
    //     }
    //     // 再来一单
    //     const { isReOrder } = this.data;
    //     if (isReOrder && cart && cart.records.length) {
    //       // 延缓加载，优化体验
    //       const timeout = 1000;
    //       const timerId = setTimeout(() => {
    //         this._render({ isCartShow: true, isReOrder: false });
    //         clearTimeout(timerId);
    //       }, timeout);
    //     }
    //     return cart;
    //   });
    // },
    onAdsBannerSuccess(e) {
      const { sqbBridge } = this.data
      const pid =
        sqbBridge.is_WX_JJZ || sqbBridge.isJJZ ? JJZ_ORDER_HOME_AD_PID : SQB_ORDER_HOME_AD_PID

      const lists = e.detail || []

      const hasMatch = _.some(lists, (item: any) => item.pid === pid && item.result)

      this.update({ showAdsBanner: hasMatch })
      if (this.adsBannerResolve) {
        this.adsBannerResolve(hasMatch)
      }

      if (hasMatch) {
        sqbBridge.track('SmMpUIViewDisplay', {
          sm_uiview_name: '品牌购顶部banner',
          sm_page_name: '点单页'
        })
      }
    },
    loadAd() {
      const { sqbBridge } = this.data
      const adsBannerFirst = this.call('getMiniProgramConfig', 'ADS_BANNER_FIRST') || false
      const aopBannerPromise = () => {
        return new Promise(resolve => {
          const isAdShow = sqbBridge.getConfig(
            isWeixin() ? 'ADSENSE_SHOW_IN_WECHAT' : 'ADSENSE_SHOW_IN_ALIPAY'
          )

          if (!isAdShow) {
            // @ts-ignore
            resolve()
          }

          this.getExtraCampusData().then(() => {
            this.setAopTrackParams({ ...home_default_aop_track_params })
          })
          const res = this.setAOPResource({})
          resolve(res && res.length ? true : false)
        })
      }

      const adsBannerPromise = () => {
        return new Promise((resolve, reject) => {
          // 添加超时处理
          const timeoutId = setTimeout(() => {
            this.adsBannerResolve = null // 清除引用
            reject('广告加载超时')
          }, 5000) // 设置合理的超时时间

          this.setAdsBannerQuery()
          this.adsBannerResolve = result => {
            clearTimeout(timeoutId) // 清除超时定时器
            this.adsBannerResolve = null // 清除引用
            resolve(result)
          }
        })
      }

      // 放入队列
      const promises = [aopBannerPromise]
      if (adsBannerFirst) promises.unshift(adsBannerPromise)
      else promises.push(adsBannerPromise)

      // 依次执行
      return new Promise(resolve => {
        promises.reduce((prev, cur, i, arr) => {
          return prev.then(() => {
            // @ts-ignore
            return cur()
              .then((res: any) => {
                if (res) {
                  // 如果有广告图片，头部高度会有变化
                  this.isHeaderHeightChanged = false
                  resolve(true)
                  return Promise.reject()
                }
                if (i === arr.length - 1) resolve(false)
              })
              .catch(err => {
                console.error(err)
              })
          })
        }, Promise.resolve())
      })
    },
    /**
     * 设置广告组件query参数
     */
    async setAdsBannerQuery() {
      const { sqbBridge, isWeapp } = this.data
      const pid =
        sqbBridge.is_WX_JJZ || sqbBridge.isJJZ ? JJZ_ORDER_HOME_AD_PID : SQB_ORDER_HOME_AD_PID

      let smart_order_type = isRoundMeal(this)
        ? (await isPayFirstRoundMeal(this))
          ? 'PAY_FIRST_TABLE_ORDER'
          : 'EAT_FIRST_ORDER'
        : 'subscribe_order'
      this.update({
        adsQuery: {
          pid, // 广告位ID，用于识别流量位，限定广告内容
          smart_order_type
        },
        showAdFlag: isWeapp
      })
    },
    async syncCartData() {
      //  围餐开启数据同步
      if (isRoundMeal(this)) {
        await callPlugin('roundMealCheckVersion', 'start')
      } else {
        await callPlugin('roundMealCheckVersion', 'end')
      }
    },
    onShow() {
      console.log('@home #onShow - 页面显示', {
        time: Date.now(),
        serviceType: _.get(this.data, 'serviceType'),
        cartCount: _.get(this.cartStore, 'count', 0)
      })
      const startTime = Date.now()
      console.log('@home - onShow 开始', startTime)
      const self = this
      // @ts-ignore
      const { sqbBridge, isLoad } = self.data

      if (self._isReload) {
        console.log('@home - onShow 跳过 - 因为正在重新加载')
        return
      }

      // 检查业务异常
      console.log('@home - 开始检查业务异常')
      this.checkBusinessException((err: any, result: any) => {
        console.log('@home - 业务异常检查完成', { hasError: !!err, hasResult: !!result })
        if (err) {
          console.error(err)
          return
        }
        if (result) {
          this.setData({ ...result })
        }
      })

      // 检查用户是否切换
      console.log('@home - 开始检查用户切换')
      self.checkUserIsSwitch()
      console.log('@home - 用户切换检查完成')

      // 同步购物车数据
      console.log('@home - 开始同步购物车数据', { isRoundMeal: isRoundMeal(this) })
      self.syncCartData()
      console.log('@home - 购物车数据同步完成')

      // @ts-ignore
      const floatingBubbleVisible = storage('floatingBubbleVisible')
      self.update({ floatingBubbleVisible })
      console.log('@home - 已更新浮动气泡可见性', { floatingBubbleVisible })

      // 初始化组件
      if (!self.__init) {
        console.log('@home - 开始首次初始化')
        self.init()
        console.log('@home - 首次初始化完成')
      }

      // 支付宝特殊处理
      if (isAlipay()) {
        console.log('@home - 开始支付宝授权数据初始化')
        initAuthUserData(self)
        console.log('@home - 支付宝授权数据初始化完成')
      }

      // 处理缓存和购物车状态
      if (isLoad) {
        const storeId = _.get(self.getStore(), 'storeId')
        // @ts-ignore
        const goodNeedReload = storage(handleHashKey('goodsChange', storeId))
        console.log('@home - 缓存检查', { storeId, goodNeedReload, isLoad })

        if (goodNeedReload) {
          storage(handleHashKey('goodsChange', storeId), null)
          console.log('@home - 商品需要重新加载', { storeId })
        } else {
          // 每次回来首页都要重新拉下购物车
          const cartStore = CartStore.getInstance(self, self.data)
          console.log('@home - 开始重置购物车', { hasCartStore: !!cartStore })

          // 初始化本地购物车,防止支付后从订单详情返回到点单首页时,存在 去结算 可点击的时间差造成可点击产生异常问题
          if (cartStore) {
            cartStore.reset()
          }

          console.log('@home - 开始初始化本地购物车')
          self.initLocalCart({ initLocal: true })
          console.log('@home - 本地购物车初始化完成')
        }

        // 处理服务类型相关逻辑
        const { serviceTypeName, serviceTypeList, selectServiceTypeShow } =
          sqbBridge.getServiceTypes()
        const {
          serviceTypeName: _serviceTypeName,
          selectServiceTypeShow: currentSelectServiceTypeShow
        } = self.data

        console.log('@home - 服务类型检查', {
          serviceTypeName,
          _serviceTypeName,
          selectServiceTypeShow,
          currentSelectServiceTypeShow,
          needUpdateSelectServiceTypeShow: currentSelectServiceTypeShow !== selectServiceTypeShow,
          needUpdateServiceTypeName: serviceTypeName !== _serviceTypeName
        })

        // fix: 解决店内扫一扫时，到围餐页面再返回时还弹窗显示的问题
        // TODO: 这个是临时解决版本， 应该数据变化应该重新刷新
        if (currentSelectServiceTypeShow !== selectServiceTypeShow) {
          self._render({
            selectServiceTypeShow
          })
          console.log('@home - 已更新服务类型显示状态', { selectServiceTypeShow })
        }

        if (serviceTypeName !== _serviceTypeName) {
          const index = _.findIndex(serviceTypeList, { active: true })
          _.forEach(serviceTypeList, (item, idx) => {
            item.active = false
            // @ts-ignore
            item.active = idx === index
          })

          console.log('@home - 开始服务类型切换', { index, serviceTypeName })

          self._render(
            { _selectedServiceTypeIndex: index, serviceTypeName, serviceTypeList },
            () => {
              console.log('@home - 开始提交服务类型')
              self.onSubmitServiceType()
              console.log('@home - 服务类型提交完成')
            }
          )
        }
      }

      console.log('@home - onShow 完成', { duration: Date.now() - startTime })
    },
    onHide() {
      console.log('@home #onHide - 页面隐藏', {
        time: Date.now(),
        hasUnsubmittedChanges: !!_.get(this.cartStore, 'count', 0)
      })
      console.log('@home - onHide')
      const self = this
      callPlugin('roundMealCheckVersion', 'end')
      const { isOrderMessageShowed } = self.data
      self.cashierSvc && self.cashierSvc.stop()
      self.update({
        orderMessageShow: false,
        isOrderMessageShowed: isOrderMessageShowed || false
      })
    },
    onClick(...args) {
      console.log(args)
    },
    checkUserIsSwitch() {
      const { sqbBridge } = this.data
      // @ts-ignore
      const loginInfo = sqbBridge.getLoginInfo()
      // @ts-ignore
      onSwitchLogin({
        bridge: sqbBridge,
        context: this,
        onSuccess: () => this.onSwitchLoginSuccess(),
        onFail: this.onSwitchLoginFail,
        cartSvc: this.cartSvc
      })({ detail: { loginInfo } })
    },
    /**
     * 收藏 取消收藏
     */
    async toggleCollect() {
      const { isCollected } = this.data
      this.update({ isCollected: !isCollected })
      if (isCollected) {
        // 取消收藏（从库中取消）
        await this.storeSvc.cancelCollectionStore()
      } else {
        // 收藏(加入到库中)
        await this.storeSvc.addCollectionStore()
      }
      this.$toast(isCollected ? this.t('已取消收藏') : this.t('收藏成功'))
    },
    /**
     * 一级分类点击事件
     * @param {*} event categoryId分类ID | event点击对象
     */
    onTapCategory(event) {
      console.log('onTapCategory', event)
      // @ts-ignore
      const {
        isRetail,
        activeCategoryId: _activeCategoryId,
        errOccurred,
        goodsDisplayModeText
      } = this.data
      const activeCategoryId =
        typeof event === 'string' ? event : _.get(event, 'currentTarget.dataset.categoryId')

      if (isRetail) {
        if (_activeCategoryId === activeCategoryId && !errOccurred) {
          return
        }

        const mScrollActiveCategoryId = `menu-cid-${activeCategoryId}`
        const sScrollActiveCategoryId = `sub-category-${activeCategoryId}`
        this.setData(
          {
            currentCategoryIdAnchor: '',
            retailLoadingType: 0,
            activeCategoryId,
            retailActiveSubCategoryId: activeCategoryId,
            // @ts-ignore
            categoryScrollIntoView: mScrollActiveCategoryId,
            retailSubCategoryScrollIntoView: sScrollActiveCategoryId,
            retailGoods: [],
            retailSubCategoryToggle: false,
            retailNoMoreGoods: false
          },
          () => {
            this.getSubCategoryData(activeCategoryId)
            this.loadPageRetailGoods(activeCategoryId, '', 1, 'primary')
          }
        )
      } else {
        // const { simplePages } = this.data
        // const currentCategory = _.find(simplePages, item => item[0] === activeCategoryId)
        const mScrollActiveCategoryId = `menu-cid-${activeCategoryId}`

        this.setData({ activeCategoryId })

        setTimeout(() => {
          this.setData({
            currentCategoryIdAnchor: `category-${activeCategoryId}-anchor`
          })
          if (goodsDisplayModeText !== 'grid') {
            this.setData({
              // @ts-ignore
              categoryScrollIntoView: mScrollActiveCategoryId
            })
          }
        }, 50)

        // const goodsListScrollTop = _.get(currentCategory, '1.0', 0)

        // setTimeout(() => {
        //   this.setData({ goodsListScrollTop })
        // }, 0)
      }
    },
    selectCombo(event) {
      const { detail: goods = {} } = event
      if (goods.package) {
        return this.linkToCombo(goods.id)
      }
    },
    // 加入购物车或购物车商品数量+1
    onAddToCart(event) {
      // TODO: 校验库存统一这里处理
      this.recordRoundMealPurchaseTime()
      onAddToCart.call(this, event)
    },
    // 购物车商品数量-1
    onRemoveFromCart({ detail: goods = {} }) {
      this.recordRoundMealPurchaseTime()
      // @ts-ignore
      onAddSpuToCart.call(this, goods, -1)
    },
    recordRoundMealPurchaseTime() {
      if (isRoundMeal(this) && this.roundMealPurchaseTime) {
        this.roundMealPurchaseTime.push(new Date().getTime())
      }
    },
    // 清空购物车
    async onClearCart() {
      const { sqbBridge } = this.data
      const service_type = sqbBridge.getServiceTypes(`serviceType`)
      const body = {
        service_type,
        compatible: true
      }
      if (isRoundMeal(this)) {
        const { avatarUrl: user_icon, nickName: user_name } = await sqbBridge.getMiniProgramUser()
        _.assign(body, { user_icon, user_name })
      }
      this.cartSvc.clearCartData(body).then((data: any) => {
        this._render({
          isCartShow: false
        })
        this.cartStore.clear(data)
      })
    },
    authPhoneReject() {
      const { sqbBridge } = this.data
      sqbBridge.storage('authPhone', { updateAt: Date.now() })
      this.onPlaceOrder()
    },
    authPhoneClick: _.throttle(
      async function (this: any) {
        try {
          await aliAuthGetPhone(this)
          await this.onPlaceOrder()
        } catch (err) {
          console.error('#authPhoneClick', err)
        }
      },
      800,
      { trailing: false }
    ),
    // 去结算
    onPlaceOrder: _.throttle(
      async function (this: any) {
        console.log('onPlaceOrder')
        // 获取SubmitBar组件实例
        let SubmitBar = null
        try {
          if (isAlipay()) {
            SubmitBar = this.$selectComponent('#submit-bar')
          } else {
            SubmitBar = this.selectComponent('#submit-bar')
          }
          const { hasCartDataShow } = SubmitBar.data
          if (!hasCartDataShow) {
            return
          }
        } catch (err) {
          console.error('#SubmitBar', err)
        }
        // @ts-ignore
        const { sqbBridge, isStoredPaySelected, isRetail } = this.data
        const cart = this.getCartData()
        // @ts-ignore
        const { isSelectedMustGoods, mustCategoryId, isLowerMinDeliveryPrice, activity } = this.data
        //拿到校园埋点数据以及校园相关携带参数
        const { campusOrderStatistics, campus_activity: campusActivity } = this.getQuery()
        const groupBuyingActivity = _.head(_.get(activity, 'groupBuyingActivities'))

        // 判断是否已选必购分类
        if (!isSelectedMustGoods) {
          this._render({ isHighLight: true, isCartShow: false })
          // 滚动至相应分类
          this.onTapCategory(mustCategoryId)
          this._render(
            {
              showMustTip: false,
              categoryScrollIntoView: 'menu-cid-' + mustCategoryId
            },
            () => {
              setTimeout(() => {
                this._render({ showMustTip: true })
              }, 500)
            }
          )
        } else {
          if (!(await canSubmitOrder(this))) return
          if (!isLowerMinDeliveryPrice) {
            // 围餐模式跳转至购物车页

            if (isRoundMeal(this)) {
              const scene = sqbBridge.getExtra('scene')
              // 有终端，且扫的是点单码，才有必要refreshTerminal
              if (TABLE_SCENES.includes(scene)) {
                await sqbBridge.refreshTerminalInfo()
              }
              const tableStatus = _.get(this, 'terminal.tableStatus')
              if (tableStatus === 'WAIT_CLEAN' || tableStatus === 'FREE') {
                this._render({
                  showSelectUserCount: true
                })
              } else {
                // @deprecated 将不能使用isPayFirstRoundMeal()方法进行判断
                if ((await isPayFirstRoundMeal(this)) || (await this.isPayFirstRoundMeal())) {
                  this.go('page-submit-firstpay-round')
                } else {
                  this.go('page-cart')
                }
              }
            } else {
              if (await this.checkMustOrder(cart)) return

              // 储值并充值 神策事件
              isStoredPaySelected &&
                this.call('track', 'MKShoppingCartClick', { click_name: '充值并支付' })

              const { isUseLocalCart } = this.data
              const goToSubmit = () => {
                // 轻餐模式进入结算页
                this.go('page-submit', {
                  isStoredPaySelected,
                  ruleId: this.ruleId,
                  goodsCouponCount: this.goodsCouponCount,
                  campusOrderStatistics,
                  campusActivity,
                  groupBuyingActivity: JSON.stringify(groupBuyingActivity),
                  memberPoints: this.memberPoints,
                  isRetail
                })
              }

              if (isUseLocalCart) {
                const submit = () => {
                  this.$clearToast()
                  this.cartStore.hooks.submitBefore.callAsync({}, (error: any, data = {}) => {
                    if (error) {
                      sqbBridge.sls('INFO', {
                        type: 'cartStore:submitBefore:error',
                        data: { data: { error, ...data } }
                      })
                      StoreHelper.log('cartStore:submitBefore 结算错误 [ERROR]', { error })
                      this.$alert({
                        title: '错误信息',
                        message: '加购商品异常，请重试',
                        showCancelButton: false
                      }).then(() => {
                        this.$loading('重试中...')
                        submit()
                      })
                      // this.alert('加购商品异常，请重试', '错误信息', '重试', () => {
                      //   this.closeAlert()
                      //   this.$loading('重试中...')
                      //   submit()
                      // })
                    } else {
                      goToSubmit()
                    }
                  })
                }
                submit()
              } else {
                goToSubmit()
              }
            }
          }
        }
      },
      800,
      { trailing: false }
    ),

    linkToCombo(item_id) {
      const { store, serviceType, serviceTypeName, canPlaceOrder } = this.data
      return this.call('navigateTo', {
        plugin: {
          name: 'scanPlugin',
          component: 'page-combo'
        },
        query: {
          storeId: (store as any).id || (store as any).storeId,
          item_id,
          service_type: serviceType,
          serviceTypeName,
          _: +new Date(),
          disabled: !canPlaceOrder
        }
      })
    },
    // 选规格
    async onTapGoodChoose(e) {
      this._onTapGoodItem(e.detail)
    },
    _onTapGoodItem(currentGood: { id?: string; [key: string]: any }) {
      const { serviceType, sqbBridge, imageDisplayConfig } = this.data
      const goodsItem = currentGood || {}
      const { id: item_id, item_tag, hot_sale, hot_sale_seq, out_of_stock, sale_count } = goodsItem
      const { goodsImageSizeIndex } = imageDisplayConfig || {}

      // 获取商品详情
      this.$loading(this.t('加载中'))

      this.goodsSvc
        .goodsDetail({
          item_id,
          service_type: serviceType,
          display_image_size: goodsImageSizeIndex
        })
        .then(({ data }: { data: any }) => {
          sqbBridge.setShareAppMessage(makeShareOptions('goods', data, this))
          this.$clearToast()

          // 套餐商品
          if (isPackageType(_.get(data, 'item.spu_type'))) {
            return this.linkToCombo(item_id)
          }

          this._render({
            goodDetailShow: true,
            currentGood: _.merge(data, {
              item: { item_tag },
              hot_sale,
              hot_sale_seq,
              out_of_stock,
              sale_count
            })
          })
        })
        .catch((error: any) => {
          console.error('onTapGoodChoose', error)
          this.$clearToast()
        })
    },
    async onTapGoodItem(e) {
      this._onTapGoodItem(e.detail)
    },
    onScrollCategories: _.debounce(function (this: any, data) {
      const { activeCategoryId } = data
      const { isRetail, goodsDisplayModeText } = this.data
      if (isRetail) {
        this.handleRetailCategoryChange(activeCategoryId)
      }
      if (goodsDisplayModeText === 'grid') {
        const { categoriesWithOffset: categories } = this.data
        const category = _.find(categories, { id: activeCategoryId })
        if (category) {
          this.setData({ activeCategoryId: category.id, categoryScrollLeft: category.offset[0] })
        }
      }
    }, 20),
    /**
     * 滚动整屏
     * @param event
     */
    onScrollHomePage: _.debounce(async function (this: any, event) {
      const { isRetail } = this.data
      if (!isRetail) {
        const { scrollTop } = event.detail
        const top = HEADER_BAR_HEIGHT + STATUS_BAR_HEIGHT
        const stickyRatio = Math.min(top, scrollTop) / top
        let isScrolled = false

        if (stickyRatio > 0.6) {
          isScrolled = true
        }

        if (isScrolled !== prevScrolled) {
          this._render({ isScrolled })
        }

        prevScrolled = isScrolled
        this.update({ stickyRatio, scrollTop, toTopView: '', currentCategoryIdAnchor: '' })

        this.loadGoodsListV2(event)
      }
    }, 80),
    loadGoodsListV2(event) {
      const { pages, categories, loadedGoods, sqbBridge } = this.data
      const { serviceType, init, goodsDisplayModeText, goodsDisplayMode } = this.data

      const viewportOffset = sqbBridge.getConfig('PLUGIN_GOODS_VIEWPORT_OFFSET')
      const firstPageRenderGoodsCount = sqbBridge.getConfig(
        'PLUGIN_GOODS_FIRST_PAGE_RENDER_GOODS_COUNT_V2'
      )
      return (
        this.homeStore
          // @ts-ignore
          .loadGoodsV2(event, pages, serviceType, {
            categories,
            loadedGoods,
            init,
            viewportOffset,
            firstPageRenderGoodsCount,
            mode: goodsDisplayModeText,
            display_image_size: goodsDisplayMode + 1
          })
          .then(async ({ pages, renders }: { pages: any; renders: any }) => {
            this.afterGoodsListChanged(pages, renders)
          })
      )
    },
    onCloseGoodDetailSheet() {
      const { storeShareData, sqbBridge } = this.data
      const { setShareAppMessage } = sqbBridge

      setShareAppMessage(makeShareOptions('store', { ...storeShareData, page: 'meal' }, this))
      this._render({ goodDetailShow: false, currentGood: null })
    },
    // 展/收缩公告内容
    onBulletinExpand() {
      const { bulletinExpand } = this.data
      this._render({ bulletinExpand: !bulletinExpand })
    },
    // 领取免费优惠券
    onClickFreeCouponTag() {
      const { sqbBridge } = this.data
      const { storeId } = sqbBridge.getStore()
      sqbBridge.navigateTo({
        path: getPageUrl('page-coupon-activities', sqbBridge.isJJZ),
        target: 'page',
        query: {
          storeId,
          form: 'emenu'
        }
      })
    },
    // 关闭微信活动
    onCloseWxActSheet() {
      this._render({ isShowWxAct: false })
    },
    // 切换服务类型相关
    onTapChangeServiceType() {
      if (_.size(this.data.serviceTypeList) > 1) this._render({ selectServiceTypeShow: true })
    },
    /**
     * 确认选择某个serviceType
     */
    async onSubmitServiceType() {
      const {
        _selectedServiceTypeIndex: currentServiceTypeIndex,
        serviceTypeList,
        // @ts-ignore
        isShowWxActFlag,
        store
      } = this.data
      const serviceTypeName = _.get(serviceTypeList, `[${currentServiceTypeIndex}].serviceTypeName`)
      const serviceType = _.get(serviceTypeList, `[${currentServiceTypeIndex}].serviceType`)

      this._render(
        {
          selectServiceTypeShow: false,
          currentServiceTypeIndex,
          serviceTypeList
        },
        () => {
          if (isShowWxActFlag === true) {
            this._render({ isShowWxActFlag: false, isShowWxAct: true })
          }
        }
      )

      this.onServiceTypesChanged({
        serviceType,
        currentServiceTypeIndex,
        selectServiceTypeShow: false,
        // @ts-ignore
        storeId: (store as any).storeId,
        serviceTypeList,
        serviceTypeName
      })
    },
    /**
     * 选择服务类型
     * @param event
     */
    onServiceSwitch(event) {
      let { serviceTypeList } = this.data
      const type = _.get(event, 'currentTarget.dataset.type')
      // @ts-ignore
      serviceTypeList = serviceTypeList.map((item: any) => {
        const { serviceTypeName } = item
        const active = serviceTypeName === type
        return { ...item, active }
      })
      const payload = { serviceTypeList }
      // 切换服务方式时设置营销弹窗（activityDialogShow）隐藏
      if (type !== this.data.serviceTypeName) {
        _.assign(payload, { activityDialogShow: false })
      }
      this._render(payload)
    },
    /**
     * 切换服务类型
     */
    async onServiceSwitchEnter() {
      const { serviceTypeList, sqbBridge, serviceTypeName } = this.data
      const _selectedServiceTypeIndex = _.findIndex(serviceTypeList, ({ active }) => active)
      if (_selectedServiceTypeIndex === -1) {
        return
      }
      // @ts-ignore
      const { action } = _.nth(serviceTypeList, _selectedServiceTypeIndex) as { action: string }
      if (action === 'scan') {
        sqbBridge.sls('INFO', {
          type: 'scanCode:start',
          data: {
            serviceTypeList,
            _selectedServiceTypeIndex
          }
        })
        // 需要设置为false
        // 否则扫一扫其他门店后， 会执行onShow
        // 导致storeId 等信息不正确
        // this.setData({ isLoad: false })
        this.setData({ selectServiceTypeShow: false })
        this._isReload = true
        sqbBridge
          .scanCodeV2()
          .then(async (qrcode: string | number | boolean) => {
            sqbBridge.sls('INFO', {
              type: 'scanCode:success',
              data: {
                qrcode
              }
            })
            this.$loading(this.t('加载中...'))
            if (!qrcode) {
              this.setData({ selectServiceTypeShow: true })
              return
            }
            // 临时解决支付宝扫一扫后崩溃的问题
            // 这个是不严谨的，需要找出根本原因
            if (isAlipay()) {
              setTimeout(async () => {
                await this.reload(qrcode)
              }, 50)
            } else {
              await this.reload(qrcode)
            }
          })
          .catch((err: any) => {
            console.error('#scanCodeV2', err)
            sqbBridge.sls('INFO', {
              type: 'scanCode:error',
              data: {
                err
              }
            })
            this.$clearToast()
            const { serviceTypes } = this.data
            _.assign(serviceTypes, { selectServiceTypeShow: true })
            sqbBridge.updateServiceTypes(serviceTypes)
            this.setData({ selectServiceTypeShow: true, serviceTypes })
          })
          .finally(() => {
            sqbBridge.sls('INFO', {
              type: 'scanCode:finally',
              data: {
                isReOrder: false
              }
            })
            this.setData({ isReOrder: false })
            setTimeout(() => {
              this._isReload = false
            }, 10)
            console.log('@home #onServiceSwitchEnter finally')
          })
      } else {
        this._render({
          _selectedServiceTypeIndex,
          serviceTypeName: _.get(
            serviceTypeList,
            `${_selectedServiceTypeIndex}.serviceTypeName`,
            serviceTypeName
          )
        })
        await this.onSubmitServiceType()
      }
    },
    onClickRedPacket() {
      this.onClickFreeCouponTag()
    },
    goBack() {
      this.call('navigateBack', { delta: 1 })
    },
    //点单小程序返回商家首页按钮
    goHome() {
      const { sqbBridge } = this.data
      const { storeId } = sqbBridge.getStore() || {}
      wx.reLaunch({
        url: getPageUrl('page-marchant-home', sqbBridge.isJJZ) + `?storeId=${storeId}`,
        query: {
          storeId
        }
      })
    },
    // 去搜索页面
    goSearch() {
      // @ts-ignore
      const { isRetail, isHaveMustGoods, mustCategoryId, isStoredPaySelected, activity } = this.data
      const { campus_activity: campusActivity } = this.getQuery()
      const groupBuyingActivity = _.head(_.get(activity, 'groupBuyingActivities'))
      this.call('navigateTo', {
        plugin: {
          name: 'scanPlugin',
          component: 'page-search'
        },
        query: {
          isStoredPaySelected,
          mustCategoryId: isHaveMustGoods ? mustCategoryId : '',
          isRetail,
          campusActivity,
          groupBuyingActivity: JSON.stringify(groupBuyingActivity)
        }
      })
    },
    // 去我的订单
    goMyOrder() {
      this.call('navigateTo', {
        plugin: {
          name: 'scanPlugin',
          component: 'page-my-order'
        }
      })
    },
    async afterCartChanged() {
      if (isRoundMeal(this)) this.cartStore.refreshRoundMealData()
      else this.initLocalCart()
    },
    afterGoodsListChanged: async function (pages, renders) {
      const { categories } = this.data
      // 更新分类或者商品列表已经加购物车数量
      this.update({ pages, categories }, async () => {
        let headerHeight = 0

        // 补偿.header高度变换，带来的页面配置的变化
        // if (!this.isHeaderHeightChanged) {
        //   headerHeight = await new Promise(resolve => {
        //     const query = isWeixin() ? this.createSelectorQuery() : wx.createSelectorQuery()
        //     query
        //       .select('.header')
        //       .boundingClientRect()
        //       .exec(([{ height }]) => {
        //         resolve(_.floor(height - HEADER_BAR_HEIGHT))
        //       })
        //   })
        //   console.info('头部高度信息', { headerHeight, HEADER_BAR_HEIGHT })
        // }

        renders &&
          this.homeStore
            .updatePages(pages, renders, headerHeight, this)
            .then((_pages: any) => {
              // this.isHeaderHeightChanged = true
              const simplePages = toSimplePages(_pages)
              // this.update({ simplePages });
              // 这个将导致页面非常卡顿
              // this.setData({ pages: _pages });
              this.update({ simplePages, pages: _pages })
            })
            .catch((err: any) => {
              console.error(err)
            })
      })
    },
    // setCategoryScrollTop: _.debounce(function (this: TComponentInstance<any, any>, data = {}) {
    //   const { activeCategoryId } = data;
    //   const categoryScrollTo = CategoryScroller.getScrollTo(activeCategoryId);

    //   if (categoryScrollTo !== prevCategoryScrollTo) {
    //     this._render({ categoryScrollTo });
    //   }
    //   prevCategoryScrollTo = categoryScrollTo;
    // }, 0),
    /**
     * 结算校验
     */
    async handleSettlement(data = {}) {
      const { mealType } = this.getTerminal() || {}
      const { categories: _categories, cart: _cart, sqbBridge } = this.data
      const {
        // @ts-ignore
        discount,
        // @ts-ignore
        cart = _cart,
        // @ts-ignore
        categories = _categories,
        // @ts-ignore
        serviceTypeName = sqbBridge.getServiceTypes(`serviceTypeName`),
        // @ts-ignore
        configs = await sqbBridge.getMcc(),
        // @ts-ignore
        serviceType = sqbBridge.getServiceTypes(`serviceType`)
      } = data

      const queryParams = sqbBridge.getMiniProgramQuery()
      const batchNoMustOrderGoods =
        _.get(queryParams, 'fromPaySubmitOrder') === 'batchNoMustOrderGoods'

      const pluginParams = {
        cart,
        configs,
        categories,
        serviceType,
        serviceTypeName,
        mealType,
        batchNoMustOrderGoods
      }

      callPlugin('handleSettlement', pluginParams).then(
        ({ isHaveMustGoods, isSelectedMustGoods, mustCategoryId }) => {
          this.update({
            isHaveMustGoods,
            isSelectedMustGoods,
            mustCategoryId
          })

          if (batchNoMustOrderGoods && !isSelectedMustGoods) {
            this.updateMustOrderGoodsStatus(mustCategoryId)
          }

          callPlugin('calcMinDeliveryPrice', { cart, discount, configs, serviceTypeName }).then(
            ({
              isHaveMinPrice,
              minDeliveryPrice,
              isLowerMinDeliveryPrice,
              minusPrice,
              actualPrice
            }) => {
              this.update({
                minusPrice,
                isHaveMinPrice,
                minDeliveryPrice,
                isLowerMinDeliveryPrice,
                actualPrice
              })
            }
          )
        }
      )
    },

    /**
     * @description: 围餐提交订单页未点必选品跳转到点单首页对必选分类高亮提示状态的处理
     * @param {string} mustCategoryId 必选商品的分类ID
     */
    updateMustOrderGoodsStatus(mustCategoryId) {
      const { initializedMustCaPosition } = this.data
      // 此判断防止加购其他非必选分类商品时重新触发定位
      if (!initializedMustCaPosition) {
        setTimeout(() => {
          // 滚动至相应分类
          this.onTapCategory(mustCategoryId)
          this._render({
            categoryScrollIntoView: 'menu-cid-' + mustCategoryId,
            initializedMustCaPosition: true
          })
        }, 500)
      }

      setTimeout(() => {
        this._render({
          isCartShow: false,
          isHighLight: true,
          showMustTip: true
        })
      }, 500)
    },

    /**
     * 更新营业时间&收银机状态
     */
    // HOOK - afterRender
    updateNoticeBarStatus: async function (serviceType) {
      // await this.checkBusinessException()
      this.checkBusinessException((err: any, result: any) => {
        if (err) {
          console.error(err)
          return
        }
        if (result) {
          this.setData({ ...result })
        }
      })
      const { noticeShowType } = this.data
      // 如果 noticeShowType 为 1是，则无法区分外卖堂食和外卖，无法触发observer
      if (serviceType === 1 && noticeShowType) {
        this._render({
          noticeShowType: 3
        })
      }
    },
    /**
     * 订单提醒
     */
    toggleOrderMessage(e) {
      const flg = e.detail || false
      this._render({ orderMessageShow: flg })
    },
    async isShowWxDialog(merchantSn) {
      return this.activitySvc.fetchIsWxAct(merchantSn).catch(() => false)
    },
    // 商品列表渲染完成
    onGoodsListRendered() {
      this._render({ isGoodsListRendered: true })
    },
    // 处理麦当劳头部icons
    updateHeaderIconsVersion: _.debounce(function (this: any, version) {
      const { config } = this.data // 支付宝不serviceType按钮显示有误差

      _.assign(config.btnServiceTypeTextStyle, config.btnServiceTypeTextStyleNew)
      if (isAlipay()) {
        _.assign(
          config.btnServiceTypeTextStyle,
          config.btnServiceTypeTextStyleMy,
          config.btnServiceTypeTextStyleMyNew
        )
      }
      // @ts-ignore
      this._render({ btnServiceTypeTextStyle: config.btnServiceTypeTextStyle })

      // 处理麦当劳sticky有空隙
      // if (config && config.themeName === 'mcDonald') {
      //   const top = _.parseInt(_.get(this, 'data.stickyStyle.top')) - 3;
      //   // @ts-ignore
      //   this._render({
      //     stickyStyle: {
      //       top: `${top}PX`,
      //     },
      //   });
      // }

      // 处理麦当劳头部icons
      // const version = headerIconVersion;
      if (version === 'v1') {
        // @ts-ignore
        this._render({ headerIconUrls: config['iconsHeaderV1'] })
      }
      if (version === 'v2') {
        // @ts-ignore
        this._render({ headerIconUrls: config['iconsHeaderV2'] })
      }
      // 更新特殊分类icon的url
      config &&
        _.map(SPECIAL_CATEGORIES, (item, cat) =>
          _.map(item, (__, key) => {
            if (key === 'icon') {
              item[key] = _.get(config, camelCase(`icon_${cat}_url`))
            }
            if (key === 'pic') {
              ;(item as any)[key] = _.get(config, camelCase(`tag_${cat}_url`))
              ;(item as any).style = _.get(config, camelCase(`${cat}_tag_style`))
            }
            return item
          })
        )

      // @ts-ignore
      this._render({ headerIconVersion: version, SPECIAL_CATEGORIES })

      if (_.get(this.data.config, 'themeName') === 'mcDonald') {
        if (version === 'v1') {
          // @ts-ignore
          this._render({
            headerIconUrls: this.data.config['iconsHeaderV1'],
            goBackColor: '#FFFFFF',
            headerIconStyle: { width: '24px', height: '24px' },
            headerMyOrderIconStyle: { width: '120rpx', height: '53rpx' }
          })
          setNavigation(config.bgNavigationColor || '#000000', config.backButtonColor)
        }

        if (version === 'v2') {
          // @ts-ignore
          this._render({
            headerIconUrls: this.data.config['iconsHeaderV2'],
            goBackColor: '#000000',
            headerIconStyle: { width: '19px', height: '19px' },
            headerMyOrderIconStyle: { width: '120rpx', height: '53rpx' }
          })
          setNavigation('#FFFFFF', '#000000')
        }
      }
    }, 0),
    onMkssCouponTagHidden() {
      this._render({ isMkssCouponTagShow: false })
    },
    onOptAddressList() {
      // const flag = e.detail
      const { showAddressSheet } = this.data
      // if (!_.isNil(flag)) return this._render({ showAddressSheet: flag })
      this._render({ showAddressSheet: !showAddressSheet })
    },
    /**
     * 商品详情弹窗 PageContainer 关闭事件
     */
    onPageContainerAfterLeave() {
      this._render({ goodDetailShow: false })
    },
    async onSwitchLoginSuccess(params = {}) {
      const { sqbBridge } = this.data

      // @ts-ignore
      const queryParams = storage('smart:home_query')
      const storeId = _.get(queryParams, 'storeId')
      ECHO('#GATHER - initByQrcodeOrStoreId from home - #onSwitchLoginSuccess')
      await sqbBridge.initByQrcodeOrStoreId({ ...queryParams, ...params, force: true })
      sqbBridge.getPayload(storeId).then(async (payload: TPayload) => {
        if (_.isEmpty(payload)) {
          ECHO('#GATHER - initByQrcodeOrStoreId from home - #onSwitchLoginSuccess - 2')
          const _payload: TPayload = await sqbBridge.initByQrcodeOrStoreId(queryParams)
          return this.mainProcess(_payload)
        }

        return this.mainProcess(payload)
      })
    },
    onSwitchLoginFail({ err }) {
      console.error('登录失败', err)
    },
    async onReLogin(params: { detail: { phoneInfo: object; loginInfo: object } } = {} as any) {
      const { detail } = params
      const context = this

      if (detail.loginInfo) {
        // @ts-ignore
        const prevUser = storage('currentUser')
        const { sqbBridge } = this.data

        storage('LOGIN', null)
        storage('currentUser', null)
        storage('token', null)

        sqbBridge
          .getUser(false, false, true)
          .then(async () => {
            // @ts-ignore
            const currentUser = storage('currentUser')

            ECHO('用户信息', { prevUser, currentUser })
            const body = {
              store_id: _.get(context, 'store.storeId'),
              service_type: _.get(context, 'data.serviceType'),
              former_user_id: _.get(prevUser, 'userId')
            }
            await this.cartSvc
              .migrateUserCart(body)
              .catch((err: any) => {
                ECHO('购物车迁移错误', { err })
                sqbBridge.sls('INFO', {
                  type: 'merchant_sync_cart_err',
                  data: { data: { prevUser, currentUser, err } }
                })
              })
              .then((resp: any) => {
                ECHO('购物车迁移结果', { resp })
                sqbBridge.sls('INFO', {
                  type: 'merchant_sync_cart_success',
                  data: { data: { prevUser, currentUser, resp } }
                })
              })

            // @ts-ignore
            const queryParams = storage('smart:home_query')
            const storeId = _.get(queryParams, 'storeId')
            await sqbBridge.initByQrcodeOrStoreId({ ...queryParams, force: true })
            sqbBridge.getPayload(storeId).then(async (payload: TPayload) => {
              if (_.isEmpty(payload)) {
                const _payload: TPayload = await sqbBridge.initByQrcodeOrStoreId(queryParams)
                return this.mainProcess(_payload)
              }

              return this.mainProcess(payload)
            })
            // 甚至返回icon
          })
          .catch((err: any) => {
            console.error('登录失败', err)
          })
      }
    },
    /**
     * 点单页浮窗对接AOP
     */
    loadFloatAOP() {
      //临时aop解决方案
      const timeout = 2000
      const timerId = setTimeout(() => {
        const { aopDialogShowed } = this.data
        if (aopDialogShowed) this._render({ activityDialogShowed: true })
        else this._render({ aopDialogShow: false })
        clearTimeout(timerId)
      }, timeout)

      const { sqbBridge } = this.data

      this.adsenseSvc
        .fetchAopContent({
          field_code: isWeixin() ? AOP_WX_HOME_FLOAT_CODE : AOP_MY_HOME_FLOAT_CODE,
          field_style: 'wechat_orderpage_popup'
        })
        .then((data: any) => {
          const { records } = data
          if (records && records.length) {
            const bannerData = records[0]
            const floatingBubbleData = {
              pic: '/emenu/campus_group_float.gif',
              ...bannerData,
              ...(bannerData.extra || {})
            }
            this._render({ floatingBubbleData })
            sqbBridge.track('SmMpPageDisplay', {
              sm_uiview_name: '点单页浮窗'
            })
          }
        })
    },
    onFloatingBubbleClose(props) {
      storage('floatingBubbleVisible', _.get(props, 'detail'))
    },
    onCustomerToMember() {
      this.onSwitchLoginSuccess({ serviceType: this.data.serviceType })
    },
    /**
     * 商品抵用券浮窗数量回调
     * @param count 商品抵用券数量
     */
    onCouponGoodsCount(e: { detail: { count: number } }) {
      this.goodsCouponCount = _.get(e.detail, 'count')
    },
    /**
     * 商品券选择弹窗显示与关闭
     * @param visible 是否显示
     */
    onVisibleChange(e: { detail: { visible: boolean } }) {
      const visible = _.get(e.detail, 'visible', false)

      const cart = this.getCartData()

      if (visible) emenuChannel.emit('onCartChange', cart)
      this._render({ goodsCouponDialogVisible: visible })
    },
    /**
     * 商品券选择弹窗选中回调
     */
    async onGoodsCouponDialogChecked(e: {
      detail: { list: { skuId: string; spuId: string; couponId: string }[] }
    }) {
      const couponList = _.get(e.detail, 'list')
      const { serviceType, serviceTypeName, payway, cart, sqbBridge } = this.data
      const { avatarUrl, nickName } = await sqbBridge.getMiniProgramUser()
      const specificItems = _.map(couponList, coupon => ({
        spu_id: coupon.spuId,
        sku_id: coupon.skuId
      }))

      this.onVisibleChange({ detail: { visible: false } })

      if (!_.isEmpty(specificItems)) {
        const mkCustomInfo = emenuChannel.getMkCustomInfo()
        this.cartSvc
          .addAndRedeemBySpuIds({
            payway,
            sub_payway: SUB_PAY_WAY,
            service_type: serviceType,
            discount_strategy: serviceTypeName,
            table_id: _.get(this.getTerminal(), 'tableId'),
            meal_type: isRoundMeal(this) ? 'ROUND_MEAL' : 'SINGLE',
            user_icon: avatarUrl,
            user_name: nickName,
            specific_items: specificItems,
            mk_custom_info: mkCustomInfo
          })
          .then((res: any) => {
            const cart = _.get(res, 'cart')
            const discount = _.get(res, 'redeem_result')

            updateMkCustomInfoByDiscount(discount)
            this.bus.emit(CART_CHANGE_EVENT, cart, discount)
            this.$toast(this.t('购物车商品有变动，请确认'))
          })
          .catch(() => {
            this.$toast(this.t('商品暂不售卖'))
            emenuChannel.setMkCustomInfo({
              specific_goods_card_list: _.reject(
                _.get(mkCustomInfo, 'specific_goods_card_list', []),
                item =>
                  _.includes(
                    _.map(couponList, coupon => coupon.couponId),
                    item
                  )
              )
            })
            this.bus.emit(CART_CHANGE_EVENT, cart)
          })
      } else {
        this.bus.emit(CART_CHANGE_EVENT, cart)
      }
    },
    /**
     * 商品券选择弹窗显示与关闭回调
     * @param visible 是否显示
     */
    onGoodsCouponDialogVisibleChange(e: { detail: { visible: boolean } }) {
      this._render({ goodsCouponDialogVisible: _.get(e.detail, 'visible') })
    },
    onClickHeaderIcon(e) {
      const type = e.currentTarget.dataset.type
      if (type === 'collect') {
        this.toggleCollect()
      }
      if (type === 'search') {
        this.goSearch()
      }
      if (type === 'order') {
        this.goMyOrder()
      }
      if (type === 'coupon') {
        this.onClickRedPacket()
      }
    },
    // 会员积分数量获取
    onMemberPoints(e: { detail: { memberPoints: number } }) {
      this.memberPoints = _.get(e.detail, 'memberPoints')
    },
    // 支持可配送区域关闭
    onAddressSheetClose() {
      this._render({ showAddressSheet: false })
    },
    clearCachedData() {
      const { serviceTypeName, sqbBridge } = this.data
      const { storeId } = sqbBridge.getStore() || {}
      storeId && storage(`emenu:${storeId}_${serviceTypeName}`, null)
    },
    handleClearCartDialog() {
      this.$alert({
        // title: '错误信息',
        message: this.t('确定清空全部商品?'),
        title: this.t('提示'),
        confirmButtonText: this.t('确定'),
        cancelButtonText: this.t('取消'),
        showCancelButton: true
      })
        .then(() => {
          this.onClearCart()
        })
        .catch(() => ({}))
    },
    onSaleDialogClose() {
      this._render({ isSaleDialogShow: false })
    },
    //临时aop解决方案
    onAopRequestShow(e) {
      const result = _.get(e, 'detail.result')
      if (result) this._render({ aopDialogShowed: true })
      else this._render({ aopDialogShow: false })
    },
    onAopClose() {
      this._render({ aopDialogShow: false, activityDialogShowed: true })
    }
  }
})
