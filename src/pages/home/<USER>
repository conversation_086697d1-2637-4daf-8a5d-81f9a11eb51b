<wxs src="@wxs/utils.wxs" module="utils" />
<wxs src="@wxs/toCSSVars.wxs" module="computed" />
<wxs src="./index.wxs" module="home" />
<wxs src="./header-icons.wxs" module="headerIcons" />

<view class="smart-page {{mpPlatform || ''}}" style="{{computed.toCSSVars(theme.style)}}">
  <smart-main-provider id="main-provider" full-height sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}" dialog-class="dialog-class" custom-class="relative smart-main-provider">
    <smart-i18n-provider id="smart-i18n-provider" sqbBridge="{{sqbBridge}}" mpBridge="{{mpBridge}}">
      <view class=" {{utils.classNames('page-home theme-style', {gridMode: goodsDisplayMode === 1, listMode: goodsDisplayMode !== 1})}}">
        <scroll-view
          data-simplepages="{{simplePages}}"
          data-viewport-height="{{systemInfo.windowHeight}}"
          data-status-bar-height="{{status_bar_height}}"
          data-display-mode="{{goodsDisplayMode}}"
          data-is-retail="{{isRetail}}"
          bindscroll="{{home.onScrollHomePage}}"
          lower-threshold="{{100}}"
          bindscrolltolower="{{home.onScrollToLower}}"
          onScrollToLower="{{home.onScrollToLower}}"
          scroll-y
          enable-flex
          enable-passive
          scroll-anchoring
          enhanced
          bounces="{{false}}"
          scroll-into-view="{{currentCategoryIdAnchor}}"
          scroll-top="{{isRetail?'':goodsListScrollTop}}"
          class="h-full relative container scroll-content-wrap"
          style="pointer-events: {{isGoodsListRendered ? 'auto': 'none'}};"
        >
          <!--TODO: -->
          <view id="top-view" />
          <view class="content" style="{{loading ? 'opacity: 0; pointer-events: none; will-change: opacity;' : 'opacity: 1; pointer-events: auto; will-change: opacity;'}}">
            <!-- 头部 -->
            <view class="header-icons-container sticky top-0 w-full z-99">
              <include src="./template/header-icons.wxml" />
            </view>
            <!-- 商家信息 -->
            <smart-store-info
              catch:serviceTypeClick="onTapChangeServiceType"
              card-custom-class="mx-30 store-info-card-custom-class"
              title-class="store-info-title-class"
              button-class="store-info-button-class"
              icon-class="store-info-icon-class"
              custom-class="store-info-class pb-16"
              mpBridge="{{mpBridge}}"
              sqbBridge="{{sqbBridge}}"
              serviceName="{{utils.i18n.t(serviceTypeList[currentServiceTypeIndex].name, i18nData)}}"
              store="{{store}}"
              bulletin="{{bulletin}}"
              bulletinExpand="{{bulletinExpand}}"
              activity="{{activity}}"
              discountTag="{{discountTag}}"
              catch:bulletinExpand="onBulletinExpand"
              i18nData="{{i18nData}}"
            >
              <view wx:if="{{activity}}" class="{{isMkssCouponTagShow ? 'mt-14' : ''}}" style="min-height:{{ isMkssCouponTagShow ? '21PX' : 0}}">
                <mkss-coupon-tag
                  config="{{config}}"
                  theme="{{mkssTheme}}"
                  activities="{{activity}}"
                  themeName="{{config.themeName}}"
                  storeId="{{store.id || store.storeId}}"
                  mpBridge="{{mpBridge}}"
                  sqbBridge="{{sqbBridge}}"
                  catch:hidden="onMkssCouponTagHidden"
                />
              </view>
              <view class="tag-info-wrap lh-1" wx:if="{{discountTag.length}}">
                <smart-ui-tag type="primary" wx:for="{{discountTag}}" wx:key="*this" theme="{{theme}}" border custom-class="px-10 mt-8 mx-4 leading-150 discount-tag-class">
                  <text>{{item}}</text>
                </smart-ui-tag>
              </view>
            </smart-store-info>
            <!-- 营销组件 - 登录 -->
            <mkss-store-member-info-card
              custom-class="mkss-store-member-info-card mx-30 mt-4 mb-10"
              wx:if="{{store.id || store.storeId}}"
              config="{{config}}"
              theme="{{mkssTheme}}"
              mpBridge="{{mpBridge}}"
              sqbBridge="{{sqbBridge}}"
              storeId="{{store.id || store.storeId}}"
              catch:onCustomerToMember="onCustomerToMember"
              catch:onMemberPoints="onMemberPoints"
              generic:auth-phone="auth-phone"
              generic:auth-login="auth-login"
            />
            <!-- 支持配送区域 -->
            <view class="address-container" wx:if="{{areas.enabled && serviceTypeName === TAKE_OUT_ORDER}}">
              <smart-card>
                <smart-address-block custom-tips-class="custom-tips-class" custom-class="address-block" catch:click="onOptAddressList" areas="{{areas}}" i18nData="{{i18nData}}" />
              </smart-card>
            </view>
            <!-- 内容 -->
            <view class="tabs-container mt-8">
              <smart-ui-tabs
                active="{{ 0 }}"
                custom-class="tabs"
                tab-class="flex-none"
                tab-active-class="tab-active-class"
                tab-underline-class="tab-underline-class"
                tabs-content-class="tabs-content-class"
                tabs-class="tabs-nav-class grid grid-flow-col flex-left"
                nav-class="mx-30"
                selectedUnderLine
              >
                <view slot="nav-right">
                  <!-- TODO: 桌台信息: 门店点单码&桌台点单码 时显示 -->
                  <view class="table-info flex-center-y" wx:if="{{serviceType === 2 && (jjzBusinessType === 1 || jjzBusinessType === 2)}}">
                    <image mode="aspectFit" class="table-info__icon" src="https://smart-static.wosaimg.com/emenu/bell_black.png" />
                    <view class="table-info__name">{{utils.i18n.t('桌号', i18nData)}}：{{jjzBusinessType === 1 ? tableName : utils.i18n.t("门店点单码", i18nData)}}</view>
                  </view>
                </view>
                <!-- 广告 -->
                <smart-ui-tab title="{{utils.i18n.t('点单', i18nData)}}" custom-class="tab tab--order">
                  <view class="tab-content">
                    <!-- TODO: 广告位 -->
                    <view class="adsense-box">
                      <smart-adsense-list custom-class="adsense-list-wrap" sqbBridge="{{sqbBridge}}" lists="{{adsenseLists}}" trackParams="{{aopTrackParams}}" />
                      <smart-ads
                        wx:if="{{showAdFlag && !(adsenseLists && adsenseLists.length)}}"
                        sqbBridge="{{sqbBridge}}"
                        generic:ads-banner="ads-banner"
                        query="{{adsQuery}}"
                        store="{{store}}"
                        catch:success="onAdsBannerSuccess"
                        custom-class="adsense-list-wrap"
                        customStyle="overflow: hidden;{{showAdsBanner ? 'height: 220rpx; border-radius: 16rpx;' : 'height: 0rpx; margin: 0rpx'}}"
                      />
                    </view>
                    <!--推荐商品-->
                    <view class="recommend-container">
                      <smart-card
                        wx:if="{{recommendShowWay === 'horizontal' && recommends.length > 0}}"
                        custom-class="mt-20 mx-30 w-ful-100 overflow-hidden card-class recommend-card-class"
                        style="display: inline-flex"
                      >
                        <smart-recommend-horizontal
                          recommend-title-class="recommend-title-class"
                          custom-class="recommend-class"
                          card-class="recommend-card-class"
                          goods-item-class="goods-item-class"
                          isUseLocalCart="{{isUseLocalCart}}"
                          visible="{{recommendShowWay === 'horizontal' && recommends.length > 0}}"
                          source="{{recommends}}"
                          generic:auth-user="auth-user"
                          sqbBridge="{{sqbBridge}}"
                          weixinAuthData="{{weixinAuthData}}"
                          i18nData="{{i18nData}}"
                          disabled="{{!canPlaceOrder}}"
                          catch:goodsClick="onGoodsClick"
                          catch:plus="onAddToCart"
                          catch:click="onTapGoodItem"
                          catch:choose="onTapGoodChoose"
                          catch:authOnce="onAuthOnceUser"
                          catch:showToast="handleToastMessage"
                          key="{{store.id || store.storeId}}"
                        />
                      </smart-card>
                    </view>
                    <!-- 推荐商品结束 -->
                    <!-- TODO: 返回顶部 -->
                    <view catchtap="onBackToTop" style="{{utils.toStyle(backToTopStyle)}}" class="flex flex-center-x back-top-box back-top">
                      <image src="https://smart-static.wosaimg.com/emenu/arrow_line.png" class="back-top-img" />
                    </view>
                    <!-- TODO: 商品分类及列表骨架屏 - 开始-->
                    <!--
                    <view class="{{utils.classNames('skeleton-container w-full h-full')}}">
                      <view class="skeleton-box flex flex-row h-full">
                        <view class="left flex flex-column" />
                        <view class="right flex-item" />
                      </view>
                    </view>
                    -->
                    <!-- 零售商品 -->
                    <block wx:if="{{isRetail}}">
                      <include src="./template/retail.wxml" />
                    </block>
                    <!-- 网格模式 -->
                    <block wx:elif="{{goodsDisplayMode === 1}}">
                      <include src="./template/grid.wxml" />
                    </block>
                    <!--列表模式-->
                    <block wx:else>
                      <include src="./template/list.wxml" />
                    </block>
                    <view>
                      <smart-submit-bar
                        serviceType="{{serviceType}}"
                        action-sheet-custom-class="submit-bar-action-sheet"
                        custom-class="submit-bar"
                        goods-item-class="goods-item-class"
                        style="--popup-background-color: transparent"
                        id="submit-bar"
                        isUseLocalCart="{{isUseLocalCart}}"
                        mustOrderTip="{{mustOrderTip}}"
                        wx:if="{{!loading}}"
                        sqbBridge="{{sqbBridge}}"
                        mpBridge="{{mpBridge}}"
                        catch:authOnce="onAuthOnceUser"
                        generic:auth-user="auth-user"
                        generic:auth-login="auth-login"
                        generic:auth-phone="auth-phone"
                        weixinAuthData="{{weixinAuthData}}"
                        show="{{isCartShow}}"
                        store="{{store}}"
                        theme="{{mkssTheme}}"
                        discount="{{discount}}"
                        serviceTypeName="{{serviceTypeName}}"
                        isStoredPaySelected="{{isStoredPaySelected}}"
                        catch:upCartGoodsNum="onAddToCart"
                        catch:downCartGoodsNum="onRemoveFromCart"
                        catch:submit="onPlaceOrder"
                        catch:focus="bindCartFocusClick"
                        catch:goodsClick="onGoodsClick"
                        catch:onCouponGoodsCount="onCouponGoodsCount"
                        catch:onVisibleChange="onVisibleChange"
                        catch:storedRuleSelectedChange="onStoredRuleSelectedChange"
                        generic:mkss-stored-activity-in-cart="mkss-stored-activity-in-cart"
                        generic:mkss-coupon-goods-available="mkss-coupon-goods-available"
                        i18nData="{{i18nData}}"
                        catch:showToast="handleToastMessage"
                        catch:showClearCartDialog="handleClearCartDialog"
                      >
                        <view wx:if="{{isSelectedMustGoods}}" style="width: 100%; height: 100%" class="flex-center">
                          <view wx:if="{{isLowerMinDeliveryPrice}}">
                            {{!!actualPrice ? utils.i18n.t("还差{}元起送", i18nData, [utils.toYuan(minusPrice)]) : utils.i18n.t("{}元起送", i18nData, [utils.toYuan(minDeliveryPrice)])}}
                          </view>
                          <view wx:else style="width: 100%; height: 100%; position: relative" catchtap="onTapAuthUser">
                            <auth-login
                              wx:if="{{(weixinAuthData.shouldAuthPhone || aliShouldAuthPhone) && !sqbBridge.is_MY_CAMPUS && IS_ENABLE_AUTH_PHONE}}"
                              class="abs-full unvisible auth-phone"
                              extClass="abs-full unvisible auth-phone"
                              bindsuccess="onAuthPhoneCallback"
                              binderror="onAuthPhoneCallbackError"
                              binddeny="onAuthPhoneCallbackDeny"
                              needAuthPrivacyList="{{needAuthPrivacyList}}"
                              target="{{authLoginTarget}}"
                              mpBridge="{{sqbBridge}}"
                            />
                            <view catchtap="onPlaceOrder" style="width: 100%; height: 100%" class="flex-center">{{utils.i18n.t(submitText, i18nData)}}</view>
                            <button
                              wx:if="{{aliShouldAuthPhone && sqbBridge.is_MY_CAMPUS && IS_ENABLE_AUTH_PHONE}}"
                              open-type="getAuthorize"
                              scope="phoneNumber"
                              onGetAuthorize="authPhoneClick"
                              onError="authPhoneReject"
                              class="auth-phone"
                            />
                          </view>
                        </view>
                        <view wx:else>{{utils.i18n.t('未点必选品', i18nData)}}</view>
                      </smart-submit-bar>
                    </view>
                  </view>
                </smart-ui-tab>
                <smart-ui-tab title="{{utils.i18n.t('商家', i18nData)}}" custom-class="tab tab--merchant">
                  <smart-merchant-detail
                    wx:if="{{!loading}}"
                    merchant="{{store}}"
                    show="{{!loading}}"
                    serviceType="{{serviceType}}"
                    serviceTypeName="{{serviceTypeName}}"
                    serviceTypeList="{{serviceTypeList}}"
                    sqbBridge="{{sqbBridge}}"
                    style="flex: 1"
                    i18nData="{{i18nData}}"
                  />
                </smart-ui-tab>
              </smart-ui-tabs>
            </view>
          </view>
        </scroll-view>
        <!-- 售卖时间弹窗 -->
        <smart-ui-dialog
          custom-class="dialog-class"
          wx:if="{{ isSaleDialogShow }}"
          title="{{utils.i18n.t(salesTimeInfo.title, i18nData)}}"
          show="{{ isSaleDialogShow }}"
          key="time-dialog"
          use-slot
          confirm-button-text="{{utils.i18n.t('我知道了', i18nData)}}"
          control-mode="external"
          catch:close="onSaleDialogClose"
        >
          <view class="flex-column flex-center text-black text-30 font-regular">
            <view>{{salesTimeInfo.date}}</view>
            <view class="pt-30">{{salesTimeInfo.cycle ? salesTimeInfo.cycle+': ' : ''}}{{salesTimeInfo.times}}</view>
          </view>
        </smart-ui-dialog>
        <!-- 服务类型弹窗 -->
        <view class="service-type-dialog-container">
          <smart-ui-dialog
            custom-class="service-type-dialog-class"
            key="service-type-dialog"
            bind:confirm="onServiceSwitchEnter"
            title="{{utils.i18n.t('请选择服务方式', i18nData)}}"
            show="{{ selectServiceTypeShow }}"
            use-slot
            confirm-button-text="{{utils.i18n.t('确认', i18nData)}}"
          >
            <view class="grid service-types">
              <view
                wx:for="{{serviceTypeList}}"
                wx:key="serviceTypeName"
                catchtap="onServiceSwitch"
                data-type="{{item.serviceTypeName}}"
                class="{{utils.classNames('service-types-item flex-row flex-center-y', { active: item.active })}}"
              >
                <view class="service-types-item-icon {{item.serviceTypeName}}" />
                <view class="flex-item pl-30">{{utils.i18n.t(item.alias, i18nData) || utils.i18n.t(item.name, i18nData)}}</view>
                <smart-ui-icon wx:if="{{item.active}}" name="tick" size="22PX" />
              </view>
            </view>
          </smart-ui-dialog>
        </view>
        <smart-dialog-user-counter
          tableName="{{tableName}}"
          catch:submit="selectUserCountCallback"
          wx:if="{{showSelectUserCount}}"
          show="{{showSelectUserCount}}"
          i18nData="{{i18nData}}"
          lang="{{lang}}"
          catch:showToast="handleToastMessage"
        />
        <smart-page-container show="{{goodDetailShow}}" z-index="{{11000}}" overlayStyle="background-color: transparent;" catch:afterleave="onPageContainerAfterLeave">
          <smart-goods-detail
            cart="{{cartDataForGoodsDetail}}"
            store="{{store}}"
            isRetail="{{isRetail}}"
            hotSaleSeqTagType="{{theme.config['sales-tag-class']}}"
            config="{{config}}"
            theme="{{mkssTheme}}"
            custom-class="goods-detail"
            image-custom-class="goods-detail-image"
            show="{{goodDetailShow}}"
            info="{{currentGood}}"
            disabled="{{!canPlaceOrder}}"
            ranking="{{ranking}}"
            catch:close="onCloseGoodDetailSheet"
            catch:addToCart="onAddToCart"
            catch:authOnce="onAuthOnceUser"
            generic:auth-user="auth-user"
            generic:auth-phone="auth-phone"
            generic:auth-login="auth-login"
            generic:mkss-stored-activity-in-material="mkss-stored-activity-in-material"
            sqbBridge="{{sqbBridge}}"
            mpBridge="{{mpBridge}}"
            weixinAuthData="{{weixinAuthData}}"
            i18nData="{{i18nData}}"
            catch:showToast="handleToastMessage"
            displayConfig="{{imageDisplayConfig}}"
          />
        </smart-page-container>
        <smart-ui-action-sheet
          close-on-click-overlay
          custom-class="address-sheet-class"
          class="address-sheet-class"
          close-class="action-sheet-close-class"
          show="{{showAddressSheet}}"
          catch:close="onAddressSheetClose"
          title="{{utils.i18n.t('支持配送地址', i18nData)}}"
        >
          <smart-ui-icon slot="header-right-slot" name="close" size="22" class="address-sheet-close-icon" catch:click="onAddressSheetClose" />
          <view class="address-item-container">
            <view class="address-item" wx:for="{{areas.areas}}" wx:key="id">
              <smart-ui-cell title="{{item.name}}" value="{{item.distance}}" custom-class="pt-30 pb-30" title-class="text-30 text-black mr-20" value-class="address-item-value" center />
              <smart-ui-divider />
            </view>
            <view class="color-999 text-24 mt-32 flex-row flex-center-x">{{utils.i18n.t('没有更多了～', i18nData)}}</view>
          </view>
        </smart-ui-action-sheet>
        <smart-notice-bar i18nData="{{i18nData}}" sqbBridge="{{sqbBridge}}" noticeShowType="{{noticeShowType}}" wx:if="{{noticeShowType}}" notice-bar-class="dialog-class" />
        <!-- TODO: 预览组件 -->
        <smart-preview mpBridge="{{mpBridge}}" catch:exitPreview="clearCachedData" />
        <!-- 营销组件 start -->
        <block>
          <!-- aop弹窗 -->
          <view style="display:{{(selectServiceTypeShow || !aopDialogShow) ? 'none':'block'}}">
            <smart-popover
              payload="{{store}}"
              serviceType="{{serviceType}}"
              requestType="order-popup"
              sqbBridge="{{sqbBridge}}"
              bind:requestShow="onAopRequestShow"
              catch:close="onAopClose"
            ></smart-popover>
          </view>
          <!-- 活动弹窗 -->
          <view style="display:{{aopDialogShow ? 'none':'block'}}">
            <mkss-activity-dialog
              wx:if="{{ENABLE_MKSS_DIALOG}}"
              visible="{{!selectServiceTypeShow && activityDialogShow && !activityDialogShowed }}"
              config="{{config}}"
              theme="{{mkssTheme}}"
              sqbBridge="{{sqbBridge}}"
              mpBridge="{{mpBridge}}"
              generic:auth-phone="auth-phone"
              generic:auth-login="auth-login"
              generic:send-coupon="send-coupon"
              storeId="{{store.id || store.storeId}}"
              devCode="{{syncWechatDevCode}}"
              scene="{{serviceTypeName}}"
              params="{{mkssActivityDialogParams}}"
              bindonClose="onCloseMkssActivityDialog"
            />
          </view>
          <!-- 商品抵用券弹窗 -->
          <!-- TODO: 内存泄露 -->
          <mkss-coupon-goods-dialog
            config="{{config}}"
            theme="{{mkssTheme}}"
            mpBridge="{{mpBridge}}"
            sqbBridge="{{sqbBridge}}"
            themeName="{{config.themeName}}"
            visible="{{goodsCouponDialogVisible}}"
            storeId="{{store.storeId}}"
            merchantId="{{store.merchantId}}"
            emenuChannel="{{emenuChannel}}"
            serviceTypeName="{{serviceTypeName}}"
            catch:onCouponChecked="onGoodsCouponDialogChecked"
            catch:onVisibleChange="onGoodsCouponDialogVisibleChange"
          />

          <!-- 加购数量弹窗 -->
          <smart-dialog-counter
            i18nData="{{i18nData}}"
            sqbBridge="{{sqbBridge}}"
            mbBridge="{{mbBridge}}"
            info="{{currentEnterGoods}}"
            catch:confirm="confirmCountChange"
            catch:cancel="cancelCountChange"
            wx:if="{{enterCountShow}}"
            catch:showToast="handleToastMessage"
          />
        </block>
        <!-- 营销组件 end -->
      </view>
    </smart-i18n-provider>
  </smart-main-provider>
</view>
