<wxs src="@wxs/utils.wxs" module="utils" />

<smart-ui-action-sheet
  show="{{show}}"
  zIndex="{{1000}}"
  title="{{TITLE_DICT[type]&&utils.i18n.t(TITLE_DICT[type].title,i18nData)}}"
  leftTitle="{{utils.i18n.t('取消',i18nData)}}"
  left-title-class="text-26"
  title-class="text-32 color-black fw-500 px-40"
  round
  catch:click-overlay="close"
  custom-class="flex-column"
  class="time-sheet-wrap time-sheet-class {{timeSheetClass}}"
  catch:click-left-title="close"
>
  <view class="flex-row flex-item overflow-hidden time-sheet-wrap">
    <scroll-view scroll-y="{{true}}" class="text-26 color-black day-wrap">
      <view catchtap="clickDay" data-index="{{index}}" wx:for="{{days}}" wx:key="index" class="flex-center day-item {{utils.classNames({dayItemActive: _dayIndex === index})}}">{{item}}</view>
    </scroll-view>
    <scroll-view scroll-y="{{true}}" class="flex-item time-list-wrap">
      <smart-ui-cell
        wx:for="{{times[_dayIndex]}}"
        wx:key="index"
        catch:click="clickTime"
        data-index="{{index}}"
        title="{{item}}"
        title-class="text-26 color-black {{index === _timeIndex ? 'fw-500' : ''}}"
        custom-class="px-30"
        center
        border
      >
        <block>
          <smart-ui-icon slot="right-icon" wx:if="{{index === _timeIndex}}" name="tick" custom-class="selected-tag" size="22PX" />
        </block>
      </smart-ui-cell>
    </scroll-view>
  </view>
</smart-ui-action-sheet>
