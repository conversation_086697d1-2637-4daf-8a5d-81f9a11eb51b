@import (reference) '@styles/const';
@import (css) '@styles/common';
@import (css) '@styles/flex';

.button-group-wrap {
  --smart-round-meal-bottom-button-left-border: var(--primary__color, #ff6a14);
  --smart-round-meal-bottom-button-left-color: var(--primary__color, #ff6a14);
  --smart-round-meal-bottom-button-right-bg: var(
    --primary-gradient__color,
    linear-gradient(270deg, #ffa331 0%, #ff6a16 100%)
  );
  --smart-round-meal-bottom-button-right-color: var(--primary-foreground__color, white);
  position: fixed;
  bottom: 0px;
  left: 0px;
  right: 0px;
  padding: 32px 30px calc(30px + var(--safe-bottom));
  box-shadow: 0px 3px 20px 0px rgba(0, 0, 0, 0.1);
  border-radius: 32px 32px 0px 0px;
  z-index: 999;

  .smart-price-class {
    &__amount {
      --display-price-font-size: 40px;
      --display-currency-font-size: 26px;
    }
  }

  .need-pay-amount-cell {
    border-bottom: 1px solid #efefef;

    &-title {
      flex-shrink: 1;
    }

    &-value {
      // font-size: 42px;
    }
  }

  .btn {
    flex: 1;
    height: 90px;
    border-radius: 90px;
    box-sizing: border-box;

    font-weight: 500;
    font-size: 34px;

    border: 2px solid var(--smart-round-meal-bottom-button-left-border, #ff6a16);
    color: var(--smart-round-meal-bottom-button-left-color, #ff6a16);

    &.button-place-order {
      border: none;
      background: var(
        --smart-round-meal-bottom-button-right-bg,
        linear-gradient(90deg, #ff6c17 0%, #ff9434 100%)
      );
      color: var(--smart-round-meal-bottom-button-right-color, white);
    }

    &:not(:last-child) {
      margin-right: 30px;
    }
  }
}
