# 业务组件库指南

## 组件库概述
业务组件库位于`src/components_v2`目录下，是基于基础UI组件库构建的高级业务组件集合。这些组件封装了特定业务场景的交互逻辑和UI展示，支持微信和支付宝小程序平台，通过统一的组件接口简化业务开发流程。业务组件库遵循与基础UI组件相同的设计理念，但更加专注于具体业务场景的实现。

## 组件引入方式示例

```json
"usingComponents": {
  "smart-goods": "@components_v2/goods/index",
  "smart-store-basic": "@components_v2/store-basic/index"
}
```

## 主要组件

### 商品相关组件
- [@goods](smart-mp-biz/src/components_v2/goods/README.md)：商品列表组件，支持多种商品展示形式和布局
- [@goods-detail](smart-mp-biz/src/components_v2/goods-detail/README.md)：商品详情组件，展示商品完整信息
- [@goods-skeleton](smart-mp-biz/src/components_v2/goods-skeleton/README.md)：商品骨架屏组件，优化加载体验
- [@price](smart-mp-biz/src/components_v2/price/README.md)：价格组件，统一价格展示格式
- [@price-activity](smart-mp-biz/src/components_v2/price-activity/README.md)：活动价格组件，支持折扣、满减等价格类型
- [@invalid-goods-dialog](smart-mp-biz/src/components_v2/invalid-goods-dialog/README.md)：无效商品弹窗，提示商品失效或下架

### 店铺相关组件
- [@store-basic](smart-mp-biz/src/components_v2/store-basic/README.md)：店铺基础信息组件，展示店铺基本信息
- [@store-cover](smart-mp-biz/src/components_v2/store-cover/README.md)：店铺封面组件，展示店铺主图
- [@store-collection](smart-mp-biz/src/components_v2/store-collection/README.md)：店铺收藏组件，提供收藏功能
- [@store-info](smart-mp-biz/src/components_v2/store-info/README.md)：店铺详情组件，展示店铺详细信息
- [@store-item](smart-mp-biz/src/components_v2/store-item/README.md)：店铺列表项组件，用于店铺列表展示
- [@store-service-types](smart-mp-biz/src/components_v2/store-service-types/README.md)：店铺服务类型组件，展示配送、自提等服务类型
- [@banner-nav](smart-mp-biz/src/components_v2/banner-nav/README.md)：横幅导航组件，提供广告位与导航功能
- [@brand-expose](smart-mp-biz/src/components_v2/brand-expose/README.md)：品牌曝光组件，用于品牌推广

### 订单相关组件
- [@order-map](smart-mp-biz/src/components_v2/order-map/README.md)：订单地图组件，展示订单配送路线与位置
- [@payment-summary](smart-mp-biz/src/components_v2/payment-summary/README.md)：支付摘要组件，展示支付金额明细
- [@cashier](smart-mp-biz/src/components_v2/cashier/README.md)：收银台组件，提供支付方式选择
- [@submit-bar](smart-mp-biz/src/components_v2/submit-bar/README.md)：购物车组件，固定在页面底部的操作栏
- [@refund-panel](smart-mp-biz/src/components_v2/refund-panel/README.md)：退款面板组件，展示退款相关信息

### 团购与拼团组件
- [@group-meal-batch-list](smart-mp-biz/src/components_v2/group-meal-batch-list/README.md)：团餐批次列表，展示团餐批次信息
- [@group-meal-btn-group](smart-mp-biz/src/components_v2/group-meal-btn-group/README.md)：团餐按钮组，提供团餐相关操作
- [@group-meal-edit-people](smart-mp-biz/src/components_v2/group-meal-edit-people/README.md)：团餐人数编辑，调整参与团餐人数

### 营销组件
- [@ads](smart-mp-biz/src/components_v2/ads/README.md)：广告组件，展示营销广告
- [@adsense](smart-mp-biz/src/components_v2/adsense/README.md)：广告感知组件，智能推荐广告
- [@adsense-list](smart-mp-biz/src/components_v2/adsense-list/README.md)：广告感知列表，批量展示推荐广告
- [@hbfq](smart-mp-biz/src/components_v2/hbfq/README.md)：花呗分期组件，提供分期付款功能
- [@discount-list](smart-mp-biz/src/components_v2/discount-list/README.md)：折扣列表，展示可用优惠
- [@discount-tag](smart-mp-biz/src/components_v2/discount-tag/README.md)：折扣标签，标识折扣信息
- [@recommend-material](smart-mp-biz/src/components_v2/recommend-material/README.md)：推荐物料，展示推荐商品

### 交互组件
- [@dialog-counter](smart-mp-biz/src/components_v2/dialog-counter/README.md)：计数器弹窗，数量选择弹窗
- [@dialog-material](smart-mp-biz/src/components_v2/dialog-material/README.md)：物料选择弹窗，选择商品规格等
- [@dialog-user-counter](smart-mp-biz/src/components_v2/dialog-user-counter/README.md)：用户计数弹窗，选择用户数量
- [@counter](smart-mp-biz/src/components_v2/counter/README.md)：计数器，通用数量选择器，用于加减购商品
- [@diner-counter](smart-mp-biz/src/components_v2/diner-counter/README.md)：用餐人数计数器，专用于选择用餐人数
- [@keyboard](smart-mp-biz/src/components_v2/keyboard/README.md)：自定义键盘，提供数字输入界面
- [@call](smart-mp-biz/src/components_v2/call/README.md)：拨打电话组件，提供电话联系功能

### 展示组件
- [@image-swiper](smart-mp-biz/src/components_v2/image-swiper/README.md)：图片轮播，多图片滑动展示
- [@preview](smart-mp-biz/src/components_v2/preview/README.md)：图片预览，大图查看功能
- [@badge](smart-mp-biz/src/components_v2/badge/README.md)：徽标，展示数字或状态标记
- [@card](smart-mp-biz/src/components_v2/card/README.md)：卡片，通用信息展示容器
- [@alert](smart-mp-biz/src/components_v2/alert/README.md)：提示，警告或提示信息展示
- [@notice-bar](smart-mp-biz/src/components_v2/notice-bar/README.md)：通知栏，滚动展示通知信息
- [@bulletin](smart-mp-biz/src/components_v2/bulletin/README.md)：公告栏，展示公告信息
- [@tag-sales-rank](smart-mp-biz/src/components_v2/tag-sales-rank/README.md)：销量排名标签，展示商品销量排名
- [@header-icons](smart-mp-biz/src/components_v2/header-icons/README.md)：头部图标，页面顶部功能图标集合

### 容器组件
- [@page-container](smart-mp-biz/src/components_v2/page-container/README.md)：页面容器，统一页面结构
- [@main-provider](smart-mp-biz/src/components_v2/main-provider/README.md)：主题提供者，全局主题管理
- [@main-provider-theme](smart-mp-biz/src/components_v2/main-provider-theme/README.md)：主题定制，自定义主题配置
- [@i18n-provider](smart-mp-biz/src/components_v2/i18n-provider/README.md)：国际化提供者，多语言支持
- [@safe-bottom](smart-mp-biz/src/components_v2/safe-bottom/README.md)：安全底部，适配底部安全区
- [@expand](smart-mp-biz/src/components_v2/expand/README.md)：展开收起，可折叠内容区域

### 加载与反馈组件
- [@loading-flicker](smart-mp-biz/src/components_v2/loading-flicker/README.md)：加载闪烁，加载状态动画
- [@full-loading](smart-mp-biz/src/components_v2/full-loading/README.md)：全屏加载，覆盖整个页面的加载状态

### 功能组件
- [@floating-bubble](smart-mp-biz/src/components_v2/floating-bubble/README.md)：浮动气泡，悬浮在页面上的功能入口
- [@fast-panel](smart-mp-biz/src/components_v2/fast-panel/README.md)：快捷面板，快速访问常用功能
- [@generics-default](smart-mp-biz/src/components_v2/generics-default/README.md)：通用默认值，提供各类默认状态展示

## 组件使用规范
- 业务组件应基于基础UI组件库构建，避免重复开发基础功能
- 组件应具有清晰的业务语义，组件名称应直观反映其用途
- 每个业务组件需提供完整的属性、事件和插槽文档
- 组件应支持自定义样式和主题定制，与UI组件库保持一致的样式语言
- 业务组件应当支持数据双向绑定，提供必要的事件回调

## 核心技术
- 基于小程序自定义组件规范开发
- 使用TypeScript进行开发，提供类型支持
- 使用Less进行样式开发
- 遵循组件化、模块化设计原则
- 适配微信和支付宝双平台

## 组件设计规范
- 遵循清晰、简洁的设计原则
- 保持一致的命名和参数传递方式
- 提供完整的文档和示例代码
- 支持完备的事件和属性配置
- 同时兼容微信和支付宝平台

## 目录结构规范
每个业务组件应遵循以下目录结构：
```
components_v2/
  └── component-name/
      ├── index.ts        # 组件入口文件
      ├── index.wxml      # 组件模板
      ├── index.less      # 组件样式
      ├── index.json      # 组件配置
      ├── README.md       # 组件文档
      └── __tests__/      # 测试文件目录
```

## 最佳实践
- 优先使用组件库中的组件，避免重复开发
- 参考组件README了解完整的API和事件
- 使用外部样式类进行样式定制，而非直接修改组件样式
- 根据业务需求合理组合使用多个基础组件
- 在开发新组件前，请先运行`nvm use 20`确保Node.js版本兼容性