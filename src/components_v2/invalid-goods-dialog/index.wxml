<wxs src="@wxs/utils.wxs" module="utils" />

<smart-ui-dialog
  show="{{list && list.length}}"
  title="{{utils.i18n.t('以下商品无法购买',i18nData)}}"
  confirmButtonText="{{utils.i18n.t('重新选购',i18nData)}}"
  catch:confirm="onConfirm"
  show-confirm-button
  useSlot
  custom-class="invalid-goods-wrap"
>
  <view wx:for="{{list}}" wx:key="index" class="flex-between flex-row w-f100 color-gray text-30 mb-40">
    <view class="color-black ellipsis flex-shrink invalid-goods-dialog__name">{{item.name}}</view>
    <view class="flex-shrink mr-6 ml-6 ellipsis invalid-goods-dialog__num">x{{item.num}}</view>
    <view class="ellipsis flex-shrink invalid-goods-dialog__reason">{{item.reason}}</view>
  </view>
</smart-ui-dialog>
