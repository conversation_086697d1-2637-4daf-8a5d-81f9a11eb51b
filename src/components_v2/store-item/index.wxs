function getStoreItemDiscountTagClass(color) {
  if (!color) return '';
  else return ('color-' + color).toLowerCase();
}

/**
 * 卡片的折叠与展开
 */
function onCollapsingCard(newValue, oldValue, ownerInstance, instance) {
  var selectComponent = ownerInstance.selectComponent('.store-discount__btn');
  if (newValue && selectComponent) {
    var storePicH = ownerInstance.selectComponent('.store-pic').getBoundingClientRect().height;
    var storeDetailH = ownerInstance.selectComponent('.store-detail').getBoundingClientRect().height;
    var storeDiscountBtnH = ownerInstance.selectComponent('.store-discount__btn').getBoundingClientRect().height;
    ownerInstance.callMethod('onCollapsingCard', { storePicH, storeDetailH, storeDiscountBtnH });
  } else {
    ownerInstance.callMethod('onCollapsingCard');
  }
}

function TAKEOUT_STATUS_ENUM(value) {
  switch (value) {
    case 1:
      return {
        value: '外卖营业中',
        class: 'store-status-takeout-open'
      };
    case 2:
      return {
        value: '外卖已打烊',
        class: 'store-status-takeout-close'
      };
    case 3:
      return {
        value: '仅限到店自取',
        class: 'store-status-takeout-self'
      };
  }
}

function BUSINESS_STATUS_TAG(value) {
  switch (value) {
    case 1:
      return 'store-status-business__status_tag1';
    case 2:
      return 'store-status-business__status_tag2';
    case 3:
      return 'store-status-business__status_tag3';
  }
}

module.exports = {
  getStoreItemDiscountTagClass: getStoreItemDiscountTagClass,
  onCollapsingCard: onCollapsingCard,
  TAKEOUT_STATUS_ENUM: TAKEOUT_STATUS_ENUM,
  // DELIVERY_TAG_ENUM: DELIVERY_TAG_ENUM,
  BUSINESS_STATUS_TAG: BUSINESS_STATUS_TAG,
};
