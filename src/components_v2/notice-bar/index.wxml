<wxs src="@wxs/utils.wxs" module="utils" />
<view class="notice-bar-out-side" wx:if="{{!(serviceType==1 && isAllow)}}">
  <view class="notice-component-wrap show">
    <view wx:if="{{noticeShowType && (noticeShowType === 1 || noticeShowType === 3)}}" class="business-wrap">
      <view class="flex-center">{{utils.i18n.t( '店铺已打烊了',i18nData )}}</view>
      <view class="business-wrap__time flex-row flex-center" wx:if="{{timeStr && timeStr.length}}">
        <view class="ellipsis">{{utils.i18n.t( '营业时段',i18nData )}}：{{timeStr[0].label}}{{timeStr[0].value}}</view>
        <view class="business-wrap__time__detail" catch:tap="toggleShowTime" wx:if="{{serviceType !== 2 && !isEqualTime}}">{{utils.i18n.t( '点击查看更多',i18nData )}}></view>
      </view>
      <view class="business-wrap__time flex-center" wx:else>{{utils.i18n.t( '商家暂停营业',i18nData )}}</view>
    </view>
    <block wx:elif="{{noticeShowType & 2}}">
      <view class="title">{{utils.i18n.t( '收银机断网了，商家无法接单',i18nData )}}</view>
      <view class="title">{{utils.i18n.t( '请联系商家处理',i18nData )}}</view>
    </block>
  </view>
</view>
<view class="notice-bar-dialog">
  <smart-ui-dialog
    wx:if="{{isShowTime}}"
    show="{{isShowTime}}"
    title="{{utils.i18n.t( '营业时段',i18nData )}}"
    footer="{{false}}"
    useSlot
    confirmButtonText="{{utils.i18n.t( '我知道了',i18nData )}}"
    custom-class="notice-bar-class {{noticeBarClass}}"
    catch:close="onNoticeBarClose"
  >
    <view class="notice-bar-dialog-content">
      <view wx:for="{{timeShow}}" wx:key="index">
        <block wx:if="{{item.break}}">
          <view>{{item.label}}</view>
          <view>{{item.value}}</view>
        </block>
        <view wx:else>{{item.label}} {{item.value}}</view>
      </view>
    </view>
  </smart-ui-dialog>
</view>
