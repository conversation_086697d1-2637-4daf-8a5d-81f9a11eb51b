import BaseComponent from '@BaseComponent'
import { getPageUrl } from '@utils'
import _ from '@wosai/emenu-mini-lodash'

BaseComponent({
  externalClasses: ['custom-class', 'custom-error-class'],
  data: {
    traceId: ''
  },
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    description: {
      type: String,
      value: '加载中'
    },
    screenTop: {
      type: Number,
      value: 0
    },
    errors: {
      type: Array,
      value: []
    },
    sqbBridge: {
      type: Object,
      value: {}
    },
    showBack: {
      type: Boolean,
      value: false
    }
  },
  observers: {
    errors(errors) {
      if (!_.isEmpty(errors)) {
        const { sqbBridge } = this.data

        const traceId = sqbBridge.getTraceId()

        if (traceId) this.setData({ traceId: `(${_.toUpper(traceId.substring(0, 8))})` })
        sqbBridge.sls('INFO', {
          type: 'loading:error',
          data: errors
        })
      }
    }
  },
  lifetimes: {
    attached() {
      try {
        const info = wx.getSystemInfoSync()
        const screenTop = _.get(info, 'screenTop', 0)
        screenTop && this.setData({ screenTop })
      } catch (e) {
        console.log(e)
      }
    }
  },
  // @ts-ignore
  methods: {
    goHome() {
      const { sqbBridge } = this.data
      const storeId = sqbBridge.getStore('storeId')
      wx.reLaunch({
        url: getPageUrl('store'),
        query: {
          storeId
        }
      })
    }
  }
})
