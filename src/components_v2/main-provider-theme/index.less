@import (css) '@styles/common';
@import (css) '@styles/flex';

.smart-main-theme-bg {
  --_default-color: #ffffff;
  // 导航栏背景图片
  --smart-main-theme-bg-background-image: var(
    --main__bg-img-3k2l1m0n9o8p,
    url('https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png')
  );
  --smart-main-theme-bg-background-color: #f6f6f6;

  .theme-content {
    background-image: var(
      --main__bg-img-3k2l1m0n9o8p,
      url('https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png')
    );
  }

  &-nav-bar {
    --icon-color: var(--navigation-bar__color, var(--_default-color));
    --nav-bar-icon-color: var(--navigation-bar__color, var(--_default-color));
    --nav-bar-title-text-color: var(--navigation-bar__color, var(--_default-color));

    &::before {
      background: var(
          --main__bg-img-3k2l1m0n9o8p,
          url('https://marketing-static.shouqianba.com/gallery/59c8ee627ccc2453d96d1a48c3b93e25.png')
        )
        no-repeat top center / cover;
    }
  }

  ::-webkit-scrollbar {
    width: 0;
    height: 0;
    color: transparent;
    display: none;
  }

  -webkit-overflow-scrolling: touch;
  scroll-snap-type: none;
  background-color: var(--smart-main-theme-bg-background-color, transparent);

  &-content {
    -webkit-overflow-scrolling: auto;
    scroll-snap-align: none;
    overflow-y: auto;
  }

  &-nav-bar {
    --icon-size: 36px;

    --nav-bar-title-font-size: 34px;
    font-weight: bold;

    &-left {
      --padding-md: 12px;
    }
  }

  .default-nav-bar {
    --nav-bar-icon-color: #000000 !important;
    --nav-bar-title-text-color: #000000 !important;
    --icon-color: #000000 !important;
    background-color: white;
    border-bottom: 1px solid #dddddd;
  }

  .theme-content {
    // background-image: var(--smart-main-theme-bg-background-image, none);
    background-size: var(--smart-main-theme-bg-background-size, contain);
    background-position: var(--smart-main-theme-bg-background-position, center top);
    background-repeat: no-repeat;

    .smart-main-theme-bg-nav-bar {
      &::before {
        display: block;
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        opacity: var(--opacity, 1);
        // background: var(--smart-main-theme-bg-background-image) no-repeat top center / cover;
        transition: opacity 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      }
    }
  }
}
