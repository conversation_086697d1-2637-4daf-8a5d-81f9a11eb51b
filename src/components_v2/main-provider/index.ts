import BaseComponent from '@BaseComponent'
import Notify from '@wosai/smart-mp-ui/notify/notify'
// import Dialog from '@wosai/smart-mp-ui/dialog/dialog'
import Toast from '@wosai/smart-mp-ui/toast/toast'
import Dialog from '@wosai/smart-mp-ui/dialog/dialog'
import _ from '@wosai/emenu-mini-lodash'
import { getCurrentPageUrl } from '@wosai/emenu-mini-utils'

const components = ['../goods/item/index', '../goods-detail/index']
type RelationType = 'descendant' | 'child'

interface Relation {
  type: RelationType
  linked: (target: any) => void
}

interface Relations {
  [key: string]: Relation
}

interface ServiceTypeInfo {
  serviceType?: string
  serviceTypeName?: string
}

interface TrackingContext {
  sqbBridge: {
    track: (event: string, data: any) => void
  }
  pageTypeInfo: ServiceTypeInfo
  trackData: Record<string, any>
}

interface PageNameRule {
  serviceType: string | number
  serviceTypeName: string
  pageName: string
}

interface PageNameConfig {
  path: string
  rules: PageNameRule[]
  getPageName: (params: ServiceTypeInfo) => string
}

function linked(componentName: string) {
  return function (target: WechatMiniprogram.Component.TrivialInstance) {
    console.log('linked', componentName, target)
  }
}

const relations: Relations = _.reduce(
  components,
  (curr: Relations, key: string) => {
    const type = key === '../goods-detail/index' ? 'descendant' : 'child'
    curr[key] = {
      type,
      linked: linked(key)
    }
    return curr
  },
  {}
)

const getPageNameByRules = function (rules: any, params: any) {
  const rule = _.find(
    rules,
    r => r.serviceType == params.serviceType && r.serviceTypeName === params.serviceTypeName
  )
  return rule ? rule.pageName : ''
}

const pageNameConfigs: PageNameConfig[] = [
  {
    path: 'E1QDEFPQUJWB/886iep4iu37w/index',
    rules: [
      { serviceType: '2', serviceTypeName: 'subscribe_order', pageName: '扫码点单提交订单页' },
      { serviceType: '1', serviceTypeName: 'take_out_order', pageName: '外卖提交订单页' },
      { serviceType: '1', serviceTypeName: 'pre_order', pageName: '自取提交订单页' }
    ],
    getPageName(params) {
      return getPageNameByRules(this.rules, params)
    }
  },
  {
    path: 'JDLTQXMWBQMW/w9eivgfaetzc/index',
    rules: [
      { serviceType: '2', serviceTypeName: 'subscribe_order', pageName: '先付围餐提交订单页' }
    ],
    getPageName(params) {
      return getPageNameByRules(this.rules, params)
    }
  },
  {
    path: 'JDLTQXMWBQMW/0dvxv1fm8z9l/index',
    rules: [
      { serviceType: '2', serviceTypeName: 'subscribe_order', pageName: '后付围餐提交订单页' }
    ],
    getPageName(params) {
      return getPageNameByRules(this.rules, params)
    }
  }
]

BaseComponent({
  options: {
    multipleSlots: true
    // addGlobalClass: true,
    // styleIsolation: 'page-apply-shared'
  },
  externalClasses: ['custom-class', 'dialog-class'],
  properties: {
    navbar: {
      type: Boolean,
      value: false
    },
    tabbar: {
      type: Boolean,
      value: false
    },
    fullHeight: {
      type: Boolean,
      value: false
    },
    themeVars: {
      type: Object,
      value: {}
    },
    background: {
      type: String
    }
    // useLoading: {
    //   type: Boolean,
    //   value: false
    // },
    // loading: {
    //   type: Boolean,
    //   value: true
    // }
  },
  data: {
    _themeVars: {}
  },
  relations,
  // @ts-ignore
  // TODO: fix this type

  lifetimes: {
    attached() {
      this.__key = 'mainProviderComp'
      const { fullHeight } = this.data
      this.setData({
        safeAreaBottom: fullHeight ? 0 : this.getSafeAreaBottom(),
        safeAreaTop: fullHeight ? 0 : this.getSafeAreaTop()
        // _themeVars: {
        //   // ..._themeVars,
        //   // ...themeVars
        // }
      })
    }
  },
  // @ts-ignore
  methods: {
    /*
     * TODO: 暂时不用
     * */
    $alert(opts: SmartMpUI.DialogOptions | 'string' = {}) {
      const defaultOpts = {
        title: '提示',
        selector: '#smart-dialog',
        context: this,
        zIndex: 10000000,
        type: 'confirm'
      } as SmartMpUI.DialogOptions
      if (typeof opts === 'string') {
        defaultOpts.message = opts
      } else if (typeof opts === 'object') {
        Object.assign(defaultOpts, opts)
      }
      // @ts-ignore
      return Dialog[defaultOpts.type || 'confirm']({ ...defaultOpts })
    },
    /**
     * * TODO: 暂时不用
     * @param opts
     */
    $notify(opts) {
      const defaultOpts = { context: this, selector: '#smart-notify', type: 'success' }
      // @ts-ignore
      return Notify({ ...defaultOpts, ...opts })
    },
    $toast(opts) {
      const defaultOpts = {
        context: this,
        selector: '#smart-toast'
      } as SmartMpUI.ToastOptions

      if (typeof opts === 'string') {
        defaultOpts.message = opts
      } else if (typeof opts === 'object') {
        Object.assign(defaultOpts, opts)
      }

      Toast({ ...defaultOpts })
    },
    $loading(opts) {
      const defaultOpts = {
        context: this,
        duration: 0,
        selector: '#smart-toast',
        type: 'sqbLoading',
        forbidClick: true
      } as SmartMpUI.ToastOptions

      if (typeof opts === 'string') {
        defaultOpts.message = opts
      } else if (typeof opts === 'object') {
        Object.assign(defaultOpts, opts)
      }
      // @ts-ignore
      Toast.loading({ ...defaultOpts, ...opts })
    },
    $clearToast() {
      Toast.clear()
    },
    $track(event: string, trackingContext: TrackingContext) {
      const { sqbBridge, pageTypeInfo, trackData } = trackingContext
      const { serviceType, serviceTypeName } = pageTypeInfo

      const currentPath = _.split(getCurrentPageUrl(), '?')[0]
      const pageConfig = _.find(pageNameConfigs, { path: currentPath })
      const pageName = pageConfig ? pageConfig.getPageName({ serviceType, serviceTypeName }) : ''
      const eventData = _.assign({}, trackData)

      if (pageName) {
        eventData.sm_page_name = pageName
      }
      sqbBridge.track(event, eventData)
    }
  }
})
