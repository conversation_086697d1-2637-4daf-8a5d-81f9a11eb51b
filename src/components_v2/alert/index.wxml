<wxs src="@wxs/utils.wxs" module="utils" />
<view class="alert-wrap" wx:if="{{alertVisible}}">
  <view class="alert">
    <view class="alert-title">
      <rich-text nodes="{{titleNode}}"></rich-text>
    </view>
    <view class="alert-content">
      <rich-text nodes="{{contentNode}}"></rich-text>
    </view>
    <view class="alert-line"></view>
    <view class="alert-footer">
      <view class="alert-footer-cancel" bindtap="handleClickAlertCancel" wx:if="{{cancel}}">
        <rich-text nodes="{{cancelNode}}" class="alert-footer-cancel-rich-text"></rich-text>
      </view>
      <view class="alert-footer-divider"></view>
      <view class="alert-footer-ok {{utils.classNames({alertFooterOkMy: !isWeixin})}}" bindtap="handleClickAlertOk" wx:if="{{ok}}">
        <rich-text nodes="{{okNode}}"></rich-text>
      </view>
    </view>
  </view>
</view>
