import _ from '@wosai/emenu-mini-lodash'
import { StringUtils } from '@wosai/emenu-mini-utils'
import BaseComponent from '@BaseComponent'

BaseComponent({
  externalClasses: ['custom-class', 'icon-class', 'counter-class'],
  properties: {
    type: {
      type: String,
      value: 'counter' // counter: 计数器 number: 仅显示数字
    },
    value: {
      type: Number,
      value: 0
    },
    min: {
      type: null,
      value: 0
    },
    minMessage: {
      type: String
    },
    max: {
      type: null
    },
    maxMessage: {
      type: String
    },
    disabled: {
      type: Boolean,
      value: false
    },
    customClass: {
      type: String,
      value: ''
    },
    authUserProps: {
      type: Object,
      value: {}
    },
    isMaxDisabled: {
      type: Boolean,
      value: false
    },
    isMinDisabled: {
      type: Boolean,
      value: false
    },
    sumCurValue: {
      type: Number,
      value: null
    },
    disabledColor: {
      type: String,
      value: '#ffa673'
    },
    minusBorderColor: {
      type: String,
      value: ''
    },
    minusColor: {
      type: String,
      value: ''
    },
    sumValue: {
      type: Number,
      value: null
    },
    index: {
      type: String,
      value: null
    },
    size: {
      type: Number,
      value: 22
    },
    countStyle: {
      type: String,
      value: null
    },
    weixinAuthData: {
      type: Object,
      value: {}
    },
    sqbBridge: {
      type: Object,
      value: null
    },
    /**
     * @deprecated
     * 将废弃，请使用input属性指定是否启用输入框
     */
    useCount: {
      type: Boolean,
      value: false
    },
    input: {
      type: Boolean,
      value: false
    },
    showMinusIcon: {
      type: Boolean,
      value: true
    },
    config: {
      type: Object,
      value: {}
    },
    forceShowMinusIcon: {
      type: Boolean,
      value: false
    },
    isCounterTrigger: {
      type: Boolean,
      value: false
    }
  },
  data: {
    maxDisabled: false,
    fontSize: 22
  },
  lifetimes: {
    created() {
      this.__key = 'counterComp'
    }
  },
  observers: {
    'sumCurValue,isMaxDisabled,sumValue,max,value': function (
      sumCurValue,
      isMaxDisabled,
      sumValue,
      max,
      value
    ) {
      if (!isMaxDisabled) return
      let maxDisabled = sumCurValue && sumCurValue >= sumValue
      if (!_.isNil(max)) {
        maxDisabled = maxDisabled || value >= max
      }
      this.setData({ maxDisabled })
    },
    size(size) {
      if (!size) return
      this.setData({ fontSize: size })
    }
  },
  // @ts-ignore
  methods: {
    onTapAuthUser() {},

    bindFocusClick() {
      if (this.data.disabled) return
      this.$emit('focus', { index: this.data.index })
    },
    onPlus(event) {
      const { min, max, maxMessage, value, disabled, maxDisabled } = this.data
      if ((!_.isNil(max) && value >= max) || maxDisabled) {
        if (maxMessage) {
          this.$emit('showToast', StringUtils.render(maxMessage, { min, max, value }))
        }
        return
      }
      if (!disabled) {
        this.$emit('plus', { index: this.data.index, ...event.detail })
      }
    },
    onMinus(event) {
      const { min, max, minMessage, value, disabled } = this.data
      if (!_.isNil(min) && value <= min) {
        if (minMessage) {
          this.$emit('showToast', StringUtils.render(minMessage, { min, max, value }))
        }
        return
      }
      // if (!disabled) {
      this.$emit('minus', { index: this.data.index, ...event.detail })
      // }
    },
    onLongPress(event) {
      this.$emit('long', event)
    },
    onAuthUserPlusCallback() {
      this.$emit('authOnce')
      this.onPlus({})
    },

    onAuthUserMinusCallback() {
      this.$emit('authOnce')
      this.onMinus({})
    }
  }
})
