// import ApoMixins from '@utils/mixins/aop'
import BaseComponent from '@BaseComponent'
import { useAopBehavior } from '@behaviors'
// @ts-ignore
import _ from '@wosai/emenu-mini-lodash'

BaseComponent({
  V2: true,
  behaviors: [useAopBehavior()],
  externalClasses: ['custom-class'],
  properties: {
    sqbBridge: {
      type: Object,
      value: null
    },
    lists: {
      type: Array,
      value: []
    },

    trackParams: {
      type: Object,
      value: {}
    }
  },

  observers: {
    // 进来曝光第一个banner
    lists(value) {
      if (value && value.length) {
        this.smMpUIViewDisplay(0)
      }
    }
  },

  data: {
    hasDsiaplayMap: {},
    currntIndex: 0
  },
  lifetimes: {
    created() {
      this.__key = 'adsenseListComp'
    }
  },

  methods: {
    bindchange(e) {
      let index = e.detail.current
      this.setData({
        currentIndex: index
      })
      this.smMpUIViewDisplay(index)
    },
    onClick(e) {
      let index = e.currentTarget.dataset.index
      let item = this.data.lists[index]
      let { trackParams } = this.data
      this.onClickAopItem(item, trackParams)
      return
    },

    /**
     * 展示曝光 只曝光一次
     */
    smMpUIViewDisplay(index) {
      let item = _.defaultTo(this.data.lists, this.properties.lists)[index]
      if (!item) return

      let { hasDsiaplayMap } = this.data
      if (hasDsiaplayMap[index]) return
      hasDsiaplayMap[index] = true

      const { sqbBridge = this.properties.sqbBridge, trackParams = this.properties.trackParams } =
        this.data
      sqbBridge.track('SmMpUIViewDisplay', {
        activity_title: item.publishName,
        activity_url: item.url,
        ...trackParams
      })
    },

    onLongPress(e) {
      const index = e.currentTarget.dataset.index
      this.$emit('adlongpress', { index })
    }
  }
})
