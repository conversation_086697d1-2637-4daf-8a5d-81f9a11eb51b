import BaseComponent from '@BaseComponent'
import _ from '@wosai/emenu-mini-lodash'

/**
 * 商品虚拟列表组件 - 优化大量商品渲染性能
 */
BaseComponent({
  properties: {
    // 商品列表数据
    items: {
      type: Array,
      value: []
    },
    // 每个商品项的高度
    itemHeight: {
      type: Number,
      value: 120
    },
    // 可视区域高度
    containerHeight: {
      type: Number,
      value: 600
    },
    // 缓冲区大小（上下各渲染多少个额外项目）
    bufferSize: {
      type: Number,
      value: 5
    },
    // 商品布局配置
    goodsLayoutConfigs: {
      type: Object,
      value: {}
    },
    // 商品显示模式
    goodsDisplayMode: {
      type: Number,
      value: 0
    }
  },

  data: {
    // 当前滚动位置
    scrollTop: 0,
    // 可视区域内的商品索引范围
    visibleRange: {
      start: 0,
      end: 0
    },
    // 实际渲染的商品列表
    renderItems: [],
    // 上方占位高度
    topPlaceholderHeight: 0,
    // 下方占位高度
    bottomPlaceholderHeight: 0
  },

  observers: {
    'items, containerHeight, itemHeight, bufferSize'() {
      this.updateVisibleItems()
    }
  },

  methods: {
    /**
     * 滚动事件处理
     */
    onScroll(event) {
      const { scrollTop } = event.detail
      this.setData({ scrollTop })
      
      // 防抖处理，避免频繁计算
      this.debounceUpdateVisibleItems()
    },

    /**
     * 防抖更新可视项目
     */
    debounceUpdateVisibleItems: _.debounce(function() {
      this.updateVisibleItems()
    }, 16), // 约60fps

    /**
     * 更新可视区域内的商品
     */
    updateVisibleItems() {
      const { items, itemHeight, containerHeight, bufferSize, scrollTop } = this.data
      
      if (!items || items.length === 0) {
        this.setData({
          renderItems: [],
          topPlaceholderHeight: 0,
          bottomPlaceholderHeight: 0
        })
        return
      }

      // 计算可视区域内的索引范围
      const startIndex = Math.floor(scrollTop / itemHeight)
      const endIndex = Math.min(
        startIndex + Math.ceil(containerHeight / itemHeight),
        items.length - 1
      )

      // 添加缓冲区
      const bufferedStart = Math.max(0, startIndex - bufferSize)
      const bufferedEnd = Math.min(items.length - 1, endIndex + bufferSize)

      // 提取需要渲染的商品
      const renderItems = items.slice(bufferedStart, bufferedEnd + 1).map((item, index) => ({
        ...item,
        virtualIndex: bufferedStart + index,
        realIndex: bufferedStart + index
      }))

      // 计算占位高度
      const topPlaceholderHeight = bufferedStart * itemHeight
      const bottomPlaceholderHeight = (items.length - bufferedEnd - 1) * itemHeight

      this.setData({
        visibleRange: { start: bufferedStart, end: bufferedEnd },
        renderItems,
        topPlaceholderHeight,
        bottomPlaceholderHeight
      })
    },

    /**
     * 商品点击事件
     */
    onGoodsClick(event) {
      const { realIndex } = event.currentTarget.dataset
      const { items } = this.data
      const goods = items[realIndex]
      
      this.triggerEvent('goodsClick', {
        goods,
        index: realIndex
      })
    },

    /**
     * 加购事件
     */
    onAddToCart(event) {
      const { realIndex } = event.currentTarget.dataset
      const { items } = this.data
      const goods = items[realIndex]
      
      this.triggerEvent('addToCart', {
        goods,
        index: realIndex
      })
    }
  }
})
