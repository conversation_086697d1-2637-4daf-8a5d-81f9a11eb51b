<wxs src="@wxs/utils.wxs" module="utils" />

<scroll-view 
  class="virtual-list-container"
  scroll-y
  scroll-top="{{scrollTop}}"
  style="height: {{containerHeight}}px;"
  bindscroll="onScroll"
  enhanced="{{true}}"
  enable-passive="{{true}}"
>
  <!-- 上方占位 -->
  <view 
    class="placeholder-top" 
    style="height: {{topPlaceholderHeight}}px;"
  ></view>
  
  <!-- 实际渲染的商品列表 -->
  <view class="render-items-container">
    <view 
      wx:for="{{renderItems}}" 
      wx:key="uuid"
      wx:for-item="goods"
      class="virtual-goods-item"
      style="height: {{itemHeight}}px;"
      data-real-index="{{goods.realIndex}}"
      bindtap="onGoodsClick"
    >
      <smart-goods-item
        goodsImgWidth="{{goodsLayoutConfigs[goodsDisplayMode].imgWidth}}"
        goodsImgHeight="{{goodsLayoutConfigs[goodsDisplayMode].imgHeight}}"
        layout="{{goodsLayoutConfigs[goodsDisplayMode].layout}}"
        title="{{goods.name}}"
        custom-class="goods-item-class goods-item-class-list box-border"
        custom-style="{{utils.toStyle(goodsLayoutConfigs[goodsDisplayMode].customStyle)}}"
        thumb="{{goods.photo_url}}"
        info="{{goods}}"
        style="--goods-item-padding-bottom: {{goodsLayoutConfigs[goodsDisplayMode].itemBottom}}PX;--goods-item-min-height: {{goodsLayoutConfigs[goodsDisplayMode].minItemHeight}}PX;"
        data-real-index="{{goods.realIndex}}"
        catch:plus="onAddToCart"
        catch:click="onGoodsClick"
      />
    </view>
  </view>
  
  <!-- 下方占位 -->
  <view 
    class="placeholder-bottom" 
    style="height: {{bottomPlaceholderHeight}}px;"
  ></view>
</scroll-view>
