.virtual-list-container {
  width: 100%;
  position: relative;
  overflow: hidden;
}

.placeholder-top,
.placeholder-bottom {
  width: 100%;
  background: transparent;
}

.render-items-container {
  width: 100%;
}

.virtual-goods-item {
  width: 100%;
  box-sizing: border-box;
  display: flex;
  align-items: center;
  border-bottom: 1px solid #f5f5f5;
  
  &:last-child {
    border-bottom: none;
  }
}
