<!-- 优惠信息 -->
<view class="goods-item__discount" wx:if="{{showDiscount && info.discount_texts && info.discount_texts.length}}">
  <view class="goods-item__discount-items" wx:if="{{info.discount_texts.length}}">
    <smart-ui-split-tag
      wx:for="{{info.discount_texts}}"
      wx:key="discount_text"
      wx:if="{{index < showDiscountNum}}"
      type="primary"
      divide
      custom-class="{{utils.classNames('goods-item__discount-tag split-tag-class', {grayscale:  !isOnSale || info.out_of_stock})}} discount-tag-class {{ discountTagClass }}"
      label="{{item.discount_text}}"
      value="{{item.quota_count ? utils.i18n.t( '限{}份',i18nData,[item.quota_count] ) : ''}}"
      round="2"
      border
    />
  </view>

  <!-- 库存信息 -->
</view>
<!-- TODO: 等产品确认在网格下放置 -->
<view class="goods-item__stock" wx:if="{{stockIn=='body' && !info.out_of_stock && info.sku && info.sku <= 10}}">
  <smart-ui-info info="{{utils.i18n.t('仅剩{}份',i18nData,[info.sku])}}" custom-class="goods-item__stock-info" />
</view>
