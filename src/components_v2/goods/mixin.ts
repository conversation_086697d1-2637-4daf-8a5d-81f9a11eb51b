// @ts-nocheck
import _ from '@wosai/emenu-mini-lodash'
import { isAlipay, NumberUtils } from '@wosai/emenu-mini-utils'
import CONFIG from '@wosai/emenu-mini-config'
import { isWeighingGoods } from '@utils'
import { getOnSaleStatus, getPriceSuffix, isMultiple, useLocalCart } from '@utils/helper'
import dayjs from '@wosai/emenu-mini-dayjs'
import { CartStore } from '@sqb/smart-mp-stores'
import { oss } from '@wosai/emenu-mini-utils/lib/cdn'
import { useI18nBehavior } from '@behaviors'

const { DEFAULT_GOODS_IMAGE, WEIGHT_GOODS_TOAST, HOTSALE_RANKING_COUNT } = CONFIG
const { multiply } = NumberUtils
const WEEK_DAYS_MIN = ['一', '二', '三', '四', '五', '六', '日']

/**
 * 判断是否不允许加购
 * @param goods
 * @returns {*}
 */
const isDisallow = (goods, isRetail, self, addedNum = 0): any => {
  const { unit_type, out_of_stock } = goods
  const { WEIGHT_GOODS_TOAST, SELL_OUT_TOAST } = CONFIG

  // unit_type=1 表示为重量商品
  if (unit_type) {
    // 餐饮模式执行以下逻辑
    if (!isRetail) {
      self.$emit('showToast', WEIGHT_GOODS_TOAST)
      return true
    }
  }
  // 无库存
  if (out_of_stock) {
    self.$emit('showToast', SELL_OUT_TOAST)
    return true
  }
  // 有库存限制
  if (_.isNumber(goods.sku) && addedNum >= goods.sku) {
    self.$emit('showToast', self.t('仅剩{}份', [goods.sku]))
    return true
  }
}
/**
 * 获取本地购物车商品库存
 * @param info
 * @param addedNum
 * @returns {number}
 */
// function getLocalGoodsSku(info, addedNum): number {
//   return Math.max(0, info.sku - addedNum)
// }

const options = {
  options: {
    multipleSlots: true,
    // @ts-ignore
    externalClasses: true,
    // styleIsolation: 'apply-shared'
    lifetimes: true
  },
  behaviors: [useI18nBehavior({})],
  externalClasses: [
    'custom-class',
    'image-class',
    'title-class',
    'price-container-class',
    'price-class',
    'suffix-price-class',
    'original-price-class',
    'goods-footer-class',
    'plus-icon-class',
    'minus-icon-class',
    'badge-class'
  ],
  properties: {
    isRetail: {
      type: Boolean,
      value: false
    },
    showSalesInfo: {
      type: Boolean,
      value: true
    },
    showDescExpand: {
      type: Boolean,
      value: false
    },
    showDescription: {
      type: Boolean,
      value: true
    },
    showPriceSuffix: {
      type: Boolean,
      value: true
    },
    counterMode: {
      type: String,
      optionalTypes: ['single', 'default'],
      value: 'default'
    },
    goodsImgWidth: {
      type: Number,
      value: 40
    },
    goodsImgHeight: {
      type: Number,
      value: 40
    },
    useCount: {
      type: Boolean,
      value: true
    },
    showOriginalPrice: {
      type: Boolean,
      value: true
    },
    showDiscount: {
      type: Boolean,
      value: true
    },
    showDiscountNum: {
      type: Number,
      value: 2
    },
    showUnit: {
      type: Boolean,
      value: true
    },
    showGoodsFooter: {
      type: Boolean,
      value: true
    },
    showAttachedInfo: {
      type: Boolean,
      value: true
    },
    rightNum: {
      type: Boolean,
      value: false
    },
    showPrice: {
      type: Boolean,
      value: true
    },
    frontLinePrice: {
      type: Boolean,
      value: false
    },
    showAvatar: {
      type: Boolean,
      value: false
    },
    centerNum: {
      type: Boolean,
      value: false
    },
    footerNum: {
      type: Boolean,
      value: false
    },
    centerPrice: {
      type: Boolean,
      value: false
    },
    horizontalPrice: {
      type: Boolean,
      value: false // 价格是否水平显示
    },
    stockIn: {
      type: String,
      value: 'body' // image: 图片里面的库存组件, body: 底部下的组件
    },
    scene: {
      type: String,
      value: 'goods_list' // goods_list, goods_detail, goods_grid, cart 用于区分场景
    },
    showDiscountFlag: {
      type: Boolean,
      value: false
    },
    isUseLocalCart: {
      type: Boolean,
      value: false
    },
    // 商品信息
    info: {
      type: Object,
      value: {}
    },
    // 销量排行显示数
    ranking: {
      type: Number,
      value: 6
    },
    // 是否可用
    disabled: {
      type: Boolean,
      value: false
    },
    // 微信授权信息
    weixinAuthData: {
      type: Object,
      value: {}
    },
    // 是否启用输入框
    input: {
      type: Boolean,
      value: false
    },
    // 装修配置
    // config: {
    //   type: Object,
    //   value: {}
    // },
    // 是否高亮 - goods-card 专用
    highlight: {
      type: Boolean,
      value: false
    },
    layout: {
      type: String,
      value: 'horizontal' // vertical, horizontal
    },
    radius: {
      type: Number,
      optionalTypes: [String]
    },
    width: {
      type: String
    },
    height: {
      type: String
    },
    isShowSecondText: {
      type: Boolean,
      value: false
    },
    showCounter: {
      type: Boolean,
      value: true
    },
    useFooterSlot: {
      type: Boolean,
      value: false
    },
    useInfoSlot: {
      type: Boolean,
      value: false
    },
    useRightSlot: {
      type: Boolean,
      value: false
    },
    i18nData: {
      type: Array,
      value: null
    },
    hotSaleSeqTagType: {
      type: String,
      value: 'style1' // style1, style2, style3
    },
    isExpandDes: {
      type: Boolean,
      value: false
    },
    showGiftCard: {
      type: Boolean,
      value: true
    },
    customStyle: {
      type: String,
      value: ''
    }
  },
  data: {
    goodsCover: '',
    // 这个文本原来由config提供
    hotSaleRankingText: '销量第##名',
    addedNum: 0,
    isOnSale: false,
    isSwitchable: false,
    isSoldOut: false,
    discountTag: null,
    displayPrice: 0,
    originalPrice: 0,
    priceSuffix: null,
    // DEFAULT_HOTSALE_MEDAL_URL: 'https://smart-static.wosaimg.com/themes/default/hotsale_medal.webp',
    // 加购按钮样式
    size: 22,
    fontSize: 22,
    showMinusIcon: true,
    // localGoodsSku: 0,
    // 展开-折叠icon状态是否为展开状态
    isDescExpanded: false
  },
  // NOTE:
  // 不要此组件使用observer, 否则会导致商品组件多次渲染， 影响性能
  // observers: {
  //   info(info) {
  //     this.init(info);
  //   },
  // },
  lifetimes: {
    attached() {
      this.__key = 'goodsItemComp'
      // setTimeout(() => {
      const { info, scene, serviceType } = this.properties
      // const isDevelopment = sqbBridge.getMiniProgramProfile() === 'development';
      if (scene === 'cart') {
        this.setData({ isCartShow: true })
        const hotsaleRankingCount = this.call('getMiniProgramConfig', HOTSALE_RANKING_COUNT)
        if (hotsaleRankingCount !== this.hotsaleRankingCount) this.setData({ hotsaleRankingCount })
        this.extendInfoForCart(info)
      }
      // 本地购物车
      this.cartStore = CartStore.getInstance(this, this.data)
      // 监听购物车数量变化

      this.cartStore.watch(
        scene === 'cart' ? 'record' : 'goods',
        info,
        ['num'],
        watchCb.call(this, info),
        scene
      )
      // 图片样式
      this.setData({ imageStyles: this.buildImageStyle(info) })
      this.init(info)
      // }, 0)
    },
    detached() {
      // const { info, scene } = this.data
      // this.cartStore.unWatch(info, scene === 'cart' ? 'record' : 'goods')
    }
  },
  methods: {
    extendInfoForCart(info) {
      const _item = isAlipay()
        ? _.cloneDeep({
            ...info,
            isMultiple: isMultiple(info),
            mainImage: info.url ? info.url.split(',')[0] : '',
            description: info.attached_info
          })
        : {
            ...info,
            isMultiple: isMultiple(info),
            mainImage: info.url ? info.url.split(',')[0] : '',
            description: info.attached_info
          }
      info.url = _item.mainImage
      this.setData({
        info,
        isDirect: true,
        displayPrice: info.price,
        linePrice: info.linePrice || null
        // item: _item
      })
    },
    buildImageStyle(info) {
      const src = (info.photo_url || DEFAULT_GOODS_IMAGE).split(',')[0]
      const width = 80
      let height = 80
      const quality = 85
      const ratio = 2
      const aspect = 1
      const blur = 1

      const imageStyles = {
        borderRadius: '8px'
      }
      const ossOptions = { width, height, quality, ratio, blur, interlace: 1 }
      if (width && !height) {
        height = Math.round(width / aspect)
      }
      Object.assign(imageStyles, {
        width,
        height,
        backgroundImage: `url(${oss(src, ossOptions)})`,
        backgroundSize: width > height ? '100% auto' : 'auto 100%'
      })
      return imageStyles
    },
    getGoods() {
      const { info = {} } = this.data
      return {
        __source: 'list',
        ...info,
        item: info
      }
    },
    // 添加缓存机制避免重复计算
    _computedDataCache: new Map(),

    init(info) {
      if (!info) return

      // 检查缓存
      const cacheKey = `${info.id}_${info.sku}_${info.activity_price || info.price}_${info.sale_time}`
      if (this._computedDataCache.has(cacheKey)) {
        const cachedData = this._computedDataCache.get(cacheKey)
        this.setData(cachedData)
        return
      }

      const isOnSale = getOnSaleStatus(info.sale_time)
      const isSoldOut = info.out_of_stock || info.sku === 0

      const priceSuffix = getPriceSuffix(info)
      const {
        sku,
        activity_price,
        price,
        need_choose_spec,
        quota_count,
        multiple,
        photo_url,
        url,
        attached_info
      } = info
      const goodsCover = this.getGoodsCover(photo_url || url)
      const data = {
        isSoldOut,
        isOnSale,
        // localGoodsSku: sku,
        priceSuffix,
        isSwitchable: need_choose_spec,
        discountNum: quota_count,
        originalPrice: !multiple && activity_price ? multiply(price, 0.01) : undefined,
        displayPrice: multiply(activity_price ? activity_price : price, 0.01),
        goodsCover
      }

      // 缓存计算结果
      this._computedDataCache.set(cacheKey, data)

      // 限制缓存大小，避免内存泄漏
      if (this._computedDataCache.size > 100) {
        const firstKey = this._computedDataCache.keys().next().value
        this._computedDataCache.delete(firstKey)
      }

      // 这个是临时解决办法
      // 如果没有更好的版本，不要调整
      // wxs计算会有时序的问题
      // data.isDescOverflow = !!(attached_info && attached_info.length >= 45)

      this.setData(_.omitBy(data, _.isNil), () => {
        // 商品列表不要使用这个方式，会耗费性能
        const { showDescExpand } = this.data
        showDescExpand &&
          setTimeout(() => {
            this.calcExpandDesc(attached_info)
          }, 100)
      })
    },
    calcExpandDesc(attached_info) {
      const query = this.createSelectorQuery()
      query?.select('.goods-item__desc').boundingClientRect()
      // 超过两行则展示展开按钮
      query?.exec(res => {
        const fontSize = 11
        const lineHeight = 1.3
        const row = 2
        const maxHeight = fontSize * lineHeight * row
        const { height } = res[0] || {}
        if (height > maxHeight) {
          this.setData({ isDescOverflow: true })
        }
      })
    },

    // 获取商品封面
    getGoodsCover(url) {
      if (_.includes(url, ',')) {
        return url.split(',')[0]
      }
      return url || DEFAULT_GOODS_IMAGE
    },

    onTapGoods() {
      this.$emit('click', this.getGoods())
    },
    onTapDesc() {
      this.setData({ isDescExpanded: !this.data.isDescExpanded })
    },
    onPlus() {
      let { isRetail, isSwitchable, addedNum, sqbBridge } = this.data
      // const t = sqbBridge && sqbBridge.i18n ? sqbBridge.i18n.t : null
      const goods = this.getGoods()

      if (!isDisallow(goods, isRetail, this, addedNum)) {
        if (isSwitchable) {
          return this.$emit('choose', goods)
        }
        // if (_.get(info, 'spu_type') === 'PACKAGE') {
        if (_.get(goods, 'package')) {
          return this.$emit('selectCombo', goods)
        }
        addedNum += this.cartStore.getAddNum(goods)
        goods['__source'] = 'list'
        if (useLocalCart(this)) {
          this.update({ addedNum /*兼容旧版本*/ })
        }

        // this.setData({ localGoodsSku: getLocalGoodsSku(goods, addedNum) })
        this.$emit('plus', goods)
      }
    },
    onMinus() {
      const goods = this.getGoods()
      let { addedNum } = this.data
      addedNum += this.cartStore.getMinusNum(goods)

      if (useLocalCart(this)) {
        this.update({ addedNum /*兼容旧版本*/ })
      }

      // this.setData({ localGoodsSku: getLocalGoodsSku(goods, addedNum) })

      goods['__source'] = 'list'
      this.$emit('dec', goods)
    },
    onAuthorized() {
      this.$emit('authOnce')
    },
    onClickNumberInput() {
      const { info, disabled, isOnSale } = this.data
      if (disabled || !isOnSale || info.out_of_stock) return

      const { addedNum } = this.data
      let goods = this.getGoods()
      goods = _.cloneDeep(goods)
      _.assign(goods.item, { num: addedNum, __source: 'input' })

      // this.setData({ localGoodsSku: getLocalGoodsSku(goods, addedNum) })

      this.$emit('focus', { goods })
    },
    onLongPress() {
      if (isWeighingGoods(this.getGoods())) return
      this.onClickNumberInput()
    },
    /**
     * 将弹窗需要的数据传输给父组件，其中包括是否可售，以及可售时间的相关数据
     */
    onClickImageTips() {
      const { info } = this.data
      const sale_time = _.get(info, 'sale_time')
      const saleTimeInfo = this.formatSaleTime(sale_time)
      if (saleTimeInfo.title) this.$emit('goodsClick', saleTimeInfo)
    },
    /**
     * 获取并处理售卖时段要展示的数据
     * @param sale_time
     */
    formatSaleTime(sale_time) {
      // 处理及设置售卖日期
      const result = {
        date: '',
        cycle: '',
        times: ''
      }
      if (_.isEmpty(sale_time)) return result

      const { start_date, end_date, cycle = [], times } = sale_time

      const _title = !_.isNil(start_date) || _.size(cycle) !== 7 ? '可售周期' : '每日可售时间'
      const _date = _.isNil(start_date)
        ? ''
        : `${dayjs(start_date).format('YYYY-MM-DD')} ~ ` +
          (_.isNil(end_date) ? '长期' : dayjs(end_date).format('YYYY-MM-DD'))

      const _cycle =
        _.size(cycle) === 7 || _.size(cycle) === 0
          ? ''
          : '周' + _.map(cycle, item => WEEK_DAYS_MIN[item - 1]).join('、')
      const _times = _.join(
        _.map(_.defaultTo(times, []), item => item.start_time + '-' + item.end_time),
        ','
      )

      _.assign(result, {
        title: _title,
        date: _date,
        cycle: _cycle,
        times: _times
      })
      return result
    },
    onAuthUserPlusCallback() {
      this.$emit('authOnce')
      this.onPlus({})
    },
    onTapAuthUser() {
      // TODO: 原来这里是不需要的， 但是smart-dev不能工作，需要确认
      this.$emit('authOnce')
      this.onPlus({})
    },
    onExpandDesc() {
      const { expandDesc } = this.data
      this.setData({ expandDesc: !expandDesc })
    }
  }
}

export default options

function watchCb(info) {
  return (err, num) => {
    const { addedNum } = this.data
    if (err) {
      console.error(err)
      return
    }
    // this.setData({ localGoodsSku: getLocalGoodsSku(info, num) })

    if (num === addedNum) return

    this.update({ addedNum: num })
  }
}
