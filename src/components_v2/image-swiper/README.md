# Image Swiper 图片轮播

### 介绍

图片轮播组件用于展示多张图片的轮播效果，支持自动轮播、自定义高度和图片适配方式，适用于商品详情、店铺展示等多个场景。该组件封装了微信小程序原生swiper组件，提供更简便的图片轮播实现方式。

### 引入

```json
"usingComponents": {
  "smart-image-swiper": "@components_v2/image-swiper/index"
}
```

## 代码演示

### 基础用法

基础的图片轮播展示，传入图片数组即可实现轮播效果。

```html
<smart-image-swiper lists="{{imageList}}" />
```

### 自定义高度

通过`height`属性自定义轮播区域的高度。

```html
<smart-image-swiper 
  lists="{{imageList}}" 
  height="500rpx" 
/>
```

### 设置封面图

通过`poster`属性设置轮播的封面图。

```html
<smart-image-swiper 
  lists="{{imageList}}" 
  poster="{{coverImage}}" 
/>
```

### 自定义样式

通过外部样式类自定义图片轮播的样式。

```html
<smart-image-swiper 
  lists="{{imageList}}" 
  custom-class="my-swiper" 
  image-custom-class="my-swiper-image" 
/>
```

```css
.my-swiper {
  border-radius: 12rpx;
  overflow: hidden;
}

.my-swiper-image {
  border-radius: 12rpx;
}
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| lists | 轮播图片数组 | *string[]* | `[]` |
| height | 轮播高度 | *string* | `750px` |
| poster | 封面图片地址 | *string* | - |

### 外部样式类

Image-Swiper组件支持以下外部样式类，用于自定义组件不同部分的样式：

| 类名 | 说明 |
| --- | --- |
| custom-class | 根节点样式类 |
| image-custom-class | 图片样式类 |

### CSS变量

组件内部样式可通过CSS类名进行覆盖：

```css
.smart-image-swiper {
  /* 轮播容器样式 */
}

.smart-image-swiper__index {
  /* 轮播页码样式 */
}

.smart-image-swiper__item {
  /* 轮播项样式 */
}
```

## 注意事项

1. 图片数组中的图片地址会自动进行OSS处理，添加resize参数，限制宽度为750px。
2. 组件默认设置了自动轮播，间隔时间为3秒。
3. 组件内置了页码指示器，显示当前图片位置和总图片数量。
4. 轮播图片默认使用contain模式展示，保证图片完整显示。
5. 如果需要禁用自动轮播，可以在业务逻辑中控制autoPlay参数。

## 最佳实践

### 在商品详情中使用

```html
<view class="goods-detail">
  <smart-image-swiper 
    lists="{{goods.images}}" 
    height="750rpx" 
  />
  <view class="goods-info">
    <!-- 商品信息 -->
  </view>
</view>
```