@import (css) '../../styles/flex';
@import (css) '../../styles/common';

.store-info-container {
  --content-color: var(--s-content-color);
  --title-color: var(--s-title-color);
  --icon-color: var(--s-icon-color);
  color: var(--content-color);

  .store-info-card {
    --card-background-color: #ffffff;
    --card-padding-top: 20px;
    --card-padding-right: 20px;
    --card-padding-bottom: 20px;
    --card-padding-left: 20px;
    box-shadow: 0 3px 8px 0 rgb(0 0 0 / 7%);

    .store-info-header {
      gap: 4px;

      .value-class {
        flex: 0 0 auto !important;
      }
    }
  }

  .title {
    // color: var(--title-color);
    color: #000000;
  }

  .button {
    --button-primary-background-color: var(--primary__color);
    --button-border-width: 0;
  }

  .service-button-text-container {
    // prettier-ignore
    line-height: 20PX;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-left: 0 !important;
  }

  .button-text {
    // prettier-ignore
    line-height: 20PX;
    // prettier-ignore
    font-size: 13PX;
    color: var(--primary-foreground__color);
  }

  .icon {
    color: var(--icon-color);
    font-size: 26px;
  }

  .notice-wrap {
    color: #8c8c8c;
    height: auto;
    font-size: 24px;

    .expand-custom-class {
      --expand-height: calc(24px * 1.5);
    }
  }
}
