@import (css) '@styles/flex';
.loading-flicker-wrap {
  padding: 0 50px calc(env(safe-area-inset-bottom) / 2);
  .divider {
    padding: 0 30px;
    font-size: 26px;
    font-weight: 300;
    color: #ccc;
    &:before,
    &:after {
      content: '';
      flex: 1;
      height: 1px;
      background: #ccc;
      transform: scaleX(0.85) scaleY(0.5);
    }
  }
  .point {
    @size: 10px;
    display: inline-block;
    margin: 0 5px;
    width: @size;
    height: @size;
    border-radius: 100%;
    background: #ccc;
  }
  .flex-item {
    flex: 1;
  }
}
