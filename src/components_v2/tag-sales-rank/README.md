# TagSalesRank 销量排名标签

### 介绍

销量排名标签组件，用于展示商品在门店内的销量排名，支持多种样式，适用于商品列表、商品详情等场景。

### 引入

```json
"usingComponents": {
  "smart-tag-sales-rank": "@components_v2/tag-sales-rank/index"
}
```

## 代码演示

### 基础用法

默认使用样式1展示销量排名标签。

```html
<smart-tag-sales-rank rank-seq="1" />
```

### 样式1

门店排名标签，默认样式。

```html
<smart-tag-sales-rank 
  rank-seq="1" 
  hot-sale-seq-tag-type="style1" 
  hot-sale-ranking-text="门店第1名" 
/>
```

### 样式2

带图标的销量排名标签。

```html
<smart-tag-sales-rank 
  rank-seq="2" 
  hot-sale-seq-tag-type="style2" 
  hot-sale-ranking-text="热销第2名" 
/>
```

### 样式3

分离式销量排名标签，左侧显示"门店销量"，右侧显示排名。

```html
<smart-tag-sales-rank 
  rank-seq="3" 
  hot-sale-seq-tag-type="style3" 
/>
```

### 禁用状态

通过`disabled`属性设置禁用状态。

```html
<smart-tag-sales-rank 
  rank-seq="1" 
  disabled 
/>
```

### 自定义排名文案

通过`hot-sale-ranking-text`属性自定义排名文案，使用`{}`作为排名数字的占位符。

```html
<smart-tag-sales-rank 
  rank-seq="1" 
  hot-sale-ranking-text="本店热销第{}位" 
/>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| rank-seq | 销量排名序号 | *number* | `0` |
| hot-sale-seq-tag-type | 标签样式类型，可选值为 `style1` `style2` `style3` | *string* | `style1` |
| disabled | 是否为禁用状态 | *boolean* | `false` |
| hot-sale-ranking-text | 排名文案模板，使用`{}`作为排名数字的占位符 | *string* | `门店第{}名` |

### 外部样式类

组件内部使用了以下一些类名，可通过外部样式覆盖：

| 类名 | 说明 |
| --- | --- |
| hot-sale-seq-tag-class | 样式1和样式2的标签样式类 |
| hot-sale-seq-tag-class-left | 样式3左侧标签样式类 |
| hot-sale-seq-tag-class-right | 样式3右侧标签样式类 |
| hot-sale-tag-class | 标签额外样式类 |

### CSS变量

组件提供了以下CSS变量，可用于自定义样式。

#### 样式1 (style1)
| 名称 | 默认值 | 描述 |
| --- | --- | --- |
| --hot-sale-seq-tag-color-style1 | - | 样式1文字颜色 |
| --hot-sale-seq-tag-bg-color-style1 | - | 样式1背景颜色 |

#### 样式2 (style2)
| 名称 | 默认值 | 描述 |
| --- | --- | --- |
| --hot-sale-seq-tag-color-style2 | - | 样式2文字颜色 |
| --hot-sale-seq-tag-bg-color-style2 | - | 样式2背景颜色 |
| --hot-sale-seq-tag-icon-style2 | - | 样式2图标背景图 |
| --hot-sale-seq-tag-border-color-style2 | - | 样式2边框颜色 |

#### 样式3 (style3)
| 名称 | 默认值 | 描述 |
| --- | --- | --- |
| --hot-sale-seq-left-color-style3 | - | 样式3左侧文字颜色 |
| --hot-sale-seq-left-bg-color-style3 | - | 样式3左侧背景颜色 |
| --hot-sale-seq-right-color-style3 | - | 样式3右侧文字颜色 |
| --hot-sale-seq-right-bg-color-style3 | - | 样式3右侧背景颜色 |

#### CSS变量使用方式

在业务组件或者页面组件的CSS样式中定义TagSalesRank组件所提供的CSS变量，以自定义标签样式。

```css
.goods-item {
  /* 样式1 */
  --hot-sale-seq-tag-color-style1: #ffffff;
  --hot-sale-seq-tag-bg-color-style1: #ff6a16;
  
  /* 样式2 */
  --hot-sale-seq-tag-color-style2: #ff6a16;
  --hot-sale-seq-tag-bg-color-style2: #fff2e8;
  --hot-sale-seq-tag-icon-style2: url("path/to/icon.png");
  --hot-sale-seq-tag-border-color-style2: #ff6a16;
  
  /* 样式3 */
  --hot-sale-seq-left-color-style3: #ffffff;
  --hot-sale-seq-left-bg-color-style3: #ff9c6e;
  --hot-sale-seq-right-color-style3: #ff6a16;
  --hot-sale-seq-right-bg-color-style3: #ffffff;
}
```

## 样式说明

### 样式1
样式1是一个简单的背景色标签，显示排名文本。

### 样式2
样式2是带有图标的边框标签，左侧为图标，右侧为排名文本。图标通过CSS背景图实现。

### 样式3
样式3是分离式标签，左侧固定显示"门店销量"文本，右侧显示排名信息。

## 注意事项

1. 在使用样式2时，图标通过`--hot-sale-seq-tag-icon-style2`变量设置，需要提供完整的图标URL。
2. 排名文本中的占位符`{}`会自动替换为`rank-seq`属性的值。
3. 当设置`disabled`属性为`true`时，标签会应用灰度滤镜效果。
4. 样式3的左右标签文本是固定的，左侧显示"门店销量"，右侧显示"第X名"，其中X为`rank-seq`的值。