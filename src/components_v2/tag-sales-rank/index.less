@import (css) '@styles/common';
@import (css) '@styles/flex';

// 销量信息
.goods-item {
  &__sales {
    &-rank {
      --hot-sale-seq-tag-color-style1: var(--tag__color-style1-3m2n1o0p9q8r);
      --hot-sale-seq-tag-bg-color-style1: var(--tag__bg-color-style1-3m2n1o0p9q8r);
      // style2
      --hot-sale-seq-tag-color-style2: var(--tag__color-style2-3m2n1o0p9q8r);
      --hot-sale-seq-tag-bg-color-style2: var(--tag__bg-color-style2-3m2n1o0p9q8r);
      --hot-sale-seq-tag-icon-style2: var(--tag__bg-img-style2-3m2n1o0p9q8r);
      --hot-sale-seq-tag-border-color-style2: var(--tag__border-color-style2-3m2n1o0p9q8r);
      // style3
      --hot-sale-seq-left-color-style3: var(--tag-left__color-style3-3m2n1o0p9q8r);
      --hot-sale-seq-left-bg-color-style3: var(--tag-left__bg-color-style3-3m2n1o0p9q8r);
      --hot-sale-seq-right-color-style3: var(--tag-right__color-style3-3m2n1o0p9q8r);
      --hot-sale-seq-right-bg-color-style3: var(--tag-right__bg-color-style3-3m2n1o0p9q8r);
      display: flex;
      //gap: 10px;
    }

    &-tag {
      display: inline-flex;

      .hot-sale-seq-tag-class {
        display: flex;
        align-items: center;
        height: 34rpx;
      }

      &.style1 {
        .hot-sale-seq-tag-text {
          color: var(--hot-sale-seq-tag-color-style1);
          line-height: 1;
        }

        .hot-sale-seq-tag-class {
          // --tag-default-color: var(--hot-sale-seq-tag-bg-color-style1);
          background: var(--hot-sale-seq-tag-bg-color-style1);
        }
      }

      &.style2 {
        .hot-sale-seq-tag-text {
          color: var(--hot-sale-seq-tag-color-style2);
        }

        .hot-sale-seq-tag-class {
          border: 1px solid var(--hot-sale-seq-tag-border-color-style2);
          // --tag-default-color: var(--hot-sale-seq-tag-bg-color-style2);
          background: var(--hot-sale-seq-tag-bg-color-style2);
          gap: 8rpx;
          padding-left: 0;

          &::before {
            content: '';
            display: block;
            /* prettier-ignore */
            width: 14PX;
            height: 100%;
            background: var(--hot-sale-seq-tag-icon-style2) no-repeat;
            background-size: contain;
            background-position: center center;
            padding-right: 8rpx;
            border-right: 1px solid var(--hot-sale-seq-tag-border-color-style2);
          }
        }
      }

      &.style3 {
        .hot-sale-seq-tag-text-left {
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--hot-sale-seq-left-color-style3);
        }

        .hot-sale-seq-tag-class-left {
          background: var(--hot-sale-seq-left-bg-color-style3);
        }

        .hot-sale-seq-tag-text-right {
          color: var(--hot-sale-seq-right-color-style3);
        }

        .hot-sale-seq-tag-class-right {
          background: var(--hot-sale-seq-right-bg-color-style3);
          --tag-border-color: var(--hot-sale-seq-right-color-style3);
          //border: 1px solid var(--hot-sale-seq-right-color-style3);
        }
      }
    }

    &-count {
      --info-color: var(--gray-color);
      --info-font-size: var(--text-sm);
    }
  }
}
