import _ from '@wosai/emenu-mini-lodash'
import { CdnUtils } from '@wosai/emenu-mini-utils'
import BaseComponent from '@BaseComponent'

const { oss } = CdnUtils

BaseComponent({
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    goods: {
      type: Object,
      value: {}
    },
    config: {
      type: Object,
      value: {}
    },
    i18nData: {
      type: Array,
      value: []
    }
  },
  data: {
    exclamationIcon: oss('/emenu/exclamation.png', { width: 26, height: 26 }),
    goodsList: []
  },
  observers: {
    show(newVal) {
      if (newVal) {
        this.formatGoodsData()
      }
    }
  },
  methods: {
    formatGoodsData() {
      const { goods } = this.data
      const goodsCount = _.get(goods, 'item.number', 0)

      const goodsList = []
      for (let i = 0; i < goodsCount; i++) {
        const goodsItem = _.cloneDeep(goods)
        _.set(goodsItem, 'item.number', 1)
        goodsList.push(goodsItem)
      }

      this.setData({ goodsList })
      this.setTitleInfo()
    },

    setTitleInfo() {
      try {
        let { goodsList = [] } = this.data
        if (!goodsList.length) return
        let goodsName = goodsList[0].item.name
        let goodsCount = goodsList.length
        let materialsList = []

        _.forEach(goodsList, goods => {
          _.forEach(goods.materials, material => {
            let { name, number } = material
            let item = _.find(materialsList, { name })
            if (!item) {
              item = { name, number }
              materialsList.push(item)
            } else {
              item.number += number
            }
          })
        })

        let titleInfo = {
          goodsName,
          goodsCount,
          materialsList
        }

        this.setData({ titleInfo })
      } catch (error) {}
    },
    /**
     * 加料减购
     * @param {*} e index 商品列表下标  idx 商品加料下标
     */
    /**
     * 处理减少加料的操作
     * @param {WechatMiniprogram.TouchEvent} e - 触发事件的对象
     */
    onMinus(e: WechatMiniprogram.TouchEvent) {
      // 从事件对象中获取商品索引和加料索引
      const { index, idx } = _.get(e, 'currentTarget.dataset')
      const { goodsList } = this.data

      /**
       * 更新商品列表中的加料信息
       * @param {Array} list - 商品列表
       * @returns {Array} 更新后的商品列表
       */
      const updateGoodsList = (list: any[]): any[] => {
        const currentGoods = _.get(list, `${index}`, {})
        const currentGoodsMaterials = _.get(currentGoods, 'materials', [])
        const material = _.get(currentGoodsMaterials, idx)

        if (!material) return list

        // 计算新的加料数量和价格
        const newMaterialCount = _.get(material, 'number', 0) - 1
        const materialPrice = _.get(material, 'price', 0)

        // 使用 lodash 的 flow 方法链式更新商品信息
        const updatedGoods = _.flow([
          // 更新商品总价
          goods => _.update(goods, 'item.price', price => _.subtract(price, materialPrice)),
          // 更新加料数量或移除加料
          goods =>
            newMaterialCount > 0
              ? _.set(goods, `materials.${idx}.number`, newMaterialCount)
              : _.update(goods, 'materials', materials => _.filter(materials, (_, i) => i !== idx))
        ])(currentGoods)

        return _.set(list, index, updatedGoods)
      }

      // 更新商品列表
      const updatedGoodsList = updateGoodsList(goodsList)

      // 更新组件状态并重新计算标题信息
      this.setData({ goodsList: _.cloneDeep(updatedGoodsList) })
      this.setTitleInfo()

      // TODO: 考虑将追踪事件移到单独的方法中
      // this.trackEvent('SmMpFeedingPup', { operate: 'Delete' })
    },
    /**
     * 取消事件
     */
    onCancel() {
      const { sqbBridge } = this.data

      // sqbBridge.track('SmMpFeedingPup', { operate: 'Cancel' });
      this.$emit('cancel')
    },
    /**
     * 确认加购事件
     */
    onConfirm() {
      const { sqbBridge, goodsList } = this.data

      // sqbBridge.track('SmMpFeedingPup', { operate: 'Confirm' });
      this.$emit('confirm', goodsList)
    }
  }
})
