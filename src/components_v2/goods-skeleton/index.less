@keyframes skeleton-loading {
  0% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0 50%;
  }
}

.goods-skeleton {
  /* prettier-ignore */
  --_gap: var(--goods-skeleton-gap, 16PX);
  padding: var(--_gap);
  box-sizing: border-box;
  display: flex;
  background: #fff;
  border-radius: 16rpx;
  flex-direction: column;

  &__category-name {
    height: 40rpx;
    width: 200rpx;
    margin-bottom: var(--_gap);
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    animation: skeleton-loading 1.4s ease infinite;
    border-radius: 8rpx;
  }

  &__content {
    display: flex;
    flex-direction: column;
    margin-bottom: var(--_gap);
    &:last-child {
      margin-bottom: 0;
    }
  }

  &__item {
    display: flex;
  }

  &__image {
    width: var(--goods-item-image-width, 156px);
    height: var(--goods-item-image-width, 156px);
    flex-shrink: 0;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    //animation: skeleton-loading 1.4s ease infinite;
    border-radius: 12rpx;
  }

  &__info {
    flex: 1;
    margin-left: var(--_gap);
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }

  &__title {
    height: 36rpx;
    width: 70%;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    //animation: skeleton-loading 1.4s ease infinite;
    border-radius: 8rpx;
  }

  &__bottom {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  &__price {
    height: 40rpx;
    width: 120rpx;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    //animation: skeleton-loading 1.4s ease infinite;
    border-radius: 8rpx;
  }

  &__button {
    height: 48rpx;
    width: 120rpx;
    background: linear-gradient(90deg, #f2f2f2 25%, #e6e6e6 37%, #f2f2f2 63%);
    background-size: 400% 100%;
    //animation: skeleton-loading 1.4s ease infinite;
    border-radius: 24rpx;
  }
}
