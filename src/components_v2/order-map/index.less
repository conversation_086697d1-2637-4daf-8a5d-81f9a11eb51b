@import (css) '@styles/flex'; /* css */
@import (css) '@styles/common'; /* css */
.order-map-wrap {
  &.en {
    /*@minipack-ignore*/
    .order-status-wrap {
      /*@minipack-ignore*/
      flex-wrap: wrap;
      height: auto;
      min-height: 90px;

      .width75 {
        /*@minipack-ignore*/
        width: 75%;
      }
      .rider-location {
        /*@minipack-ignore*/
        width: 100%;
        order: 1;
        padding-bottom: 12px;
      }
    }
    .navigational {
      /*@minipack-ignore*/
      width: 180px !important;
    }
  }

  background: #ffffff;
  box-shadow: 0px 2px 9px 0px rgba(148, 148, 148, 0.36);
  border-radius: 11px;
  width: 100%;
  height: var(--map-height, 100%);
  position: relative;

  .order-status-wrap {
    // --cell-height: 100px;
    position: absolute;
    width: 710px;
    // height: 100px;
    padding-top: 16px;
    padding-bottom: 16px;
    // left: 0;
    // right: 0;
    // top: 178px;

    left: 50%;
    transform: translateX(-50%);
    // top: 20px;
    // padding: 0 30px;

    &::after {
      content: '';
      position: absolute;
      z-index: -1;
      width: 100%;
      height: 100%;
      left: 0;
      top: 0;
      background: rgba(255, 255, 255, 0.9);
      // box-shadow: 0px 2px 10px 0px rgba(148, 148, 148, 0.36);
      border-radius: 12px;
      // filter: blur(2px);
      backdrop-filter: blur(10px);
    }

    .img-wrap-height {
      width: 68px;
      height: 68px;
    }

    .status-text {
      // flex: initial;
      margin-left: 16px;
      font-size: 28px;
      // line-height: 36px;
      font-weight: 500;
      color: #333;
      font-family: PingFangSC, PingFang SC;
      text-align: left;
      font-style: normal;
    }

    .navigational {
      --tag-default-color: #fff;
      --tag-align-items: center;
      height: 54px;
      border-radius: 28px;
      border: 1px solid var(--theme-primary-color, #ff6a16);
      font-size: 26px;
      font-weight: 500;
      color: var(--theme-primary-color, #ff6a16);
      // right: 30px;
      // transform: rotate(360deg) translateY(-50%);
      box-sizing: border-box;
      width: 158px;

      .navigational-icon {
        --icon-color: var(--theme-primary-color, #ff6a16);
        --icon-size: 32px;
      }
    }

    .distance {
      color: var(--theme-primary-color, #ff6a16);
    }

    .rider-location {
      height: 32px;
      font-size: 24px;
      font-weight: 500;
      color: #999999;
      margin-left: 16px;
    }
  }

  // &.heytea {
  //   .status-text {
  //     color: #333;
  //   }

  //   .order-status-wrap {
  //     border-radius: 10px;
  //   }
  // }
}
