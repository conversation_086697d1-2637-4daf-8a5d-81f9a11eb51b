/**
 * 注意⚠️
 * 整个加购过程不能有await， 否则会导致加购数量的显示延迟
 */
import _ from '@wosai/emenu-mini-lodash'
import { AsyncSeriesWaterfallHook, SyncHook } from '@wosai/emenu-mini-utils'
import { Store } from './Store'
import { isEqualArray, isSameGoods, log, toAttachInfo } from './helper'
import { CartModel } from './CartModel'
import CONFIG from '@wosai/emenu-mini-config'
import { emenuChannel, showToast } from '@utils'
import {
  getQueryOpts,
  showStoredPay,
  isRoundMeal,
  checkCartVersionValid,
  isPayFirstRoundMeal
} from '@utils/helper'
import { handleCartByDiscountWithOff, updateMkCustomInfoByDiscount } from '@utils/discount'

let isInit = false
const {
  WECHAT_PAY_WAY,
  SUB_PAY_WAY,
  ROUND_MEAL,
  SUBSCRIBE_ORDER,
  EAT_FIRST_ORDER,
  PAY_FIRST_TABLE_ORDER
} = CONFIG
const STORAGE_PREFIX = Symbol('__SMART__:CART')
const WATCHER_PREFIX = Symbol('__SMART__:CART:WATCHER')
const initCartData = {
  cart: {
    client_version: 0,
    total: 0,
    total_price: 0,
    records: []
  },
  discount: { total_discount: 0 }
}
// const maxRetries = 3; // 最大重试次数
const retryDelay = 500 // 500毫秒的重试延迟
const debounceDelayForAddAndRedeem = 500 // 1秒的防抖延迟, 这个会影响本地购物车同步API的频率
const debounceDelayForAddAndRedeemRoundMeal = 0 // 0毫秒的防抖延迟，围餐商品加购
const debounceDelayForRefreshRoundMeal = 50 // 50毫秒的防抖延迟,围餐轮询刷新数据
const throttleDelayForAddToCart = 250 // 300毫秒的节流，围餐商品加购
const debounceWatch = 500
const debounceWatchRoundMeal = 0
let recordWatchTimer
let refreshRoundMealTimer

const WATCHER_TYPES = ['goods', 'record', 'recent']
// 加购未提交后端的商品
// const stagedCardRecord = new Map();
class CartStore extends Store {
  private static instance: CartStore
  public cartModel: CartModel
  private cartDataStore
  private watchersStore
  private addToCartThrottle
  // private lastSyncCart: TCart;
  // 用于不同购物车操作阶段的钩子
  hooks = {
    // 初始化购物车前的钩子，可以在此阶段进行一些预处理操作
    init: new AsyncSeriesWaterfallHook(),
    // 添加商品到购物车前的钩子，可以在此阶段对商品进行处理或验证
    addBefore: new AsyncSeriesWaterfallHook(),
    // 更新购物车记录之前， 可更新购物车记录价格等
    updateRecordBefore: new AsyncSeriesWaterfallHook(),
    // 添加商品到购物车后的钩子，可以在此阶段进行一些后续操作或处理附加信息
    add: new AsyncSeriesWaterfallHook(),
    // 保存本地购物车成功，未同步到后端前的钩子，可以在此阶段进行一些后续操作
    addSuccess: new SyncHook(),
    roundMealAddSuccess: new SyncHook(),
    // 同步购物车数据前的钩子，可以在此阶段进行一些预处理操作
    syncBefore: new AsyncSeriesWaterfallHook(),
    // 同步购物车成功后数据后的钩子，可以在此阶段进行一些后续操作
    syncSuccess: new AsyncSeriesWaterfallHook(),
    syncSuccessEnd: new SyncHook(),
    roundMealSyncSuccessEnd: new SyncHook(),
    // 同步购物车失败后数据后的钩子，可以在此阶段进行一些后续操作
    syncFail: new SyncHook(),
    clear: new SyncHook(),
    submitBefore: new AsyncSeriesWaterfallHook(),
    // 清空购物车的钩子
    reset: new SyncHook()
  }
  timer: any
  roundMealTimer: any
  roundMealCartCounter = []

  /**
   * 构造函数。
   * @param {} view - 视图对象，用于显示和更新界面。
   * @param {} data - 数据对象，用于存储和操作购物车数据。
   */
  constructor(view: unknown, data: unknown) {
    log('#constructor')
    super(view, data)
    // 创建一个新的CartModel实例，用于与后端进行购物车数据的交互
    this.cartModel = new CartModel()
    this.cartDataStore = this.global[STORAGE_PREFIX] || (this.global[STORAGE_PREFIX] = new Map())
    this.watchersStore = this.global[WATCHER_PREFIX] || (this.global[WATCHER_PREFIX] = new Map())
    this.addToCartThrottle = _.throttle(this.addToCart, throttleDelayForAddToCart, {
      leading: true,
      trailing: false
    })
    // 本地购物车加购流程
    // 后期有业务流程变更， 可以在这里修改
    this.initHooks()
  }

  // NOTE:
  // 由于购物车是全局的，所以不需要考虑多个购物车实例的情况
  // 购物车单例， view以第一个调用getInstance为准
  // 点单场景， view应该是home组件
  static getInstance(view, data): CartStore {
    if (!this.instance) {
      this.instance = new CartStore(view, data)
    } else if (view && view !== this.instance.view) {
      // 如果提供了新的视图且不同于当前视图，则更新视图引用
      // 使用setter方法替代直接访问私有属性
      this.instance.view = view
    }
    return this.instance
  }

  static destroyInstance() {
    if (this.instance) {
      this.instance.destroy()
    }
    this.instance = null as unknown as CartStore
    return null
  }

  // 清空购物车
  reset() {
    this.hooks.reset.call(initCartData)
  }

  initHooks() {
    // 当购物车服务类型更改时的事件监听器
    // this.on(CART_SERVICE_TYPE_CHANGE_EVENT_NAME, async () => await this.init());
    // 当购物车被清空时的事件监听器
    // TODO: 如果维他奶可以多次下单， 那支付完成后回到首页， 需要这样清空购物车
    // 否则商品列表的购物车数量不会消失
    this.hooks.reset.tap('cartStore:reset', cart => {
      this.runWatchers(cart.records, { init: true })
    })

    this.hooks.clear.tap('cartStore:clear', ({ cart }) => {
      log('#hooks#clear - clear call')
      // 清空购物车
      const { records } = cart
      // 将原来的记录都变成0
      this.runWatchers(records, { init: true })
      this.hooks.init.callAsync(null, (err, localCart) => {
        log('#hooks#init callAsync', { err, localCart })
      })
    })
    // 初始化购物车
    this.hooks.init.tapAsync('cartStore:init', async (opts, cb) => {
      if (isInit) return
      const { cart } = this.getData()
      log('#hooks#init - init call start 初始化购物车， 包括请求后端默认购物车数据', {
        cart,
        ...opts
      })
      // @ts-ignore
      const { cart: initCart, discount: initDiscount, initLocal = true } = opts || {}
      // 将原来的记录都变成0, 否则从订单详情返回， 会出现加购数量不为0
      // 如果从外卖切换到自取， 不需要清空购物车
      if (initLocal) {
        // this.initLocal();
        // this.runWatchers(cart.records, { init: true, initIgnore });
      }
      try {
        const data = initCart ? { cart: initCart, discount: initDiscount } : await this.init(opts)

        try {
          const { cart: newCart } = data
          const newRecordsMap = _.keyBy(newCart.records, 'id')
          const deletedRecords = _.filter(cart.records, record => !newRecordsMap[record.id])
          if (_.get(data, 'cart.records', []).length === 0 || deletedRecords.length > 0) {
            this.runWatchers(cart.records, { init: true })
          }
        } catch (e) {
          log('#hooks#init - init call start ERROR', { e })
        }

        this.hooks.syncSuccess.callAsync(data, (err, data) => {
          this.syncToStorage(data)
          const carData = this.getData()
          this.hooks.addSuccess.call(carData)
          // this.hooks.syncSuccessEnd.call(carData);
        })
        cb(null, data)
      } catch (e) {
        this.runWatchers(cart.records, { init: true })
        cb(e)
      }
    })
    // 将添加的商品转换为购物车商品
    this.hooks.addBefore.tapAsync('cartStore:addBefore', async ({ goods, from }, cb) => {
      // 否则会有负数的情况
      // this.updateTotalDiscountAsZero();

      const { item_uid } = goods
      // 根据业务需求将添加的商品进行转换，生成购物车商品数据
      log('#hooks#addBefore 1 start 可更新加入购物车的商品数据', { goods, from })
      let cartRecord = {}
      // 左边为购物车商品keys
      // 检查来源是否为购物车
      if (from === 'cart' || (from === 'input' && item_uid)) {
        // 从记录中提取必要的属性，并将它们赋值给 item 属性
        cartRecord = this.makeRecordWithItem(goods)
      } else {
        // 详情 -> 购物车 或者 列表 -> 购物车
        // 定义商品转换的键值映射
        const mapKeys = {
          id: '', // 初始化时record id应该为空
          attached_info: 'attached_info',
          category_id: 'item.category_id',
          item_id: 'item.id',
          name: 'item.name',
          url: from === 'detail' ? 'item.photo_url_list.0' : 'item.photo_url',
          out_of_stock: 'item.out_of_stock',
          sku: 'item.sku',
          sku_type: 'item.sku_type',
          status: 'item.status',
          user_icon: 'user_icon',
          user_icons: 'user_icons',
          user_id: 'user_id',
          user_name: 'user_name',
          num: 'item.num',
          item: 'item',
          attributes: 'attributes',
          spec: 'spec',
          spec_id: 'spec.id',
          materials: 'materials',
          min_sale_num: 'min_sale_num',
          open_table_must_order: 'open_table_must_order'
        }
        // 使用键值映射将商品进行转换
        const result = _.mapValues(mapKeys, value => {
          _.set(goods, 'item.num', goods.num)
          return _.get(goods, value)
        })
        // 移除值为 null 或 undefined 的属性
        cartRecord = _.omitBy(result, _.isNil)
      }
      log('#hooks#addBefore 1 end', { goods: cartRecord, from })
      cb(null, { goods: cartRecord, from })
    })
    // 拼接attach_info字段
    this.hooks.addBefore.tapAsync('cartStore:addBefore', async ({ goods, from }, cb) => {
      log('#hooks#addBefore 2 start', { goods, from })
      const newCartGoods = goods
      newCartGoods['attached_info'] = toAttachInfo(goods)
      log('#hooks#addBefore 2 end', { goods: newCartGoods, from })
      cb(null, { goods: newCartGoods, from })
    })
    // 更新购物车记录之前
    this.hooks.updateRecordBefore.tapAsync('cartStore:updateRecordBefore', async (record, cb) => {
      log('#hooks#updateRecordBefore start', { record })
      record['price'] = record['item']['price'] * record.num
      record.dirty = true
      //isRoundMeal(this.view) && this.addRoundMealCartCounter(record, num);
      log('#hooks#updateRecordBefore end', { record })
      cb(null, record)
    })
    // 成功将加购加入本地购物车
    this.hooks.addSuccess.tap('addSuccess', ({ cart, refreshAll = true }) => {
      log('#hooks#addSuccess', { cart, refreshAll })
      if (cart && refreshAll) {
        const delayTime = this.view?.isRoundMeal() ? debounceWatchRoundMeal : debounceWatch
        clearTimeout(recordWatchTimer)
        recordWatchTimer = setTimeout(() => {
          const init = cart.records.length === 0
          this.runWatchers(cart.records, { init })
        }, delayTime)
      }
    })
    // 发出去同步之前，可更新body
    this.hooks.syncBefore.tapAsync('syncBefore', async (body, cb) => {
      log('#hooks#syncBefore start 更新优惠参数', { body })
      // 增加优惠参数
      body['mk_custom_info'] = emenuChannel.getMkCustomInfo()
      log('#hooks#syncBefore end', { body })
      cb(null, body)
    })
    // 更新优惠参数信息
    this.hooks.syncSuccess.tapAsync('syncSuccess', ({ discount, cart }, cb) => {
      log('#hooks#syncSuccess start 更新购物车商品的优惠信息', { discount, cart })
      // 根据discount参数更新优惠信息
      updateMkCustomInfoByDiscount(discount)
      const { cart: newCart, discount: newDiscount } = handleCartByDiscountWithOff.call(
        this.view,
        cart,
        discount
      )
      log('#hooks#syncSuccess start', { discount: newDiscount, cart: newCart })
      cb(null, { cart: newCart, discount: newDiscount })
    })
    this.hooks.syncFail.tap('syncFail', (err: Error) => {
      log('#hooks#syncFail [ERROR]', { err })
    })
    this.hooks.submitBefore.tapAsync('cartStore:submitBefore', async (options, cb) => {
      log('#hooks#submitBefore start', { options })
      this.addAndRedeem(1, 1)
        .then(res => {
          cb(null, res)
          log('#hooks#submitBefore end success', { res })
        })
        .catch(err => {
          log('#hooks#submitBefore end error', { err })
          cb(err)
        })
    })

    this.hooks.reset.tap('reset', () => {
      this.initLocal()
    })

    this.hooks.roundMealAddSuccess.tap('roundMealAddSuccess', async data => {
      log('#hooks#roundMealAddSuccess', { data })
      // 截流
      clearTimeout(this.roundMealTimer)
      this.roundMealTimer = setTimeout(
        () => this.addAndRedeemRoundMeal(),
        debounceDelayForAddAndRedeemRoundMeal
      )
    })
  }

  watchers(type) {
    const key = this.key + type
    if (!this.watchersStore.get(key)) {
      this.watchersStore.set(key, [])
    }
    return this.watchersStore.get(key)
  }

  setWatchers(type, watchers) {
    const key = this.key + type
    if (this.watchersStore) {
      this.watchersStore.set(key, watchers)
    }
  }

  initLocal() {
    this.syncToStorage(initCartData)
  }

  /**
   * 初始化购物车。
   * 使用 init 钩子异步调用 initCartData 函数。
   * 如果出现错误，记录错误消息。
   * 否则，将新的购物车数据同步到存储中。
   */
  async init(opts = {}) {
    // const { serviceTypeName } = this;
    isInit = true
    const localCart = _.cloneDeep(initCartData)
    try {
      const { cart, redeem_result: discount = {} } = await this.getCartAndRedeem(opts)
      this.view?.isRoundMeal() && checkCartVersionValid(cart)
      _.assign(localCart, { cart, discount })
      isInit = false
    } catch (err) {
      log('#init ERROR', { err })
      isInit = false
    }
    return localCart
  }

  destroy() {
    _.forEach(WATCHER_TYPES, type => {
      this.setWatchers(type, [])
      this.setWatchers(this.key + type, [])
    })
    clearTimeout(this.timer)
    clearTimeout(recordWatchTimer)
    clearTimeout(refreshRoundMealTimer)

    // 将资源设置为 null 以彻底清理
    this.timer = null
    this.roundMealTimer = null
    this.hooks = null as any
    this.watchersStore = new Map()
    this.cartDataStore = new Map()
    this.cartModel = null as any
    this.global[STORAGE_PREFIX] = new Map()
    this.global[WATCHER_PREFIX] = new Map()

    // 清理视图引用
    this.clearViewReference()
  }

  /**
   * 拉取购物车及优惠信息
   * @param opts点单页透传
   */
  async getCartAndRedeem(opts = {}) {
    if (this.view?.isRoundMeal()) {
      const {
        interestId,
        data: { isStoredPaySelected }
      } = this.view
      const _isPayFirstRoundMeal = await isPayFirstRoundMeal(this.view)
      const _service_type_name = _isPayFirstRoundMeal ? PAY_FIRST_TABLE_ORDER : EAT_FIRST_ORDER
      const params = {
        ...(await this.commonParams()),
        ...opts,
        fill_recommend_materials: true,
        // service_type_name: serviceTypeName,
        mk_custom_info: emenuChannel.getMkCustomInfo(),
        recharge_and_pay: isStoredPaySelected,
        recharge_interest_id: interestId,
        discount_strategy: _service_type_name,
        service_type_name: _service_type_name
      }
      return this.cartModel.getRoundMealListAndRedeem(params)
    } else {
      const params = {
        ...(await this.commonParams()),
        fill_recommend_materials: true,
        // service_type_name: serviceTypeName,
        mk_custom_info: emenuChannel.getMkCustomInfo(),
        ...opts
      }
      // 通过CartModel获取购物车数据
      return this.cartModel.getCartAndRedeem(params)
    }
  }

  onAddToCart(event, addNum = 1) {
    this.view?.isRoundMeal() ? this.addToCartThrottle(event, addNum) : this.addToCart(event, addNum)
  }

  /**
   * 处理添加商品到购物车的事件。
   *
   * @param event - 包含商品详情的事件对象。
   * @param addNum
   */
  addToCart(event, addNum = 1) {
    let _addNum = addNum
    log('#onAddToCart start', { event, _addNum })
    let { detail: goods } = event
    // 如果是批量添加商品到购物车
    goods = goods || {}
    const { num, spu_type, id: item_id } = goods
    const from = _.get(goods, '__source')

    const bridge = _.get(this.view, 'data.sqbBridge')
    if (bridge) {
      bridge.sls('INFO', {
        type: 'cartStore:onAddToCart',
        data: { data: { goods, _addNum, from } }
      })
    }
    // 这个会阻碍加购数量的显示
    // 获取用户信息
    // console.log('Getting user info');
    // await aliAuthGetUserInfo(this.view);

    // 套餐
    if (!num) {
      // 如果是套餐商品，跳转到套餐页面
      if (spu_type === 'PACKAGE') {
        log('#onAddToCart start 套餐跳转')
        return this.view.linkToCombo(item_id)
      }
    }

    // 详情会有最少购买数量
    if (from === 'detail') {
      log('#onAddToCart start 详情加购')
      _addNum = _.get(goods, 'item.number') || 1
      // 商品多个加料, 如同个商品加2个， 不同的加料
      if (_.isArray(goods)) {
        _.forEach(goods, goodsItem => {
          const _num = _.get(goodsItem, 'item.number')
          this.add(goodsItem, _num, from)
        })
        return
      }
    }
    // 商品列表起售加购
    if ((from === 'list' || from === 'cart') && _.has(goods, 'min_sale_num')) {
      log('#onAddToCart start 列表加购(list or cart)', { from, goods })
      if (addNum > 0) {
        _addNum = this.getAddNum(goods)
      } else {
        _addNum = this.getMinusNum(goods)
      }
    }
    // 添加商品到购物车
    log('#onAddToCart end)', { goods, _addNum, from })
    this.add(goods, _addNum, from)
  }

  /**
   * 向购物车中添加商品。
   *
   * @param {TGoods} goods - 要添加到购物车的商品。
   * @param {number} num - 要添加的商品数量。
   * @param {TAddAndRedeemFrom} from - 商品添加的来源。
   * @param {boolean} rollback - 围餐回滚数据
   */
  add(goods = {}, num: number, from, rollback = false) {
    log('#add start', { goods, num, from })
    this.hooks.addBefore.callAsync({ goods, from }, (err: Error, { goods: newGoods }) => {
      log('#hooks#addBefore result', { goods, from, newGoods, err })
      if (err) log('#hooks#addBefore result ERROR', { err })

      const cartData = this.getData()
      const cart = cartData.cart
      const { cartGoods, index } = this.findLocalRecordByGoods(newGoods)
      const _isRoundMeal = this.view?.isRoundMeal() && !rollback

      // 加购
      if (num >= 0) {
        // 购物车不存在商品
        if (!cartGoods) {
          newGoods['num'] = num
          this.hooks.updateRecordBefore.callAsync(newGoods, (err: Error, newRecord, from) => {
            if (err) log('#hooks#updateRecordBefore result ERROR', { err, from, newRecord })
            cart.records.unshift(newRecord)
            // TODO:  是否可以放到updateRecordBefore中
            _isRoundMeal && this.addRoundMealCartCounter(newRecord, num)
          })
        } else {
          // 购物车长按输入0
          if (num === 0) {
            cartGoods.num = 0
          } else {
            if (from === 'input') {
              cartGoods.num = num
            } else {
              cartGoods.num += num
            }
          }
          this.hooks.updateRecordBefore.callAsync(cartGoods, (err: Error, newRecord, from) => {
            if (err) {
              log('#hooks#updateRecordBefore result ERROR', { err, from, newRecord })
              return
            }
            if (!newRecord) {
              log('#hooks#updateRecordBefore result newRecord not exist ERROR', {
                err,
                from,
                newRecord
              })
              return
            }
            cart.records[index] = newRecord
            // TODO:  是否可以放到updateRecordBefore中
            _isRoundMeal && this.addRoundMealCartCounter(newRecord, num)
          })
        }
      }
      // 减购
      if (num < 0) {
        if (!cartGoods) return
        if (cartGoods.num > 0) {
          cartGoods.num += num
        }
        this.hooks.updateRecordBefore.callAsync(cartGoods, (err, newRecord) => {
          if (err) {
            log('#hooks#updateRecordBefore result newRecord not exist ERROR', {
              err,
              from,
              newRecord
            })
          }
          cart.records[index] = newRecord
          // TODO:  是否可以放到updateRecordBefore中
          _isRoundMeal && this.addRoundMealCartCounter(newRecord, num)
        })
      }

      this.hooks.add.callAsync(cartData, (err, newCartData) => {
        log('#hooks#add result newRecord not exist ERROR', { err, cartData, newCartData })
        // 计算价格
        const _newCartData = this.calcTotalPrice(newCartData)
        // 本地购物车如果有变化，设置优惠为空， 否则会出现总价为负数的情况
        // const discount = {};
        // 同步到本地
        const data = { record: cartGoods || newGoods, from }
        _newCartData['cart'] = cart
        this.syncToStorage(_newCartData)
        //围餐-加购时分类和购物车二次刷新逻辑
        // this.hooks.addSuccess.call({ ...data, ..._newCartData, refreshAll: _isRoundMeal ? false : true });

        if (this.view?.isRoundMeal()) {
          if (rollback) return _newCartData

          this.hooks.roundMealAddSuccess.call({ ...data, ..._newCartData })
        } else {
          log('#addSuccess', { data, _newCartData })
          this.hooks.addSuccess.call({ ...data, ..._newCartData })
          clearTimeout(this.timer)
          this.timer = setTimeout(() => this.addAndRedeem(), debounceDelayForAddAndRedeem)
        }

        return _newCartData
      })
    })
  }

  /**
   * 围餐场景-尝试将购物车中的商品加入累加器中。
   * @param {TGoods} goods - 要添加到购物车的商品。
   * @param {number} num - 要增加/减少的商品数量。
   */
  async addRoundMealCartCounter(goods, num) {
    const _goods = _.find(this.roundMealCartCounter, item => isSameGoods(item, goods))
    if (_goods) {
      _goods.num += num
    } else {
      const newGoods = _.cloneDeep(goods)
      newGoods.num = num
      this.roundMealCartCounter.push(newGoods)
    }
  }

  /**
   * 围餐场景-尝试将购物车中的商品加入远程购物车，失败回滚本地购物车。
   */
  async addAndRedeemRoundMeal(): Promise<any> {
    return new Promise(async (resolve, reject) => {
      if (!this.roundMealCartCounter) return

      const _cloneRecords = _.cloneDeep(this.roundMealCartCounter)
      const bodyItems = await Promise.all(
        this.roundMealCartCounter.map(record => this.toBody(record))
      )
      this.roundMealCartCounter = []
      const body = { items: bodyItems }

      this.hooks.syncBefore.callAsync(body, async (err: Error, newBody) => {
        if (err) return reject(err)
        try {
          const { cart: remoteCart, redeem_result: discount } =
            await this.cartModel.addAndRedeemBatch(newBody)
          if (!checkCartVersionValid(remoteCart)) return
          this.handleRoundMealSuccess(remoteCart, discount, resolve)
        } catch (err) {
          const matched = (err.message || '').match(/剩\D?(\d+)/)
          matched && showToast(err.message)
          _cloneRecords.map(item => {
            this.add(item, -item.num, 'input', true)
          })
          return reject(err)
        }
      })
    })
  }

  /**
   * 围餐场景-将远程购物车数据同步到本地购物车，并调用成功钩子函数。
   *
   * @param {object} remoteCart - 远程购物车数据。
   * @param {object} discount - 折扣数据。
   * @param {Function} resolve - Promise的resolve函数，用于在Promise内部解析结果。
   */
  private handleRoundMealSuccess(remoteCart, discount, resolve) {
    const remoteRecords = remoteCart.records.map(record =>
      this.transformGoodsToRecord(record, 'cart')
    )
    remoteCart.records = remoteRecords
    //  如果购物车中没有的商品，则设置数量为0
    const _localRecords = _.cloneDeep(
      _.uniqBy(_.get(this.getData(), 'cart.records') || [], 'item_id')
    )
    const notExistRecords = _.filter(_localRecords, item => {
      return (
        _.findIndex(
          remoteRecords,
          remoteItem => _.get(item, 'item_id') == _.get(remoteItem, 'item_id')
        ) < 0
      )
    })
    !_.isEmpty(notExistRecords) && this.runWatchers(notExistRecords, { init: true })

    log('#handleRoundMealSuccess start', { remoteCart, discount })
    this.syncCart(remoteCart, discount, null, resolve)
    this.hooks.roundMealSyncSuccessEnd.call({})
  }

  /**
   * 围餐场景-轮询将程购物车数据同步到本地购物车，并调用成功钩子函数。
   */
  async refreshRoundMealData() {
    clearTimeout(refreshRoundMealTimer)
    refreshRoundMealTimer = setTimeout(async () => {
      const { cart, redeem_result: discount = {} } = await this.getCartAndRedeem()
      log('#refreshRoundMealData', { cart, discount })
      if (!checkCartVersionValid(cart)) return
      this.handleRoundMealSuccess(cart, discount, null)
    }, debounceDelayForRefreshRoundMeal)
  }

  /**
   * 尝试将购物车中的商品加入远程购物车，如果失败则进行重试。
   *
   * @async
   * @param {number} [retry=1] - 当前的重试次数，默认为1。
   * @param {number} [maxRetries=3] - 最大的重试次数，默认为3。
   * @returns {Promise} - 如果成功，Promise 将解析为添加并兑换商品的响应。如果失败，Promise 将被拒绝。
   */
  async addAndRedeem(retry = 1, maxRetries = 3): Promise<any> {
    const cartData = this.getData()
    const localRecords = this.getDirtyRecords(cartData.cart)
    if (!localRecords.length) return

    const bodyItems = await Promise.all(localRecords.map(record => this.toBody(record)))
    const body = { items: bodyItems }

    return new Promise((resolve, reject) => {
      this.hooks.syncBefore.callAsync(body, async (err: Error, newBody) => {
        if (err) {
          this.handleError(err, retry, maxRetries, reject)
          return
        }

        try {
          const { cart: remoteCart, redeem_result: discount } =
            await this.cartModel.addAndRedeemBatch(newBody)
          // console.log('cartStore:addAndRedeem', { body, newBody, remoteCart, discount });
          this.handleSuccess(remoteCart, discount, resolve)
        } catch (err) {
          this.handleError(err, retry, maxRetries, reject)
        }
      })
    })
  }

  /**
   * 处理购物车同步成功的情况。
   *
   * @param {object} remoteCart - 远程购物车数据。
   * @param {object} discount - 折扣数据。
   * @param {Function} resolve - Promise的resolve函数，用于在Promise内部解析结果。
   */
  private handleSuccess(remoteCart, discount, resolve) {
    const localCart = _.cloneDeep(this.getData().cart)
    const remoteRecords = remoteCart.records.map(record =>
      this.transformGoodsToRecord(record, 'cart')
    )
    remoteCart.records = remoteRecords

    log('#handleSuccess start', { remoteCart, discount, localCart })

    if (isEqualArray(remoteRecords, localCart.records)) {
      this.syncCart(remoteCart, discount, localCart, resolve)
    }
    // else if (_.isEmpty(remoteCart.records)) {
    //   this.syncCart(remoteCart, discount, null, resolve);
    // }
    else {
      log('#handleSuccess ERROR', { remoteCart, discount, localCart })
      throw Error('购物车数据不一致')
    }
  }

  /**
   * 将远程购物车数据同步到本地购物车，并调用成功钩子函数。
   *
   * @param {object} remoteCart - 远程购物车数据。
   * @param {object} discount - 折扣数据。
   * @param {object} localCart - 本地购物车数据。
   * @param {Function} resolve - Promise的resolve函数，用于在Promise内部解析结果。
   */
  private syncCart(remoteCart, discount, localCart, resolve) {
    const cartData = { cart: remoteCart, discount }
    this.hooks.syncSuccess.callAsync({ ...cartData, localCart }, (err, { discount, cart }) => {
      this.syncToStorage({ discount, cart })
      // this.hooks.syncSuccessEnd.call({ discount, cart });
      this.hooks.addSuccess.call({ discount, cart })
      resolve && resolve({ cart, discount })
    })
  }

  /**
   * 尝试将购物车中的商品添加并兑换，如果失败则进行重试。
   *
   * @async
   * @param err
   * @param {number} [retry=1] - 当前的重试次数，默认为1。
   * @param {number} [maxRetries=3] - 最大的重试次数，默认为3。
   * @param reject
   * @returns {Promise} - 如果成功，Promise 将解析为添加并兑换商品的响应。如果失败，Promise 将被拒绝。
   */
  private handleError(err, retry, maxRetries, reject) {
    const { cart, discount } = this.getData()
    this.hooks.syncFail.call({ error: err, cart, discount })
    reject({ error: err })
    if (retry < maxRetries) {
      setTimeout(() => this.addAndRedeem(retry + 1).catch(reject), retryDelay)
    } else {
      return Promise.reject(err)
    }
  }

  /**
   * 清空购物车。
   */
  clear() {
    // 清空本地购物车
    this.hooks.clear.call({ ...this.getData() })
  }

  /**
   * 从存储中获取数据，如果没有则使用默认数据。
   * @returns 包含获取到的数据的对象。
   */
  getData() {
    // 检查是否定义了键
    if (!this.key) {
      // @ts-ignore
      return { cart: {}, discount: {} }
    }
    // 从存储中获取数据，如果没有则使用默认数据
    // @ts-ignore
    // let data = storage(this.key) || __CART_DATA__[this.key];
    let data = this.cartDataStore.get(this.key)
    // 如果数据不可用，初始化本地存储并再次获取数据
    if (!data) {
      this.initLocal()
      // @ts-ignore
      // data = storage(this.key) || __CART_DATA__[this.key];
      // data = __CART_DATA__[this.key];
      data = this.cartDataStore.get(this.key)
    }

    return {
      ...data
    }
  }

  // name 可能是购物车商品和商品列表
  watch(type, goods, fields: string[] = [], cb, scene) {
    if (!goods) return
    // @ts-ignore
    // 组件刚刚初始化时， 会触发watch
    const addedNum = this.calcNumByGoods(goods, type)

    if (fields.includes('num')) {
      log(`#watch [${type}] start ${goods.name}}`, { addedNum, goods })
      // 页面滚动时才能会有红点
      cb(null, addedNum)
    }

    const watchers = this.watchers(type)
    watchers.push({ goods, fields, cb, scene })
    this.setWatchers(type, watchers)
  }

  unWatch(cb, scene) {
    _.forEach(WATCHER_TYPES, type => {
      const watchers = this.watchers(type)
      const rejectedWatchers = _.reject(watchers, watcher => watcher.scene === scene)
      this.setWatchers(type, rejectedWatchers)
    })
  }

  runWatchers(records, opts = {}) {
    //@ts-ignore
    const { init = false, initIgnore = [] } = opts
    // @ts-ignore
    log('#runWatchers start', {
      records,
      init,
      serviceType: this.serviceType,
      opts,
      watchers: this.watchers('goods')
    })
    _.forEach(WATCHER_TYPES, type => {
      if (init) {
        if (initIgnore.includes(type)) return
        const watchers = this.watchers(type)
        _.forEach(watchers, ({ cb, fields, goods }) => {
          log(`#watch start [${type}] ${goods.name} - 0`, { goods, fields, type, init })
          cb(null, 0)
        })
      } else {
        _.forEach(records, record => {
          const filteredWatchers = this.queryWatchers(record, type)
          _.forEach(filteredWatchers, ({ cb, fields, goods }) => {
            // 套餐或者多规格需要累加
            const recordNum = init ? 0 : this.calcNumByGoods(goods, type)
            log(`#watch start [${type}] ${goods.name} - ${recordNum}`, {
              goods,
              recordNum,
              fields,
              type
            })
            // @ts-ignore
            cb(null, recordNum)
          })
        })
      }
    })
  }

  /**
   * 返回购物车在存储中的键。
   * 该键基于storeId和serviceType生成。
   * @returns 购物车在存储中的键。
   */
  private get key() {
    // 如果storeId未定义，返回false
    if (!this.storeId) return ''
    // 根据storeId和serviceType生成键

    return `${this.storeId}:${this.serviceType}`
  }

  /**
   * 将购物车数据同步到存储中。
   * @param cartData
   */
  private syncToStorage(cartData) {
    log('#syncToStorage start', { cartData })
    const { cart } = cartData
    // @ts-ignore
    if (cart.records.length > 0) {
      // 如果record没有item， 则构造item
      // 如果购物车记录为空，则创建新的购物车记录
      cart.records = this.createCartRecords(cart.records)
    }
    // 扫一扫其他店铺时，会不更新优惠
    if (!cartData.discount) {
      cartData.discount = {
        total_discount: 0
      }
    }
    // 将购物车对象存储在存储中
    // storage(`${this.key}`, cart);
    // @ts-ignore
    // __CART_DATA__[this.key] = cartData;
    this.cartDataStore.set(this.key, cartData)
    log('#syncToStorage end', { cartData, key: this.key })
    // this.triggerCartChangedEvent(cartData);
  }

  /**
   * 将给定的记录更新为包含商品详情。
   * @param record - 需要更新的记录。
   * @returns 更新后的记录。
   */
  makeRecordWithItem(record) {
    // 从记录中提取必要的属性，并将它们赋值给 item 属性
    record.item = _.pick(record, ['name', 'price', 'category_id', 'item_tag', 'photo_url'])
    // 通过将总价除以数量来计算每个商品的单价
    if (!this.view?.isRoundMeal()) record.item['price'] = record.price / record.num

    // 购物车的item_id是商品的id
    record.item['id'] = record.item_id
    // 如果来源为购物车，则设置购物车商品的item_uid为购物车记录的id， 后端需要依赖
    record['item_uid'] = record.id

    return record
  }

  private createCartRecords(records) {
    return records.map(record => {
      if (!record.item) {
        return this.makeRecordWithItem(record)
      }
      return record
    })
  }

  /**
   * 将商品转为购物车商品。
   *
   * @param goods - 需要转换的商品。
   * @param from - 商品的来源。
   * @returns 转换后的购物车商品。
   */
  // @ts-ignore
  transformGoodsToRecord(goods, from) {
    // 左边为购物车商品keys
    let mapKeys
    // 检查来源是否为购物车
    if (from === 'cart') {
      return this.makeRecordWithItem(goods)
    } else {
      // 定义商品转换的键值映射
      mapKeys = {
        id: '', // 初始化时record id应该为空
        attached_info: 'attached_info',
        category_id: 'item.category_id',
        item_id: 'item.id',
        name: 'item.name',
        url: from === 'detail' ? 'item.photo_url_list.0' : 'item.photo_url',
        out_of_stock: 'item.out_of_stock',
        sku: 'item.sku',
        sku_type: 'item.sku_type',
        status: 'item.status',
        user_icon: 'user_icon',
        user_icons: 'user_icons',
        user_id: 'user_id',
        user_name: 'user_name',
        num: 'item.num',
        item: 'item',
        attributes: 'attributes',
        spec: 'spec',
        spec_id: 'spec.id',
        materials: 'materials',
        open_table_must_order: 'open_table_must_order'
      }
    }
    // 使用键值映射将商品进行转换
    const result = _.mapValues(mapKeys, value => {
      // @ts-ignore
      _.set(goods, 'item.num', goods.num)
      return _.get(goods, value)
    })
    // 移除值为 null 或 undefined 的属性
    return _.omitBy(result, _.isNil)
  }

  /**
   * 根据给定的商品和类型，计算购物车中指定商品的总数量。
   * 这里会影响到购物车、商品列表的数量显示
   * @param {object} goods - 要计算数量的商品项。
   * @param {string} [type='goods'] - 商品的类型。可以是 'goods' 或 'record'。默认为 'goods'。
   * @returns {number} 购物车中指定商品的总数量。
   */
  calcNumByGoods(goods: object, type = 'goods'): number {
    const cart = this.getData().cart
    const records = cart.records
    if (!records || !records.length) return 0
    // 商品列表显示的数量需要累加item_id相同的
    if (type === 'goods' || type === 'recent') {
      return _.sumBy(
        // 开台必点商品不计入商品列表的购买数量
        // 加价购不计入数量
        _.filter(
          records,
          record =>
            // @ts-ignore
            (record.item_id === goods.id || record.item_id === goods.item_id) &&
            !record.open_table_must_order &&
            !record.brand_act
        ),
        'num'
      )
    }
    if (type === 'record') {
      const { cartGoods } = this.findLocalRecordByGoods(goods)
      return cartGoods ? cartGoods.num : 0
    }
    return 0
  }

  queryWatchers(record, type) {
    return _.uniqBy(
      _.filter(this.watchers(type), ({ goods }) => {
        if (type === 'goods' || type === 'recent') {
          return goods.id === record.item_id
        }
        if (type === 'record') {
          return isSameGoods(record, goods)
        }
      }),
      'cb'
    )
  }

  /**
   * 将本地记录转换为请求体
   * @param localRecord 本地记录对象
   * @returns 请求体对象
   */
  async toBody(localRecord) {
    const { serviceTypeName: service_type_name, supportCardPay } = this
    const takeout_profit_sharing = await this.takeoutProfitSharing()
    // 获取围餐和优惠策略相关信息
    const _isRoundMeal = this.view?.isRoundMeal()
    const discount_strategy = _isRoundMeal ? EAT_FIRST_ORDER : service_type_name
    // 构建bodyRecord对象
    const bodyRecord = this.buildBodyGoods(localRecord)
    // const { num: number, user_icon, user_name } = localRecord;
    const { num: number } = localRecord
    let { user_icon, user_name } = localRecord

    const bridge = _.get(this.view, 'data.sqbBridge')
    if (_isRoundMeal && bridge) {
      const { avatarUrl, nickName } = await bridge.getMiniProgramUser()
      user_icon = avatarUrl
      user_name = nickName
    }

    _.set(bodyRecord, 'item.number', number)
    // 构建请求体对象
    const body = {
      client_version: Date.now()
    }

    return _.assign(
      body,
      _.omitBy(
        {
          ...(await this.commonParams()),
          cover: true, // 请求购物车时，这个参数会覆盖原来购物车的值
          compatible: true,
          meal_type: _isRoundMeal ? 'ROUND_MEAL' : 'SINGLE',
          mk_custom_info: emenuChannel.getMkCustomInfo(),
          // from_cart 为 true 时表示拉取最低优惠（显示储值支付时）
          from_cart: showStoredPay(
            service_type_name,
            takeout_profit_sharing,
            _isRoundMeal,
            supportCardPay
          ),
          service_type_name,
          discount_strategy,
          user_icon,
          user_name,
          number,
          ...bodyRecord
        },
        _.isNil
      )
    )
  }

  async commonParams() {
    const {
      storeId: store_id,
      serviceType: service_type,
      serviceTypeName: service_type_name,
      tableId,
      merchantId: merchant_id,
      terminalSn: terminal_sn,
      // order_sn
      supportCardPay
    } = this
    const takeout_profit_sharing = await this.takeoutProfitSharing()
    // 获取table_id
    const table_id = tableId || _.get(this.view.getQueryOpts(), 'tableId')
    const result = {
      terminal_sn,
      payway: WECHAT_PAY_WAY,
      sub_payway: SUB_PAY_WAY,
      store_id,
      table_id,
      merchant_id,
      service_type,
      discount_strategy: service_type_name,
      service_type_name,
      from_cart: showStoredPay(
        service_type_name,
        takeout_profit_sharing,
        // isRoundMeal(this.view),
        this.view?.isRoundMeal(),
        supportCardPay
      )
    }
    return _.omitBy(result, _.isNil)
  }

  /**
   * 根据购物车记录构建商品的主体部分。
   * @param record - 购物车记录。
   * @returns 构建好的商品主体。
   */
  private buildBodyGoods(record) {
    // 从记录中解构出属性
    const {
      num,
      item,
      spec = null,
      attributes = null,
      materials = null,
      quota_count,
      item_uid
    } = record
    // 使用必需的属性初始化结果对象
    const result = { number: num, quota_count, item }
    // 原来购物车id
    if (item_uid) {
      result['item_uid'] = item_uid
    }
    // 如果属性不为空，则将其添加到结果中
    if (!_.isEmpty(attributes)) {
      _.assign(result, {
        attributes: attributes.map(attribute => _.pick(attribute, ['id', 'name']))
      })
    }
    // 如果加料不为空，则将其添加到结果中
    if (!_.isEmpty(materials)) {
      _.assign(result, {
        materials: materials.map(material =>
          _.pick(material, ['id', 'name', 'price', 'number', 'source'])
        )
      })
    }
    // 如果规格不为空，则将其添加到结果中
    if (!_.isEmpty(spec)) {
      _.assign(result, { spec: _.pick(spec, ['id', 'name', 'price']) })
    }

    return result
  }

  /**
   * 计算购物车中所有项目的总价和总数量。
   * @param cartData - 购物车数据对象。
   * @returns 更新后的购物车数据对象，包含计算后的值。
   */
  private calcTotalPrice(cartData) {
    const cart = cartData.cart
    // 通过将每个项目的数量与其价格相乘来计算总价
    cart['total_price'] = _.round(
      _.sumBy(cart.records, ({ item, num }) => num * item.price),
      2
    )

    // 通过对所有项目的数量求和来计算总数量
    cart['total'] = _.sumBy(cart.records, 'num')

    return { ...cartData, cart }
  }

  getDirtyRecords(cartData) {
    if (!cartData) return
    // return _.filter(cartData.records, { dirty: true });
    return cartData.records
  }

  /**
   通过商品信息查找本地购物车记录
   包含以下信息
   id: string; // 商品id
   spec_id: string; // 规格id
   material_id: string; // 加料id
   attribute_id: string; // 属性id
   @param goods - 要在本地购物车记录中搜索的商品信息
   @returns 包含找到的购物车商品及其在记录中的索引的对象。
   */
  findLocalRecordByGoods(goods) {
    // TODO:
    // 后端增加加料
    const { records } = this.getData().cart
    const cartGoods = _.find(records, record => isSameGoods(record, goods))
    const index = _.findIndex(records, record => isSameGoods(record, goods))

    return { cartGoods, index }
  }

  getAddNum(goods) {
    const { min_sale_num } = goods
    if (min_sale_num) {
      const { cartGoods } = this.findLocalRecordByGoods(goods)

      if (cartGoods && cartGoods.num >= min_sale_num) {
        return 1
      } else {
        return min_sale_num
      }
    }
    return 1
  }

  // 获取减购数量
  getMinusNum(goods) {
    const { cartGoods } = this.findLocalRecordByGoods(goods)
    const { min_sale_num } = goods

    if (min_sale_num && cartGoods) {
      if (cartGoods.num > min_sale_num) {
        return -1
      } else if (cartGoods.num === min_sale_num) {
        return -min_sale_num
      } else {
        return 0
      }
    }

    return -1
  }

  updateTotalDiscountAsZero() {
    log('#updateTotalDiscountAsZero start')
    const cartData = this.getData()
    _.set(cartData, 'discount.total_discount', 0)
    this.syncToStorage(cartData)
  }
}

export { CartStore }
