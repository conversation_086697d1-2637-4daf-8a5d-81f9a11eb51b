import { Store } from './Store'
import _ from '@wosai/emenu-mini-lodash'
import { md5 } from '@wosai/emenu-mini-md5'
import { isWeixin } from '@wosai/emenu-mini-utils'
import { calcRemainNumAndOutOfStock, transformGoods, getStatusBarHeight } from '@utils/helper'
import { GoodsModel } from './GoodsModel'
import {
  CATEGORY_NAME_HEIGHT,
  GOODS_ITEM_HEIGHT,
  GOODS_ITEM_MARGIN_BOTTOM,
  GRID_CATEGORY_NAME_HEIGHT,
  GRID_GOODS_ITEM_HEIGHT,
  GRID_GOODS_ITEM_MARGIN_BOTTOM,
  RECENT_CAT_ID,
  RECENT_GOODS_ITEM_HEIGHT,
  RECOMMEND_CAT_ID,
  SPECIAL_CAT_IDS,
  VIEWPORT_OFFSET,
  LARGE_GOODS_ITEM_HEIGHT,
  LARGE_GOODS_ITEM_MARGIN_BOTTOM
} from './constants'

type TCategory = {
  offset?: number[]
  isSamePrev?: boolean
  data?: any[]
  item_count: number
}

const HEADER_BAR_HEIGHT = getStatusBarHeight()
const BUILD_PAGES_LAYOUT_DEBUG = false
export const IS_INCLUDES_SPECIAL = (id: string) =>
  _.some(SPECIAL_CAT_IDS, specialId => id && id.indexOf && id.indexOf(specialId) !== -1) // 商品列表滚动

const BASE_END = 170
// const STORE_DISTANCE_TO_TOP = STATUS_BAR_HEIGHT + CAPSULE_HEIGHT + 22; //门店位置距离顶部距离
export const systemInfo = wx.getSystemInfoSync()
const height = systemInfo.windowHeight
const BOTTOM_HEIGHT = 94
export const PAGE_SIZE = 30
// 布局显示几列
const rpxToPx = (rpx: number) => {
  const designWidth = 750
  const eps = 1e-4
  const deviceWidth = _.get(systemInfo, 'windowWidth', 375)
  // const deviceDPR = _.get(systemInfo, 'pixelRatio',  2);

  return Math.floor((rpx / designWidth) * deviceWidth + eps)
}
const GOODS_DISPLAY_MODE_COLUMN_MAP = {
  list: 1,
  grid: 2,
  largeImageList: 3
}
export const getGridGoodsItemHeight = () => {
  return rpxToPx(GRID_GOODS_ITEM_HEIGHT)
}
// 商品高度
const calcGoodsItemHeight = (catId: string, mode: 'list' | 'grid' | 'large-list') => {
  if (catId && catId === RECENT_CAT_ID && mode !== 'grid') {
    return RECENT_GOODS_ITEM_HEIGHT
  }

  if (mode === 'grid') {
    return getGridGoodsItemHeight() + GRID_GOODS_ITEM_MARGIN_BOTTOM
  }
  if (mode === 'large-list') {
    return LARGE_GOODS_ITEM_HEIGHT + LARGE_GOODS_ITEM_MARGIN_BOTTOM
  }
  return GOODS_ITEM_HEIGHT + GOODS_ITEM_MARGIN_BOTTOM
}
/**
 * 计算商品滚动页面高度
 * @param categories
 * @param size
 * @returns {*[]}
 */

/**
 *  页面高度
 * @param prevPage
 * @param currentCatId
 * @param itemCount
 * @param mode
 * @returns {number}
 */
const getHeight = (prevPage, currentCatId, itemCount, mode) => {
  let result
  const prevPageCategories = _.get(prevPage, 'categories')
  if (mode === 'list' || mode === 'large-list') {
    if (_.find(prevPageCategories, { id: currentCatId })) {
      result = itemCount * calcGoodsItemHeight(currentCatId, mode)
    } else {
      result = itemCount * calcGoodsItemHeight(currentCatId, mode) + CATEGORY_NAME_HEIGHT
    }
  } else if (mode === 'grid') {
    const itemCountChunks = _.ceil(itemCount / GOODS_DISPLAY_MODE_COLUMN_MAP[mode])
    if (_.find(prevPageCategories, { id: currentCatId })) {
      result = itemCountChunks * calcGoodsItemHeight(currentCatId, mode)
    } else {
      result = itemCountChunks * calcGoodsItemHeight(currentCatId, mode) + GRID_CATEGORY_NAME_HEIGHT
    }
  }
  return _.floor(result)
}

const getPageSize = (sqbBridge, size) => {
  let remotePageSize = sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE')
  if (_.isObject(remotePageSize)) {
    remotePageSize = false
  }
  return size || remotePageSize || PAGE_SIZE
}

/**
 * 页面的offset
 * @param prevOffset
 * @param height
 * @returns {(*)[]}
 */
const getOffset = (prevOffset, height) => {
  return [prevOffset[1], prevOffset[1] + height]
}

/**
 * 页面的page字段信息
 * @param _prevPageIdx
 * @returns {*}
 */
const getPageIdx = (_prevPageIdx: number): any => _prevPageIdx + 2

/**
 * 创建page的分类信息
 * @param category
 * @param key
 * @param _item_count
 * @param categoryOffset
 * @param isSamePrev
 * @returns {{item_count, isSamePrev: boolean, data: *[], offset, name, id, key, height: number}}
 */
const buildCategory = (category, key, _item_count, categoryOffset, isSamePrev = false) => {
  const { name, id, category_id = id } = category

  // const data = _.get(category, 'items', []);
  return {
    id: category_id,
    isSamePrev,
    name,
    // data: _.map(data, (item) => transformGoods(item, serviceType)),
    data: [],
    key,
    item_count: _item_count,
    offset: categoryOffset,
    height: categoryOffset[1] - categoryOffset[0]
  }
}

/**
 * 当前分页的分类和前一分页的分类是否相同
 * @param _prevPage
 * @param category_id
 * @returns {boolean}
 */
const isSamePrev = (_prevPage, category_id) => {
  let samePrev = false
  const prevPageCategories = _.get(_prevPage, 'categories', [])
  if (_.get(_.last(prevPageCategories), 'id') === category_id) {
    samePrev = true
  }
  return samePrev
}

/**
 * 计算当前分类的偏移量
 * @param _prevPageOffset
 * @param height
 * @param _page
 * @returns {(*)[]}
 */
const calcCategoryOffset = (_prevPageOffset, height, _page) => {
  let categoryOffset = [_prevPageOffset[1], _prevPageOffset[1] + height]
  const lastCatOffsetEnd = _.get(_.last(_page['categories']), 'offset.1')

  if (lastCatOffsetEnd) {
    categoryOffset = [lastCatOffsetEnd, lastCatOffsetEnd + height]
  }
  return categoryOffset
}

/**
 * 计算前一分页的偏移量
 * @param prevPage
 * @returns {*}
 */
const getPrevPageOffset = prevPage => _.get(prevPage, 'offset', [0, BASE_END])

const getPageAllItemCount = page => {
  if (!page) return 0
  return _.sumBy(page.categories, 'item_count')
}

function getPageHeight(page, item_count, category_id, mode) {
  if (!page) return 0
  const categories = page.categories
  if (mode === 'grid') {
    return _.sumBy(page.categories, 'height')
  }
  return (
    _.reject(categories, { isSamePrev: true }).length * CATEGORY_NAME_HEIGHT +
    item_count * calcGoodsItemHeight(category_id, mode)
  )
}

/**
 * 创建新分页
 * @param _prevPage
 * @param _category
 * @param _item_count
 * @param _prevPageIdx
 * @param pageSize
 * @param mode
 * @returns {{item_count: number, offset: *[], page: *, categories: *[], height: number}}
 */
const createPageItem = (_prevPage, _category, _item_count, _prevPageIdx, pageSize, mode) => {
  const { id, category_id = id } = _category
  const pageIndex = getPageIdx(_prevPageIdx)
  const _page = {
    page: pageIndex,
    height: 0,
    offset: [],
    categories: [],
    item_count: 0
  }

  const _prevPageOffset = _.get(_prevPage, 'offset', [0, BASE_END])
  // 计算分类高度
  const key = `id-${category_id}-${getPageIdx(_prevPageIdx)}`
  const _height = getHeight(_prevPage, category_id, _item_count, mode)
  const categoryOffset = calcCategoryOffset(_prevPageOffset, _height, _page)

  _page['categories'].push(
    buildCategory(_category, key, _item_count, categoryOffset, isSamePrev(_prevPage, category_id))
  )

  // const height =
  //     _.reject(_page['categories'], { isSamePrev: true }).length * CATEGORY_NAME_HEIGHT +
  //     _item_count * calcGoodsItemHeight(category_id);
  const pageIncreaseCount = (_prevPage && _prevPage['pageIncreaseCount']) || 0
  const height = getPageHeight(_page, _item_count, category_id, mode)

  // (_page['categories'] || []).length * CATEGORY_NAME_HEIGHT + _item_count * calcGoodsItemHeight();
  _.assign(_page, {
    height,
    pageIncreaseCount: pageIncreaseCount,
    offset: getOffset(_prevPageOffset, height),
    item_count: _item_count,
    key: md5([pageIndex, ..._.map(_page['categories'], 'id')].join('-'))
  })
  return _page
}

const getCategoryHeight = (addItemCount, category_id: any, mode) => {
  let itemCount = addItemCount
  if (mode === 'grid') {
    itemCount = _.ceil(addItemCount / GOODS_DISPLAY_MODE_COLUMN_MAP[mode])
    return itemCount * calcGoodsItemHeight(category_id, mode) + GRID_CATEGORY_NAME_HEIGHT
  }
  return itemCount * calcGoodsItemHeight(category_id, mode) + CATEGORY_NAME_HEIGHT
}

/**
 * 前一页面的商品数量不足pageSize,则更新
 * @param prevPage
 * @param category
 * @param pageSize
 * @param mode
 * @returns {{newPrevPage, newCategoryItemCount: number}}
 */
const updatePrevPage = (prevPage, category, pageSize, mode) => {
  const { item_count, id, category_id = id } = category
  // const prevPageCategories = _.get(prevPage, 'categories', []);
  // const { item_count: prevPageItemCount /*? prevPage.page == 2 ? prevPage.item_count : 'oo' */ } = prevPage;
  const prevPageItemCount = getPageAllItemCount(prevPage)
  const lessCount = pageSize - prevPageItemCount
  let prevPageOffset = getPrevPageOffset(prevPage)
  let _item_count = item_count - lessCount

  // 将当前分类的商品数量导入前一分页
  let addItemCount
  // 当前分类大于前一页少的数量
  if (item_count > lessCount) {
    prevPage['item_count'] += lessCount
    addItemCount = lessCount
  } else {
    // 当前分类小于前一页少的数量
    prevPage['item_count'] += item_count
    addItemCount = item_count
  }

  let pageIncreaseCount = prevPage['pageIncreaseCount'] || 0
  let currentPageAdd = 0

  // 如果当前分类商品数量为奇数，且下个分页还存在相同分类ID时
  if (mode === 'grid') {
    // 如果是三列，需要判断余数为1或者2
    const remainder = 1
    if (addItemCount % GOODS_DISPLAY_MODE_COLUMN_MAP[mode] === remainder && _item_count > 0) {
      addItemCount += remainder // 实际
      pageIncreaseCount += remainder
      _item_count -= remainder // 下个分类 -1
      currentPageAdd = remainder
    }
  }
  prevPage['item_count'] += currentPageAdd

  // 计算分类高度
  const key = `id-${category_id}-${prevPage.page}`
  const height = getCategoryHeight(addItemCount, category_id, mode)
  const categoryOffset = calcCategoryOffset(prevPageOffset, height, prevPage)
  prevPage['categories'].push(
    buildCategory(category, key, addItemCount, categoryOffset, isSamePrev(prevPage, category_id))
  )

  // 前一页面更新后，重新计算页面的高度等信息
  // const _height =
  //   _.reject(prevPage['categories'], { isSamePrev: true }).length * CATEGORY_NAME_HEIGHT +
  //   prevPage['item_count'] * calcGoodsItemHeight(category_id, mode);

  const _height = getPageHeight(prevPage, prevPage['item_count'], category_id, mode)

  // (prevPage['categories'] || []).length * CATEGORY_NAME_HEIGHT + prevPage['item_count'] * calcGoodsItemHeight();

  prevPageOffset = [prevPageOffset[0], prevPageOffset[0] + _height]
  const _key = md5([prevPage.page, _.map(prevPage['categories'], 'id')].join('-'))

  _.assign(prevPage, {
    height: _height,
    offset: prevPageOffset,
    key: _key,
    pageIncreaseCount
  })
  return { newPrevPage: prevPage, newCategoryItemCount: _item_count, addItemCount }
}

export const getActiveCategoryId = (renders, scrollTop) => {
  const _scrollTop = _.floor(scrollTop)

  const page = _.minBy(
    _.filter(renders, ({ offset }) => offset[1] - _scrollTop > 0),
    ({ offset }) => offset[1] - _scrollTop
  )

  if (page) {
    return _.get(
      _.minBy(
        _.filter(page.categories, ({ offset }) => offset[1] - _scrollTop > 0),
        ({ offset }) => offset[1] - _scrollTop
      ),
      'id'
    )
  }
}

let initGoodsCount = 0

export class HomeStore extends Store {
  public goodsModel: GoodsModel
  private static instance: any

  constructor(view, data) {
    super(view, data)
    this.goodsModel = new GoodsModel()
  }

  static getInstance(view, data) {
    if (!this.instance) {
      this.instance = new HomeStore(view, data)
    }
    return this.instance
  }

  static destroy() {
    if (this.instance) {
      // 清理实例持有的资源
      this.instance.destroyInstance()
    }
    this.instance = null
    return null
  }

  // 实例级别的destroy方法，用于清理资源
  destroyInstance() {
    // 清理goodsModel
    if (this.goodsModel) {
      this.goodsModel = null as any
    }

    // 清理视图引用
    this.clearViewReference()
  }

  buildPagesLayout(
    categories,
    size?: number,
    opts: { mode: 'list' | 'grid' | 'large-list' } = { mode: 'list' }
  ) {
    const { sqbBridge } = this.view.data
    const { mode } = opts
    // 商品列表初始偏移量[0, 170]
    const pages = []
    let idx = 0
    const pageSize = getPageSize(sqbBridge, size)

    /**
     * 每个分页最大商品数量：pageSize + 推荐商品的数量
     * @returns {*}
     */
    const getMaxPageSize = () => pageSize

    /**
     * 循环分类创建分页
     * @param category
     * @returns {*}
     */
    const walk = category => {
      if (!categories[idx]) return

      const prevPageIdx = pages.length - 1
      const { item_count } = category
      const prevPage = _.last(pages)
      const prevPageItemCount = getPageAllItemCount(prevPage) //_.get(prevPage, 'item_count', 0);
      const prevPageCategories = _.get(prevPage, 'categories', [])
      // 上页是否包含特殊分类
      const isPrevPageSpecialCategories =
        _.size(_.find(prevPageCategories, ({ id }) => IS_INCLUDES_SPECIAL(id))) || 0

      if (IS_INCLUDES_SPECIAL(_.get(category, 'id'))) {
        const _page = createPageItem(prevPage, category, item_count, prevPageIdx, pageSize, mode)
        idx++
        pages.push(_page)
        return walk(categories[idx])
      }

      const maxPageSize = getMaxPageSize()
      // 第一页或者前一页已经满足分页数量
      const shouldCreateNewPage =
        prevPageItemCount >= maxPageSize || !prevPage || isPrevPageSpecialCategories

      if (shouldCreateNewPage) {
        if (item_count > 0 && item_count <= maxPageSize) {
          const _page = createPageItem(prevPage, category, item_count, prevPageIdx, pageSize, mode)
          idx++

          pages.push(_page)
          return walk(categories[idx])
        }
        // 分类数量够分页
        if (item_count > maxPageSize) {
          const _page = createPageItem(prevPage, category, pageSize, prevPageIdx, pageSize, mode)

          const __page = _.reduce(
            _page,
            (curr, value, key) => {
              if (key === 'page' || key === 'offset' || key === 'categories') {
                if (key == 'page' || key == 'offset') {
                  curr[key] = value
                }
                if (key === 'categories') {
                  curr[key] = _.map(value, cat => {
                    return {
                      id: cat.id,
                      height: cat.height,
                      item_count: cat.item_count,
                      offset: cat.offset
                    }
                  })
                }
              }

              return curr
            },
            {}
          )

          // BUILD_PAGES_LAYOUT_DEBUG &&
          // console.log(`buildPageLayout page ${prevPageIdx}`, JSON.stringify({ page: __page }, null, 2));

          pages.push(_page)
          category['item_count'] -= maxPageSize
          return walk(category)
        }
      }

      // 前一页已经不满足分页数量
      const shouldUpdatePrevPage = prevPage && prevPageItemCount < maxPageSize

      if (shouldUpdatePrevPage) {
        if (prevPageItemCount < maxPageSize) {
          const result = updatePrevPage(prevPage, category, pageSize, mode)
          const { newPrevPage, newCategoryItemCount } = result
          pages[prevPageIdx] = newPrevPage

          if (result && prevPage.item_count >= maxPageSize) {
            const __page = _.reduce(
              prevPage,
              (curr, value, key) => {
                if (
                  key === 'height' ||
                  key === 'page' ||
                  key === 'offset' ||
                  key === 'item_count'
                ) {
                  curr[key] = value
                }
                if (key === 'categories') {
                  curr[key] = _.map(value, cat => {
                    return {
                      isSamePrev: cat.isSamePrev,
                      id: cat.id,
                      height: cat.height,
                      item_count: cat.item_count,
                      offset: cat.offset
                    }
                  })
                }

                return curr
              },
              {}
            )

            BUILD_PAGES_LAYOUT_DEBUG &&
              console.log(
                `buildPageLayout page ${prevPageIdx}`,
                JSON.stringify({ page: __page }, null, 2)
              )
          }

          if (newCategoryItemCount > 0) {
            category['item_count'] = newCategoryItemCount
            return walk(category)
          }
        }

        idx++
        return walk(categories[idx])
      }
    }

    categories && walk(categories[idx])
    return pages
  }

  /**
   * 获取实际高度后更新pages及category的offset和height
   * @returns
   * @param _pages
   * @param _renders
   * @param headerHeight
   * @param view
   */
  updatePages(_pages, _renders, headerHeight = 0, view) {
    const pages = _.cloneDeep(_pages)
    const renders = _.cloneDeep(_renders)

    if (headerHeight > 0) {
      pages.unshift({
        page: 'header',
        offset: [0, headerHeight]
      })
      renders.unshift({
        page: 'header'
      })
    }
    /**
     *  更新page和category的height和offset等
     * @param {*} idx
     * @param {*} elements
     * @param {*} increment
     * @returns
     */
    const update = (idx, elements, increment) => {
      if (!elements[idx]) return
      elements[idx].offset[0] += increment
      elements[idx].offset[1] += increment

      _.forEach(elements[idx].categories, cat => {
        cat.offset[0] += increment
        cat.offset[1] += increment
      })
      idx++

      return update(idx, elements, increment)
    }

    /**
     * 计算渲染后的实际商品列表分类的高度
     * @param {*} pageIdxs
     * @returns
     */
    const getCategoriesContainerHeight = (pageIdxs = [], _pages) => {
      const promises = _.map(pageIdxs, pageIdx => {
        return new Promise(resolve => {
          const query = isWeixin() ? view.createSelectorQuery() : wx.createSelectorQuery()
          query
            .selectAll(`#page-${pageIdx} .category-group`)
            .boundingClientRect()
            .exec(result => {
              resolve(result[0])
            })
        })
      })

      return Promise.all(promises)
    }

    const pageIdxs = _.map(renders, 'page')
    // 计算分类的商品的高度
    return getCategoriesContainerHeight(pageIdxs, pages).then(result => {
      const values = _.map(pageIdxs, (pageIdx, index) => [pageIdx, result[index]])
      const newPages = _.cloneDeep(pages)

      _.forEach(values, val => {
        const [page, categories] = val
        const pageIdx = _.findIndex(newPages, { page })

        if (pageIdx > -1) {
          _.forEach(categories, (category, catIdx: number) => {
            let increment = 0
            // 支付宝不支持获取dataset
            // const categoryKey = _.get(category, 'dataset.key');
            // const catIdx = _.findIndex(newPages[pageIdx].categories, { key: categoryKey });
            // const catIdx = index;
            if (catIdx > -1) {
              increment = _.floor(category.height - newPages[pageIdx].categories[catIdx].height)

              if (increment) {
                // allIncrement = increment;
                newPages[pageIdx].categories[catIdx].offset[1] += increment
                newPages[pageIdx].categories[catIdx].height += increment
                newPages[pageIdx].offset[1] += increment
                newPages[pageIdx].height += increment

                update(catIdx + 1, newPages[pageIdx].categories, increment)
              }
            }
          })

          const nextPage = newPages[pageIdx + 1]
          if (nextPage) {
            const _increment = _.get(newPages[pageIdx], 'offset[1]') - _.get(nextPage, 'offset[0]')

            update(pageIdx + 1, newPages, _increment)
          }
        }
      })

      const idx = _.findIndex(newPages, { page: 'header' })
      if (idx > -1) {
        newPages.splice(idx, 1)
      }

      return newPages
    })
  }

  async loadGoods(event, pages, serviceType, opts = {}): Promise<{ pages: {}[]; renders: {}[] }> {
    console.log('#loadGoods - 0', { event, pages, serviceType, opts })
    const { sqbBridge, cart } = this.view.data
    // @ts-ignore
    const {
      // @ts-ignore
      categories,
      // @ts-ignore
      loadedGoods,
      // @ts-ignore
      init = false,
      // @ts-ignore
      firstPageRenderGoodsCount = 6,
      // @ts-ignore
      viewportOffset,
      // @ts-ignore
      mode = 'list',
      // @ts-ignore
      display_image_size = 1
    } = opts
    if (init) initGoodsCount = 0
    const isIntersected = ([start, end], [start1, end1]) => {
      const result = Math.max.apply(null, [start, start1]) <= Math.min.apply(null, [end, end1])
      // console.log('商品', [start, end], [start1, end1], result);
      return result
    }
    const mainHeight = height - HEADER_BAR_HEIGHT - BOTTOM_HEIGHT
    // @ts-ignore
    const needShowCategoryName = (scrollTop, category: TCategory = {}) => {
      const { offset, isSamePrev } = category
      return isSamePrev && _.inRange.apply(null, [scrollTop, ...offset])
    }
    const scrollTop = _.get(event, 'detail.scrollTop', 0)
    const _viewport_offset = viewportOffset || VIEWPORT_OFFSET
    const range: [number, number] = [
      scrollTop - _viewport_offset,
      scrollTop + mainHeight + _viewport_offset
    ]
    const requests = []
    const renders = []
    const getIncludeSpecials = cats => _.filter(cats, ({ id }) => IS_INCLUDES_SPECIAL(id))
    const getIncludeSpecialSize = cats =>
      _.size(_.filter(cats, ({ id }) => IS_INCLUDES_SPECIAL(id)))

    const newPages = _.cloneDeep(pages)

    newPages.forEach(page => {
      const { offset, loaded, loading } = page
      if (isIntersected(offset, range)) {
        // console.log('商品 - 命中', { page, loaded, loading });
        page.rendered = true
        _.forEach(page.categories, category => {
          category['isShowCategoryName'] = !!needShowCategoryName(scrollTop, category)
        })

        renders.push(page)
        if (!loaded && !loading) {
          const specialCategories = getIncludeSpecials(page.categories)
          if (_.size(specialCategories)) {
            // 不更新key，会导致切换serviceType时，商品价格变化，不会触发更新
            _.forEach(specialCategories, category => {
              // 初始化时，只加载第一页的商品
              if (init) {
                if (initGoodsCount < firstPageRenderGoodsCount) {
                  const c = _.find(categories, { id: category.id })
                  if (_.get(c, 'items', []).length > 0 && category.data.length === 0) {
                    category.data = _.slice(c.items, 0, firstPageRenderGoodsCount - initGoodsCount)
                    initGoodsCount += category.data.length
                  }
                }
              } else {
                category.data = _.get(_.find(categories, { id: category.id }), 'items', [])
              }

              category &&
                _.forEach(category.data || [], item => {
                  item.current_own_category_id = category.id
                  calcRemainNumAndOutOfStock(item, cart)

                  return transformGoods(item, serviceType)
                })
            })
            return
          }
          if (!init) {
            page.loading = true
            // 修复支付宝会重新拉
            const _page = _.find(pages, { page: page.page })
            if (_page) {
              _page.loading = true
            }
          }

          requests.push(page)
        }
      } else {
        delete page.rendered
      }
    })

    // 最后一页的商品有可能在下一页
    if (!init && mode === 'grid') {
      const lastNewPage = _.last(requests)
      const lastPage = _.last(pages)
      if (_.get(lastNewPage, 'page') === _.get(lastPage, 'page')) {
        requests.push({
          page: _.get(lastNewPage, 'page', 0) + 1,
          loading: true
        })
      }
    }

    let pageIdxOffset = 0
    const specialCategorySize = getIncludeSpecialSize(categories)
    if (specialCategorySize) {
      pageIdxOffset += specialCategorySize
    }

    const results = await Promise.all(
      requests.map(({ page, categories: cats }) => {
        if (loadedGoods && loadedGoods.pages[page - pageIdxOffset - 1]) {
          return Promise.resolve({
            goods: loadedGoods.pages[page - pageIdxOffset - 1]
          })
        }
        // 不使用缓存
        // return this.goodsModel.fetchGoodsV2({
        //   page: page - pageIdxOffset || 1,
        //   page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
        //   service_type: serviceType,
        //   store_id: this.storeId,
        // });
        // console.log('商品', { page, pageIdxOffset, serviceType });
        // 使用缓存 - 优化：直接获取处理后的数据
        const processedGoods = this.goodsModel.getProcessedGoods({
          page: page - pageIdxOffset || 1,
          page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
          service_type: serviceType,
          store_id: this.storeId,
          display_image_size
        }, cart, serviceType)

        if (processedGoods) {
          return Promise.resolve(processedGoods)
        }

        // 如果没有处理后的缓存，则获取原始数据
        return this.goodsModel.getGoods({
          page: page - pageIdxOffset || 1,
          page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
          service_type: serviceType,
          store_id: this.storeId,
          display_image_size
        })
      })
    )

    requests.forEach((page, i) => {
      const recommendCat = _.find(page.categories, { id: RECOMMEND_CAT_ID })
      if (recommendCat) {
        const recommend = _.get(results[i], 'data.records')
        const categoryIdx = _.findIndex(page.categories, { id: RECOMMEND_CAT_ID })
        page.categories[categoryIdx].data = _.map(recommend, item => {
          calcRemainNumAndOutOfStock(item, cart)
          // return updateItemKey(item, serviceType);
          return transformGoods(item, serviceType)
        })
      } else {
        const goods = _.get(results[i], 'goods')
        if (!_.isEmpty(goods)) {
          _.forEach(page.categories, (cat, idx) => {
            const { id: category_id } = cat
            const catGoods = _.find(goods, { category_id })

            if (init && catGoods) {
              // 上一页面商品不足时，继续给分类补充商品
              if (initGoodsCount < firstPageRenderGoodsCount) {
                const items = _.slice(catGoods.items, 0, firstPageRenderGoodsCount - initGoodsCount)
                page.categories[idx]['data'] = _.map(items, item => {
                  // @ts-ignore
                  item.current_own_category_id = category_id
                  calcRemainNumAndOutOfStock(item, cart)
                  // return updateItemKey(item, serviceType);
                  return transformGoods(item, serviceType)
                })

                initGoodsCount += items.length
              }
            } else if (catGoods) {
              if (mode === 'grid') {
                // idx === 0 时，需要检查上一页商品是否满足当前分类的商品数量
                // 不满足, 需要补足
                this.handleGridGoods(goods, newPages, page, cart, serviceType)
              }
              const category = page.categories[idx]
              category['data'] = _.map(catGoods.items, item => {
                item.current_own_category_id = category_id
                calcRemainNumAndOutOfStock(item, cart)
                // return updateItemKey(item, serviceType);
                return transformGoods(item, serviceType)
              })
            }
          })
          //
          if (!page.categories && mode === 'grid') {
            this.handleGridGoods(goods, newPages, page, cart, serviceType)
          }
        }
      }

      if (!init) {
        // 修复会重新拉商品列表
        const _page = _.find(pages, { page: page.page })
        if (_page) {
          _.assign(_page, { loading: false, loaded: true })
        }

        _.assign(page, { loading: false, loaded: true })
      }
    })

    // console.log('requests', { requests, newPages });

    return new Promise(resolve => {
      resolve({
        pages: newPages,
        renders
        // activeCategoryId: getActiveCategoryId(pages, scrollTop),
      })
    })
  }
  // 获取零售商品分页数据
  loadRetailGoods({ serviceType, category_id, cursor }) {
    const { sqbBridge } = this.view.data
    const params = {
      page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
      service_type: serviceType,
      store_id: this.storeId,
      category_id,
      cursor
    }
    return this.goodsModel.fetchGoodsV2(params)
  }

  async loadGoodsV2(event, pages, serviceType, opts = {}) {
    const { sqbBridge, cart } = this.view.data
    // @ts-ignore
    const {
      // @ts-ignore
      categories,
      // @ts-ignore
      loadedGoods,
      // @ts-ignore
      init = false,
      // @ts-ignore
      firstPageRenderGoodsCount = 6,
      // @ts-ignore
      viewportOffset,
      // @ts-ignore
      mode = 'list',
      // @ts-ignore
      display_image_size = 1
    } = opts
    if (init) initGoodsCount = 0

    const mainHeight = height - HEADER_BAR_HEIGHT - BOTTOM_HEIGHT
    // @ts-ignore
    const needShowCategoryName = (scrollTop, category: TCategory = {}) => {
      const { offset, isSamePrev } = category
      return isSamePrev && _.inRange.apply(null, [scrollTop, ...offset])
    }
    const scrollTop = _.get(event, 'detail.scrollTop', 0)
    const visiblePages = event.detail.pages
    const _viewport_offset = viewportOffset || VIEWPORT_OFFSET
    // const range: [number, number] = [
    //   scrollTop - _viewport_offset,
    //   scrollTop + mainHeight + _viewport_offset
    // ]
    const requests = []
    const renders = []
    const getIncludeSpecials = cats => _.filter(cats, ({ id }) => IS_INCLUDES_SPECIAL(id))
    const getIncludeSpecialSize = cats =>
      _.size(_.filter(cats, ({ id }) => IS_INCLUDES_SPECIAL(id)))

    const newPages = _.cloneDeep(pages)

    newPages.forEach(page => {
      const { offset, loaded, loading } = page
      if (_.find(visiblePages, { key: page.page })) {
        page.rendered = true
        _.forEach(page.categories, category => {
          category['isShowCategoryName'] = !!needShowCategoryName(scrollTop, category)
        })

        renders.push(page)
        // 不要判断loading
        // 主要原因是， 因为网络时序问题， 会导致loading为true状态的请求的分类数据一直为空，也不会再次请求
        // 这个重复请求应该使用goodsModel的缓存机制避免
        // if (!loaded && !loading) {
        if (!loaded) {
          const specialCategories = getIncludeSpecials(page.categories)
          if (_.size(specialCategories)) {
            // 不更新key，会导致切换serviceType时，商品价格变化，不会触发更新
            _.forEach(specialCategories, category => {
              // 初始化时，只加载第一页的商品
              if (init) {
                if (initGoodsCount < firstPageRenderGoodsCount) {
                  const c = _.find(categories, { id: category.id })
                  if (_.get(c, 'items', []).length > 0 && category.data.length === 0) {
                    category.data = _.slice(c.items, 0, firstPageRenderGoodsCount - initGoodsCount)
                    initGoodsCount += category.data.length
                  }
                }
              } else {
                category.data = _.get(_.find(categories, { id: category.id }), 'items', [])
              }
              category &&
                _.forEach(category.data || [], item => {
                  item.current_own_category_id = category.id
                  calcRemainNumAndOutOfStock(item, cart)

                  return transformGoods(item, serviceType)
                })
            })
            return
          }
          if (!init) {
            page.loading = true
            // 修复支付宝会重新拉
            const _page = _.find(pages, { page: page.page })
            if (_page) {
              _page.loading = true
            }
          }

          requests.push(page)
        }
      } else {
        delete page.rendered
      }
    })

    // 最后一页的商品有可能在下一页
    if (!init && mode === 'grid') {
      const lastNewPage = _.last(requests)
      const lastPage = _.last(pages)
      if (_.get(lastNewPage, 'page') === _.get(lastPage, 'page')) {
        requests.push({
          page: _.get(lastNewPage, 'page', 0) + 1,
          loading: true
        })
      }
    }

    let pageIdxOffset = 0
    const specialCategorySize = getIncludeSpecialSize(categories)
    if (specialCategorySize) {
      pageIdxOffset += specialCategorySize
    }

    const results = await Promise.all(
      requests.map(({ page, categories: cats }) => {
        if (loadedGoods && loadedGoods.pages[page - pageIdxOffset - 1]) {
          return Promise.resolve({
            goods: loadedGoods.pages[page - pageIdxOffset - 1]
          })
        }
        // 不使用缓存
        // return this.goodsModel.fetchGoodsV2({
        //   page: page - pageIdxOffset || 1,
        //   page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
        //   service_type: serviceType,
        //   store_id: this.storeId,
        // });
        // 使用缓存
        return this.goodsModel.getGoods({
          page: page - pageIdxOffset || 1,
          page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
          service_type: serviceType,
          store_id: this.storeId,
          display_image_size
        })
      })
    )

    requests.forEach((page, i) => {
      const recommendCat = _.find(page.categories, { id: RECOMMEND_CAT_ID })
      if (recommendCat) {
        const recommend = _.get(results[i], 'data.records')
        const categoryIdx = _.findIndex(page.categories, { id: RECOMMEND_CAT_ID })
        page.categories[categoryIdx].data = _.map(recommend, item => {
          calcRemainNumAndOutOfStock(item, cart)
          // return updateItemKey(item, serviceType);
          return transformGoods(item, serviceType)
        })
      } else {
        const goods = _.get(results[i], 'goods')
        if (!_.isEmpty(goods)) {
          _.forEach(page.categories, (cat, idx) => {
            const { id: category_id } = cat
            const catGoods = _.find(goods, { category_id })

            if (init && catGoods) {
              // 上一页面商品不足时，继续给分类补充商品
              if (initGoodsCount < firstPageRenderGoodsCount) {
                const items = _.slice(catGoods.items, 0, firstPageRenderGoodsCount - initGoodsCount)
                page.categories[idx]['data'] = _.map(items, item => {
                  // @ts-ignore
                  item.current_own_category_id = category_id
                  calcRemainNumAndOutOfStock(item, cart)
                  // return updateItemKey(item, serviceType);
                  return transformGoods(item, serviceType)
                })

                initGoodsCount += items.length
              }
            } else if (catGoods) {
              if (mode === 'grid') {
                // idx === 0 时，需要检查上一页商品是否满足当前分类的商品数量
                // 不满足, 需要补足
                this.handleGridGoods(goods, newPages, page, cart, serviceType)
              }
              const category = page.categories[idx]
              // 优化：检查是否已经处理过数据
              if (catGoods.items && catGoods.items[0] && catGoods.items[0].uuid) {
                // 数据已经处理过，直接使用
                category['data'] = catGoods.items.map(item => {
                  item.current_own_category_id = category_id
                  return item
                })
              } else {
                // 数据未处理，需要转换
                category['data'] = _.map(catGoods.items, item => {
                  item.current_own_category_id = category_id
                  calcRemainNumAndOutOfStock(item, cart)
                  // return updateItemKey(item, serviceType);
                  return transformGoods(item, serviceType)
                })
              }
            }
          })
          //
          if (!page.categories && mode === 'grid') {
            this.handleGridGoods(goods, newPages, page, cart, serviceType)
          }
        }
      }

      if (!init) {
        // 修复会重新拉商品列表
        const _page = _.find(pages, { page: page.page })
        if (_page) {
          _.assign(_page, { loading: false, loaded: true })
        }

        _.assign(page, { loading: false, loaded: true })
      }
    })

    return new Promise(resolve => {
      resolve({
        pages: newPages,
        renders
        // activeCategoryId: getActiveCategoryId(pages, scrollTop),
      })
    })
  }

  private handleGridGoods(goods, newPages, page, cart, serviceType) {
    _.forEach(goods, (categoryGoods, idx) => {
      // @ts-ignore
      const { category_id: _category_id } = categoryGoods
      const prevPage = _.find(newPages, { page: page.page - 1 })
      if (!prevPage || !_.get(prevPage, 'loaded')) return
      const prevPageCategory = _.find(prevPage.categories, { id: _category_id })
      if (prevPageCategory) {
        // FIXIT: 如果向上滚动时，当前页面应该向上补充商品
        // 当前只满足向下滚动的方式
        const { data, item_count, id: prevCatId } = prevPageCategory // 上一分类
        const lessCount = item_count - data.length
        if (lessCount > 0) {
          // 从当前页面找上一个分类的商品
          const currentPageCatGoods = _.find(goods, { category_id: prevCatId })
          if (_.get(currentPageCatGoods, 'items.length')) {
            // @ts-ignore
            const items = _.take(currentPageCatGoods.items, lessCount)
            _.forEach(items, item => {
              calcRemainNumAndOutOfStock(item, cart)
              // @ts-ignore
              data.push(transformGoods(item, serviceType))
            })
            // @ts-ignore
            goods[idx].items = _.slice(currentPageCatGoods.items, lessCount)
          }
        }
      }
    })
  }

  /**
   * 计算分类的偏移量-网格模式
   * @param categories
   * @param pages
   * @param elements
   */
  calcCategoryOffsetGrid(categories, pages, elements) {
    if (!categories || !categories.length) return
    const pageCategories = _.flatMap(pages, 'categories')
    const groupedCategories = _.groupBy(pageCategories, 'id')
    const mergedCategories = _.map(groupedCategories, category => {
      return {
        ...category[0],
        offset: [category[0].offset[0], category[category.length - 1].offset[1]]
      }
    })

    // const truncateLength = 11; // 截断长度8个字
    // @ts-ignore
    const name = _.get(_.first(categories), 'shortName', '')
    const offsetXWithBoldFont = _.floor(name.length * 4.8)

    return _.map(categories, (category, idx: number) => {
      const element = _.find(elements, { id: `menu-cid-${category.id}` })
      const pageCategory = _.find(mergedCategories, { id: category.id })

      const scrollElements = _.take(elements, idx - 1)
      const paddingRight = 50
      // const categoryTextPadding = 0
      const scrollX = _.floor(_.sumBy(scrollElements, 'width'))
      const width = _.floor(element.width)

      if (idx === 0 || idx === 1) {
        return {
          ...category,
          offset: [0, 0],
          width,
          styleWidth: width - paddingRight,
          pageOffset: pageCategory.offset
        }
      }
      return {
        ...category,
        offset: [scrollX, 0],
        width,
        styleWidth: width - paddingRight,
        pageOffset: pageCategory.offset
      }
    })
  }

  async pollAllGoods(payloadGoods, pageCount = 6, maxTotal, params = { page: 1 }) {
    const total = _.get(payloadGoods, 'total', 0)
    _.merge(params, {
      store_id: this.storeId,
      service_type: this.serviceType
      // page_size: sqbBridge.getConfig('PLUGIN_GOODS_PAGE_SIZE') || PAGE_SIZE,
    })
    // console.log('💢商品预拉取 - 开始', { pageCount, total, params });
    const allGoods = await this.goodsModel.pollAllGoods(
      params,
      total,
      pageCount,
      payloadGoods,
      maxTotal
    )
    // console.log('💢商品预拉取 - 结束', { allGoods });
    return allGoods
  }

  setPreloadStatus(status) {
    this.goodsModel.setPreloadStatus(status)
  }
}
