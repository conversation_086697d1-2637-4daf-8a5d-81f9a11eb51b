import { Model } from './Model'
import _ from '@wosai/emenu-mini-lodash'
// 缓存对象，用于存储API响应
const cache = new Map()
// const LIMIT_TOTAL = 180;

function log(...args) {
  console.log('💢商品预拉取 ', ...args)
}

function toUrl(params) {
  const { service_type, store_id, page } = params
  if (!(service_type && store_id && page)) throw new Error('params error')
  return `${service_type}/${store_id}/${page}`
}

export class GoodsModel extends Model {
  private isEnableGoodsPreload = true
  // @retryable({ retries: 3, timeout: 1000 })
  public fetchGoodsV2(params) {
    return this.api('fetchGoodsV2', params)
  }

  setPreloadStatus(status = false) {
    this.isEnableGoodsPreload = status
  }

  toUrl = toUrl

  async pollAllGoods(params, total, pageCount = 6, payloadGoods, maxTotal) {
    const { service_type, store_id } = params
    // 拼接url， 用于缓存商品数量
    const url = `${service_type}/${store_id}/total`
    const goodsTotalChanged = this.cache.get(url) !== total
    this.cache.set(url, total)

    const cachePageSize = this.getCachePageSize(params) // ?
    // 如果聚合接口
    if (payloadGoods) {
      const pages = _.get(payloadGoods, 'pages', [])
      let _page = 1
      _.forEach(pages, page => {
        const url = this.toUrl({ ...params, page: _page })
        this.setCache(url, page)
        _page += 1
      })
      if (cachePageSize >= total) {
        // log('命中聚合接口商品缓存， 不需要重新缓存', { cachePageSize, total });
        return this.cache
      }
    }

    pageCount = this.limitPageCount(pageCount)
    const limitTotal = this.limitTotal(total, maxTotal)

    const pollPageSize = this.getPollPageSize(params, pageCount)
    const totalCalls = this.calculateTotalCalls(limitTotal, pollPageSize)

    const _params = {
      ...params,
      page_size: pollPageSize
    }

    // 商品数量变化，重新拉取
    if (this.isCacheAllGoods(totalCalls, _params) && !goodsTotalChanged) {
      // log('商品数量无变化或者已经缓存商品， 不重新预拉取商品==============', { goodsTotalChanged, cache: this.cache });
      return this.cache
    }

    for (let i = 0; i < totalCalls; i++) {
      // log('每页商品缓存 - 开始', { _params, cachePageSize, totalCalls });
      await this.fetchAndCacheGoods(_params, cachePageSize)
      _params.page += 1
    }

    return this.cache
  }

  private limitPageCount(pageCount: number) {
    return Math.min(pageCount, 6)
  }

  private limitTotal(total: number, limitTotal: number) {
    return Math.min(total, limitTotal)
  }

  isGoodsInCache(url: string) {
    return this.cache.has(url)
  }

  isCacheAllGoods(totalCalls, _params) {
    let cachePage = 1
    let isCacheAllGoods = true
    for (let i = 0; i < totalCalls; i++) {
      const url = this.toUrl({ ..._params, page: cachePage })
      if (!this.isGoodsInCache(url)) {
        isCacheAllGoods = false
        break
      }
      cachePage += 1
    }

    // log('是否命中预拉取', { isCacheAllGoods, cache: this.cache, totalCalls });

    return isCacheAllGoods
  }

  getCachePageSize(params) {
    return _.get(params, 'page_size', 30)
  }

  getPollPageSize(params, pageCount: number) {
    return this.getCachePageSize(params) * pageCount
  }

  calculateTotalCalls(total: number, pageSize: number) {
    return Math.ceil(total / pageSize)
  }

  async fetchAndCacheGoods(_params, cachePageSize) {
    // log('fetchAndCacheGoods', { _params, cachePageSize });
    try {
      const goods = await this.fetchGoodsV2(_params)
      if (goods && goods.goods.length > 0) {
        const newPages = this.paginateGoods(goods, cachePageSize)
        _.forEach(newPages, (page, idx) => {
          const url = this.toUrl({
            ..._params,
            page: this.getCachePage(_params, idx, cachePageSize)
          })
          this.setCache(url, page)
        })
      }
    } catch (e) {
      console.error('pollAllGoods error', e)
    }
  }
  getCachePage(params, idx, cachePageSize) {
    const page = params.page || 1
    const pageSize = params.page_size || 30

    const result = (pageSize * (page - 1)) / cachePageSize + idx + 1

    // log('getCachePage', { page, pageSize, idx, cachePageSize, result });

    return result
  }

  // 添加处理后数据的缓存
  private processedGoodsCache = new Map()

  async getGoods(params) {
    // TODO: 过滤params service_type store_id 确保url一致
    const url = toUrl(params)
    // log('#getGoods', { params, url, cache });
    const cachedGoods = cache.get(url)
    if (cachedGoods && this.isEnableGoodsPreload) {
      // log('命中缓存', { params, url, cachedGoods, cache });
      return _.cloneDeep(cachedGoods)
    }
    try {
      const goods = await this.fetchGoodsV2(params) // ?
      this.setCache(url, goods)
      return goods
    } catch (e) {
      console.error('getGoods error', e)
      return null
    }
  }

  // 新增：获取处理后的商品数据（避免重复处理）
  getProcessedGoods(params, cart, serviceType) {
    const url = toUrl(params)
    const cacheKey = `${url}_${serviceType}_processed`

    if (this.processedGoodsCache.has(cacheKey)) {
      return this.processedGoodsCache.get(cacheKey)
    }

    const rawGoods = cache.get(url)
    if (!rawGoods) return null

    // 处理商品数据
    const processedGoods = {
      ...rawGoods,
      goods: rawGoods.goods.map(item => {
        const processedItem = transformGoods(item, serviceType)
        calcRemainNumAndOutOfStock(processedItem, cart)
        return processedItem
      })
    }

    // 缓存处理后的数据
    this.processedGoodsCache.set(cacheKey, processedGoods)
    return processedGoods
  }

  // 清理处理后数据的缓存
  clearProcessedCache() {
    this.processedGoodsCache.clear()
  }

  get cache() {
    return cache
  }

  // @cacheGoodsImage
  setCache(url: string, goods) {
    // log('setCache', { url, goods });
    cache.set(url, goods)
  }

  paginateGoods(data, pageSize) {
    const paginatedData = [] // 存储所有分页后的数据
    let currentPage = { goods: [] } // 当前页
    let currentCount = 0 // 当前页的项目计数

    _.forEach(data.goods, category => {
      // 检查当前页加上此类别的项目数是否超过页面大小
      if (currentCount + category.items.length > pageSize) {
        // 需要分页
        const remainingSpace = pageSize - currentCount // 当前页剩余空间
        let remainingItems = category.items.slice(remainingSpace) // 下一页的项目

        // 将当前类别的一部分项目添加到当前页
        currentPage.goods.push({
          category_id: category.category_id,
          items: category.items.slice(0, remainingSpace)
        })
        paginatedData.push(currentPage)

        // 创建新的页面并添加剩余的项目
        currentPage = { goods: [{ category_id: category.category_id, items: remainingItems }] }
        currentCount = remainingItems.length

        // 如果剩余项目超过页面大小，则继续分页
        while (currentCount > pageSize) {
          remainingItems = currentPage.goods[0].items.slice(pageSize)
          currentPage.goods[0].items = currentPage.goods[0].items.slice(0, pageSize)

          paginatedData.push(currentPage)
          currentPage = { goods: [{ category_id: category.category_id, items: remainingItems }] }
          currentCount = remainingItems.length
        }
      } else {
        // 将整个类别添加到当前页
        currentPage.goods.push(category)
        currentCount += category.items.length
      }
    })

    // 添加最后一页（如果有项目的话）
    if (currentPage.goods.length > 0) {
      paginatedData.push(currentPage)
    }
    // log('#paginatedData', paginatedData);

    return paginatedData
  }
}
