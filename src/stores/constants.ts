/**
 * 商品列表每商品固定高度
 * @type {number}
 */
export const GOODS_ITEM_HEIGHT = 81
export const GRID_GOODS_ITEM_HEIGHT = 400 // RPX
export const LARGE_GOODS_ITEM_HEIGHT = 200 // 大图模式商品列表每商品固定高度
/**
 * 买过的菜 商品列表每商品固定高度
 * @type {number}
 */
export const RECENT_GOODS_ITEM_HEIGHT = 36
/**
 * 商品列表可视区域偏移量
 * @type {number}
 */
export const VIEWPORT_OFFSET = 2000
/**
 * 每商品margin-bottom
 * @type {number}
 */
export const GOODS_ITEM_MARGIN_BOTTOM = 24
export const GRID_GOODS_ITEM_MARGIN_BOTTOM = 7
export const LARGE_GOODS_ITEM_MARGIN_BOTTOM = 24
/**
 * 商品列表中分类文字高度
 * @type {number}
 */
export const CATEGORY_NAME_HEIGHT = 36
export const GRID_CATEGORY_NAME_HEIGHT = 35
/**
 * API请求分页size
 * @type {number}
 */
export const systemInfo = wx.getSystemInfoSync()
export const STATUS_BAR_HEIGHT = systemInfo.statusBarHeight // 手机状态栏高度
export const CAPSULE_HEIGHT = systemInfo.platform === 'ios' ? 48 : 44 // 在安卓端胶囊48px，ios端44px
export const STORE_DISTANCE_TO_TOP = STATUS_BAR_HEIGHT + CAPSULE_HEIGHT + 22 //门店位置距离顶部距离
// export const HEADER_BAR_HEIGHT = STATUS_BAR_HEIGHT + CAPSULE_HEIGHT || 88
export const HEADER_BAR_HEIGHT = 88
export const RECOMMEND_CAT_ID = 'recommend'
export const HOT_SALE_CAT_ID = 'hotSale'
export const RECENT_CAT_ID = 'recent'
export const DISCOUNT_CAT_ID = 'discount'
export const TAG_CAT_ID = 'product_tag'
export const SPECIAL_CAT_IDS = [
  RECOMMEND_CAT_ID,
  HOT_SALE_CAT_ID,
  RECENT_CAT_ID,
  DISCOUNT_CAT_ID,
  TAG_CAT_ID
]
// 商品列表滚动
/**
 * 商品列表视窗默认高度
 * @type {number}
 */
/**
 * 商品列表每商品固定高度
 * @type {number}
 */
