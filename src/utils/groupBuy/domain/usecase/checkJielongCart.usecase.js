import { formatCart } from '../util'

let checkJielongCartUseCase = ({ request, reduceRepository }) => {
  return async function (jielong_id) {
    let store_id = reduceRepository.getStore().storeId
    let result = await request({
      url: '/api/v1/jielong/check',
      method: 'POST',
      data: {
        jielong_id,
        store_id
      }
    })
    formatCart(result.data)
    return result.data
  }
}

export { checkJielongCartUseCase }
