import dayjs from '@wosai/emenu-mini-dayjs'
import _ from '@wosai/emenu-mini-lodash'
import qs from 'qs'
import { AddressService } from '@wosai/emenu-mini-services'
import {
  CdnUtils,
  DateUtils,
  getAppId,
  isWeixin,
  NumberUtils,
  StorageUtils,
  StringUtils
} from '@wosai/emenu-mini-utils'
import { getPageUrl, getPolyLine, getStoreImage } from './helper'
import CONFIG from '@wosai/emenu-mini-config'
import { transformByDPR } from './rpx'
import { md5 } from '@wosai/emenu-mini-md5'
import { canIUse } from './canIUse'

const {
  COMMON_ORDER,
  DEFAULT_DATE_FORMATTER,
  DEFAULT_DIRECTION_TYPE,
  DEFAULT_GOODS_IMG,
  DEFAULT_MEAL_TYPE,
  GEO_TYPE,
  MCC_BUSINESS_STATUS,
  MCC_DELIVERY_TYPE,
  MCC_PRE_BUSINESS_STATUS,
  MCC_TAKEOUT_STATUS,
  MERCHANT_RECEIVED_ORDER,
  WECHAT_PAY_WAY,
  PRE_ORDER,
  RETAIL_ORDER,
  SERVICE_TYPES,
  STORED_PAY_WAY,
  SUB_PAY_WAY,
  SUBSCRIBE_ORDER_SCENE,
  TAKE_OUT_ORDER,
  TAKEOUT_ORDER_SCENE,
  WEEKDAYS,
  PAGES,
  MP_PAGES
} = CONFIG

const { oss } = CdnUtils
const { storage } = StorageUtils
const { toEllipsis } = StringUtils
const { reduction } = NumberUtils
const { format } = DateUtils

const geoType = GEO_TYPE && GEO_TYPE.toUpperCase()
const addressService = new AddressService()
const bikerMan = 'https://smart-static.wosaimg.com/99zhe/map/map2/bikerMan.png'
const riderArriveStore = [603]
const TITLE_DICT = {
  [TAKE_OUT_ORDER]: {
    title: '选择送达时间',
    firstLine: '立即送出'
  },
  [PRE_ORDER]: {
    title: '选择取单时间',
    firstLine: '立即取餐'
  },
  [RETAIL_ORDER]: {
    title: '选择取单时间',
    firstLine: '立即取货'
  }
}

export const handleGoodsImg = {
  getUrl(url) {
    return _.split(url || DEFAULT_GOODS_IMG, ',')
  },
  setGoodsImg(url, format = { format: 'png' }) {
    return _.head(this.getUrl(url))
  }
}

export const formatZhCn = (timestamp, formatDict) => {
  try {
    if (!timestamp) return
    const diff = dayjs(format(timestamp, DEFAULT_DATE_FORMATTER)).diff(
      format(dayjs(), DEFAULT_DATE_FORMATTER),
      'day'
    )
    if (!diff) return DateUtils.formatHour(timestamp)
    let prefix = ''
    const week = _.nth(WEEKDAYS, dayjs(timestamp).day() - 1)
    const [year, month, day, hour, minute, second] = _.split(format(timestamp), /\D/)
    if (diff > 1) {
      prefix = `${month}月${day}日(${week})`
    } else {
      prefix = `明天(${week})`
    }

    if (formatDict) {
      if (typeof formatDict !== 'object') throw new Error('format is dict')
      const mapValue = formatDict[String(diff)]
      if (mapValue) prefix = mapValue
    }

    return `${prefix} ${hour}:${minute}`
  } catch (error) {
    console.log('error____error', error)
  }
}

// 省略 等～
export const formatBrief = (items, len) => {
  if (!_.isArray(items) || !_.size(items)) return ''
  let { item: { name } = {} } = items[0]
  if (len) name = toEllipsis(name, len)
  if (_.size(items) === 1) return name
  return `${name} 等${items
    .map(({ item }) => ~~_.get(item, 'number'))
    .reduce((i, j) => i + j)}件商品`
}

export function handleDeliveryFee(dataSource) {
  const delivery_info = _.get(dataSource, 'extra.delivery_info')
  // 处理费送相关
  const deliveryFee = +_.get(delivery_info, 'delivery_fee') || 0
  const reductionAmount = _.get(delivery_info, 'reduction_amount')
  const realDeliveryFee = reduction(deliveryFee, reductionAmount || 0)
  return { realDeliveryFee, reductionAmount, deliveryFee }
}

export const getDiscountByOrder = async (order, cartSvc) => {
  const { realDeliveryFee } = handleDeliveryFee(order)
  const storeId = _.get(order, 'store_id')
  const totalAmount = _.get(order, 'original_amount') - realDeliveryFee || 0
  const type = _.get(order, 'type')
  const sn = _.get(order, 'sn')
  const isStoredPay = _.get(order, 'payway') == STORED_PAY_WAY
  let discountStrategy
  const takeOuterOrder = [TAKE_OUT_ORDER, PRE_ORDER]
  if (type) {
    discountStrategy = !_.includes(takeOuterOrder, type)
      ? SUBSCRIBE_ORDER_SCENE
      : TAKEOUT_ORDER_SCENE
  }

  try {
    let discounts =
      (await cartSvc.fetchDiscountV2({
        totalAmount,
        storeId,
        payway: isStoredPay ? STORED_PAY_WAY : WECHAT_PAY_WAY,
        subPayway: SUB_PAY_WAY,
        discountStrategy: discountStrategy || COMMON_ORDER,
        order_sn: sn,
        // getDiscountByOrder方法只在订单详情调用，只能使用原生支付方式，故传false
        from_cart: false
      })) || {}
    let { total_discount: totalDiscount } = discounts
    let paidAmount = reduction(totalAmount, totalDiscount)
    if (paidAmount < 0) paidAmount = 0
    return { ...discounts, paidAmount }
  } catch (error) {
    console.log('error_____error', error)
  }
}

export function handleMealType(storeConfigs) {
  if (_.isEmpty(storeConfigs)) return []
  try {
    let cloneOrderTypes = _.cloneDeep(SERVICE_TYPES)
    let businessStatus = _.get(storeConfigs, MCC_BUSINESS_STATUS, 0)
    let takeoutStatus = _.get(storeConfigs, MCC_TAKEOUT_STATUS, 0)
    let takeoutType = _.get(storeConfigs, MCC_DELIVERY_TYPE)
    let preBusinessStatus = _.get(storeConfigs, MCC_PRE_BUSINESS_STATUS, 1)
    if (_.isNil(preBusinessStatus)) preBusinessStatus = true
    takeoutType = takeoutType || DEFAULT_MEAL_TYPE
    _.forEach(cloneOrderTypes, item => {
      const { status } = item
      status === MCC_BUSINESS_STATUS &&
        _.set(_.find(cloneOrderTypes, { status: MCC_BUSINESS_STATUS }), 'status', businessStatus)
      status === MCC_TAKEOUT_STATUS &&
        _.set(_.find(cloneOrderTypes, { status: MCC_TAKEOUT_STATUS }), 'status', takeoutStatus)
      status === MCC_PRE_BUSINESS_STATUS &&
        _.set(
          _.find(cloneOrderTypes, { status: MCC_PRE_BUSINESS_STATUS }),
          'status',
          preBusinessStatus && takeoutStatus
        )
    })
    if (~~takeoutType === 1 || !takeoutType || ~~takeoutType === -1) {
      _.set(_.find(cloneOrderTypes, { value: TAKE_OUT_ORDER }), 'status', false)
    }
    return _.filter(cloneOrderTypes, item => !!_.get(item, 'status'))
  } catch (error) {
    console.log('error______error', error)
  }
}

export const fetchBatchDistance = async (allAddress, to) => {
  if (_.isEmpty(allAddress)) {
    return []
  }

  function handleAddressParams() {
    return _.map(allAddress, ({ id, latitude: lat, longitude: lon } = {}) => ({
      id,
      from: { lat, lon },
      to,
      directionType: 'BICYCLING'
    }))
  }

  return await addressService.fetchBatchDirection(handleAddressParams(allAddress, to))
}

export const checkCarts = function (storeId, serviceType) {
  return Promise.resolve()
  // if (!serviceType) {
  //   console.log(serviceType, 'checkCarts 需要参数serviceType');
  // }
  // // 1 价格变化 2 价格变化 || 商品变化
  // return new Promise(async (res, rej) => {
  //   storage({
  //     [handleHashKey('goodsChange', storeId)]: null,
  //   });
  //   const opts = getQueryOpts.call(this);

  //   const cartService = new CartService(opts);
  //   const result = await cartService.checkCartV1({ storeId, service_type: serviceType, tableId: opts.tableId });

  //   if (!result) return res();
  //   const { success, invalid, price, check_fail_list } = result || {};

  //   if (success) return res();
  //   // const isInvalid = !_.isEmpty(invalid);
  //   // const isPriceChange = !_.isEmpty(price);
  //   const isFailList = !_.isEmpty(check_fail_list);
  //   storage({
  //     [handleHashKey('goodsChange', storeId)]: { invalid, price },
  //   });
  //   if (isFailList) return rej(check_fail_list);
  //   // if (!isInvalid && isPriceChange) {
  //   //   rej(Error('购物车商品价格发生变化,请确认后下单'));
  //   // }
  //   // if (isInvalid || isPriceChange) {
  //   //   rej(Error('购物车商品发生变化，请确认后下单'));
  //   //   // return goBack();
  //   // }
  // });
}

export const mealRoundStatusMap = order => {
  const statusMap = {
    INIT: 1,
    ACCEPTED: 611
  }
}

export class MapHandelData {
  constructor(ctx, refreshLocation = false) {
    this.ctx = ctx
    this.refreshLocation = refreshLocation
  }

  location = null
  avatar = null
  static createMapData = function (ctx) {
    return new this(ctx)
  }
  setMarkerInit = function () {
    const { store } = this.ctx
    const { config: themeConfig } = this.ctx.data
    const { lat, lon } = _.get(store, 'location') || {}
    const iconWidth = transformByDPR(90) || 45
    const storeLogo = oss(getStoreImage(store) || themeConfig.iconDefaultStore, {
      width: 45,
      ratio: 2,
      round: 6
    })
    return {
      latitude: lat,
      longitude: lon,
      iconPath: storeLogo,
      width: iconWidth,
      height: iconWidth,
      id: 1
    }
  }
  getTakeOutMapPoints = async function (order) {
    const { config: themeConfig } = this.ctx.data
    try {
      let { status, type, extra, sn, avatar = themeConfig.iconDefaultPortrait } = order || {}
      // delivery_info
      const delivery_info = _.get(extra, 'delivery_info') || {}
      const is_dm_info = _.get(extra, 'dm_info.dm_mobile')
      const initMarkers = this.setMarkerInit()
      // // 外卖配送  1 有骑手信息前  2骑手到店前  3 骑手到店后  code 603 骑手正在店内取货
      const { latitude: lat1, longitude: lon1 } = delivery_info
      let markers = [initMarkers]
      let state = {}
      if (!is_dm_info) {
        // 用户信息 id 为 2  骑手 id 为 3
        markers = [
          initMarkers,
          { ...initMarkers, latitude: lat1, longitude: lon1, id: 2, iconPath: avatar }
        ]
      } else {
        if (_.includes(riderArriveStore, status)) {
          markers = [initMarkers]
        } else {
          try {
            const { latitude: riderLat, longitude: riderLon } =
              await addressService.fetchRiderLocation(sn)
            if (riderLat) {
              state = { riderLat, riderLon }
              markers = [
                initMarkers,
                {
                  ...initMarkers,
                  latitude: riderLat,
                  longitude: riderLon,
                  id: 3,
                  iconPath: bikerMan
                }
              ]
            }
          } catch (error) {
            console.log('error____error', error)
          }
        }
      }
      return { ...state, markers }
    } catch (error) {
      console.log('error_____error', error)
    }
  }

  async geLocation() {
    if (this.location) return this.location
    this.location = await this.ctx.call('getLocation', this.refreshLocation).catch(err => null)
    // this.location = await getLocation(this.refreshLocation).catch((err) => null);
    return this.location
  }

  async getUserAvatarUrl() {
    if (this.avatar) return this.avatar
    const { config: themeConfig } = this.ctx.data

    const user = await this.ctx.call('getUserInfo').catch(err => ({}))
    return (this.avatar = oss(_.get(user, 'avatarUrl', themeConfig.iconDefaultPortrait), {
      width: 45,
      round: 6
    }))
  }

  async handleMapData(order) {
    if (_.isEmpty(order)) return
    const { store } = this.ctx
    const { store_id: storeId, id, extra, type, sn, status } = order
    const { lat, lat2 = lat, lon, lon2 = lon } = _.get(store, 'location') || {}
    let avatar = this.avatar
    if (!avatar) avatar = await this.getUserAvatarUrl()
    let latitude = lat2,
      longitude = lon2
    let markers = [this.setMarkerInit()]
    let state = { latitude, longitude, markers, store, showMapOrderId: id }
    const isTakeOutInSelf = _.get(extra, 'delivery_info.delivery_type') == 2
    let { latitude: lat1, longitude: lon1 } = _.get(extra, 'delivery_info') || {}
    if (isTakeOutInSelf || type === PRE_ORDER) {
      const location = (await this.geLocation()) || {}
      lat1 = +_.get(location, 'latitude')
      lon1 = +_.get(location, 'longitude')
      latitude = Number(lat1) - (lat1 - lat2) / 2
      longitude = Number(lon1) - (lon1 - lon2) / 2
      markers = [
        ...markers,
        { ...markers[0], latitude: lat1, longitude: lon1, id: 2, iconPath: avatar }
      ]
      let latDict = { lat1, lon1, lat2, lon2 }
      if (isTakeOutInSelf) {
        latDict = { lat1: lat2, lon1: lon2, lat2: lat1, lon2: lon1 }
      }
      const direction = await addressService.fetchAddressLine({
        ...latDict,
        geoType,
        directionType: DEFAULT_DIRECTION_TYPE
      })
      const polyline = getPolyLine(direction)
      state = { ...state, latitude, longitude, markers, polyline, userLocation: location }
      return this.ctx.setData(state), state
    }

    if (type === TAKE_OUT_ORDER) {
      const mapPointsInfo = (await this.getTakeOutMapPoints({ ...order, avatar })) || {}
      const { markers, ...riderPosition } = mapPointsInfo || {}
      this.ctx.setData({ ...state, ...mapPointsInfo, riderPosition })
    }
  }
}

export const takeOutTypeMap = type => TITLE_DICT[type].firstLine

/**
 * 将url中的占位符替换为真实值
 * @param {*} url
 * @description 目前只支持 `:userId`、`:openId`、`:appId`
 * @description 支持md5加密 `:md5:userId:sqbWxMini`、`:md5:openId`、`:md5:appId:sqbWxMini`
 * @example
 * // userId = 123
 * // openId = 456
 * // appId = 789
 * transformUrlByPlaceholders('https://www.baidu.com/:userId/:openId/:appId')
 * // https://www.baidu.com/123/456/789
 * @returns {string}
 */
export function transformUrlByPlaceholders(url = '') {
  if (typeof url !== 'string' || !url) return url

  try {
    url = url
      .replace(/:userId/g, storage('currentUser.userId'))
      .replace(/:openId/g, storage('currentUser.thirdpartyUserId'))
      .replace(/:appId/g, getAppId())

    const encodeTypes = { md5 }
    const encodeReg = new RegExp(
      `(?::(${Object.keys(encodeTypes).join('|')}))([\\w-]+)(?::([\\w-]+))?`,
      'g'
    )

    url = url.replace(encodeReg, (match, p1, p2, p3) => {
      // 将 `:md5123:sqbWxMini` 转换为 `md5('sqbWxMini:123')`
      // 将 `:md5123` 转换为 `md5('123')`
      const fn = encodeTypes[p1]
      const str = p3 ? `${p3}:${p2}` : p2
      return _.isFunction(fn) ? fn(str) : match
    })
  } catch (error) {
    console.log(error)
  }

  return url
}

/**
 * 运营位跳转
 * @description 暂时只需要 `url` 属性
 * @param {*} payload 运营配置数据
 * @param {*} payload.url 配置的跳转链接
 * @param {*} payload.params 额外的参数
 */
export function operationJump(payload = {}) {
  const sqbBridge = _.get(this, 'data.sqbBridge')

  let { url: path, params = {} } = payload
  path = _.trim(path)

  // 空
  if (!sqbBridge || !path) return

  path = transformUrlByPlaceholders(path)

  const queryIndex = path.indexOf('?')
  let _path = queryIndex >= 0 ? path.substring(0, path.indexOf('?')) : path
  let search = queryIndex >= 0 ? path.substring(path.indexOf('?') + 1) : ''
  let query = qs.parse(search) || {}

  // 合并参数
  _.merge(query, params)

  const isWebView = /^(http|https)/.test(path),
    isWeixinUrl = /^weixin:\/\//.test(path),
    isAlipayUrl = /^alipays:\/\//.test(path),
    isSQBUrl = /^sqb:\/\//.test(path)

  if (isWebView) {
    // h5 链接
    return sqbBridge.navigateTo({
      target: 'webview',
      path: sqbBridge.isJJZ ? PAGES.web : path,
      query: { url: sqbBridge.isJJZ ? path : undefined }
    })
  } else if (isSQBUrl && _.isFunction(sqbBridge.open)) {
    // 符合sqb://协议的链接
    return sqbBridge.open({ schemaUrl: path })
  } else if (((isWeixin() && isWeixinUrl) || (!isWeixin() && isAlipayUrl)) && query.appId) {
    // 跳转微信 或 支付宝小程序
    // weixin://dl/business/?t=${TICKET}
    // alipays://platformapi/startApp?appId=2021002147643982&chinfo=hengsaoka_shouqianba
    // appId: 小程序 appId
    // openType: 打开小程序的方式
    // extraData: 需要传递给目标小程序的数据，目标小程序可在 App.onLaunch，App.onShow 中获取到这份数据。
    const { appId, openType, path: pagePath, ...extraData } = query
    const sqb = isWeixin() ? wx : my
    const method =
      openType === 'embedded' && canIUse('openEmbeddedMiniProgram')
        ? 'openEmbeddedMiniProgram'
        : 'navigateToMiniProgram'
    if (_.isFunction(sqb[method])) {
      return sqb[method]({
        appId,
        path: pagePath,
        extraData,
        query: extraData,
        fail: error => {
          console.log(error)
        }
      })
    }
  }

  // pages/stores/index?page=gatherStore&storeSetId=xx
  // pages/stores/index?componentName=page-search&pluginName=scanPlugin
  if (_path === '/pages/stores/index' || _path === 'pages/stores/index') {
    // 兼容老的命名
    const pageNameMap = {
      gatherStore: 'page-aggregation-list',
      gatherStoreEntrance: 'page-aggregation-list'
    }
    const { componentName, page } = query
    if (page) _path = _.kebabCase(pageNameMap[page] || page) || _path
    else if (componentName) _path = _.kebabCase(componentName) || _path
  }

  // 存放在 PAGES 或者 MP_PAGES 里的页面
  if ((sqbBridge.isJJZ && PAGES[_path]) || (!sqbBridge.isJJZ && MP_PAGES[_path])) {
    // const { activitySns, activityType = 0, route = '' } = query;
    return sqbBridge.navigateTo({
      path: getPageUrl(_path, sqbBridge.isJJZ),
      target: 'page',
      query
    })
  }

  // 兼容 packages/plugin 路径
  path = path.replace(/packages\/plugin\/view\/index/, 'pages/plugin/index')

  // 其余
  if (path[0] !== '/') path = `/${path}`
  return sqbBridge.navigateTo({ path, target: 'page' })
}

// check 接口和支付接口的错误合并到一起
// ckfCallback:check_fail_list弹窗
export function decodePayResult(result) {
  let modal_detail_value = null
  let alert_message_value = null
  let toast_message_value = null
  // check或者支付错误了
  if (
    result &&
    result.data &&
    result.data.cart_check_result &&
    !result.data.cart_check_result.success
  ) {
    let { data } = result
    let { cart_check_result } = data

    cart_check_result.error_msg = cart_check_result.error_msg || '支付出错'
    let { check_fail_list, error_code, error_msg, error_tip_way } = cart_check_result

    switch (error_tip_way) {
      // 弹窗提示
      case 'POPUP':
        // 商品库存不足弹窗
        if (check_fail_list && check_fail_list.length) {
          modal_detail_value = cart_check_result
        } else {
          alert_message_value = cart_check_result
        }
        break
      // toas提示
      default:
        toast_message_value = true
        wx.showToast({
          title: error_msg || '支付出错',
          icon: 'none',
          duration: 2000
        })

        break
    }
  }

  return [modal_detail_value, alert_message_value, toast_message_value]
}

/**
 * 请求协议弹窗授权
 * @param {*} needAuthPrivacyList 待签协议列表
 * @param {*} options 配置项
 * @returns
 */
export function requestPolicyWithBridge(needAuthPrivacyList, options = {}) {
  try {
    const privacyRequestNotify = _.get(this, 'observers.privacyRequest.notify')
    const privacyInit = this.getCurrentPageTask('PRIVACY_INIT')

    if (
      _.isEmpty(needAuthPrivacyList) ||
      !_.isFunction(privacyRequestNotify) ||
      !_.isFunction(privacyInit.task.then)
    ) {
      return Promise.resolve({ status: 'SUCCESS' })
    }

    return new Promise(resolve => {
      const config = _.pick(options, ['openType', 'once'])

      privacyInit.task.then(() => {
        privacyRequestNotify({
          needAuthPrivacyList, // 待签协议列表
          once: true, // 是否只弹一次
          openType: 'agreePrivacyAuthorization', // 场景类型：'getPhoneNumber'：获取手机号 ｜ 'getUserInfo'：获取用户信息 ｜ 'agreePrivacyAuthorization'：纯签署授权协议
          ...config,
          // 签署成功后的回调，如果回调中会改变ucUserId,返回一个promise
          callback: event => {
            resolve(event)
          }
        })
      })
    })
  } catch (error) {
    return Promise.resolve({ status: 'SUCCESS' })
  }
}
