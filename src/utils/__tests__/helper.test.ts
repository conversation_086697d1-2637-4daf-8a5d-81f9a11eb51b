import {
  transformGoods,
  calcRemainNumAndOutOfStock,
  canSubmitOrder,
  isRoundMeal,
  onAddToCart,
  onAddSpuToCart,
  getDataset,
  handleHashKey
} from '../helper'
import _ from '@wosai/emenu-mini-lodash'

// Mock lodash
jest.mock('@wosai/emenu-mini-lodash', () => ({
  get: jest.fn().mockImplementation((obj, path, defaultValue) => {
    if (path === 'mealType') return 'round_meal'
    if (path === 'serviceType') return 2
    return defaultValue
  }),
  find: jest.fn().mockImplementation((arr, predicate) => arr[0]),
  isFunction: jest.fn().mockReturnValue(true),
  cloneDeep: jest.fn().mockImplementation(obj => obj),
  noop: jest.fn()
}))

// Mock helper functions
jest.mock('../helper', () => {
  const originalModule = jest.requireActual('../helper')
  return {
    ...originalModule,
    transformGoods: jest.fn().mockImplementation(goods => ({
      ...goods,
      key: 'test-key'
    })),
    calcRemainNumAndOutOfStock: jest.fn().mockImplementation((goods, cart) => {
      goods.remain_num = goods.stock - 2
      goods.out_of_stock = goods.remain_num <= 0
    }),
    canSubmitOrder: jest.fn().mockImplementation(ctx => {
      return ctx.data.cart.records.length > 0
    }),
    isRoundMeal: jest.fn().mockImplementation(() => true)
  }
})

describe('helper utils', () => {
  describe('transformGoods', () => {
    it('should transform goods correctly', () => {
      const mockGoods = {
        id: '1',
        price: 1000,
        name: 'Test Item'
      }

      const result = transformGoods(mockGoods, 1)

      expect(result).toEqual(
        expect.objectContaining({
          id: '1',
          price: 1000,
          name: 'Test Item',
          key: 'test-key'
        })
      )
    })
  })

  describe('calcRemainNumAndOutOfStock', () => {
    it('should calculate remain number correctly', () => {
      const goods = {
        id: '1',
        stock: 10
      }

      const cart = {
        records: [
          {
            item_id: '1',
            quantity: 2
          }
        ]
      }

      calcRemainNumAndOutOfStock(goods, cart)
      expect(goods.remain_num).toBe(8)
    })

    it('should mark as out of stock when no stock left', () => {
      const goods = {
        id: '1',
        stock: 2
      }

      const cart = {
        records: [
          {
            item_id: '1',
            quantity: 2
          }
        ]
      }

      calcRemainNumAndOutOfStock(goods, cart)
      expect(goods.out_of_stock).toBe(true)
    })
  })

  describe('canSubmitOrder', () => {
    it('should return true when conditions are met', async () => {
      const context = {
        data: {
          cart: {
            records: [{ quantity: 1 }]
          },
          store: {
            business_status: 'OPEN'
          },
          sqbBridge: {
            getMiniProgramUser: jest.fn().mockResolvedValue({ phoneNumber: '***********' })
          }
        }
      }

      const result = await canSubmitOrder(context)
      expect(result).toBe(true)
    })

    it('should return false when cart is empty', async () => {
      const context = {
        data: {
          cart: {
            records: []
          },
          sqbBridge: {
            getMiniProgramUser: jest.fn().mockResolvedValue({ phoneNumber: '***********' })
          }
        }
      }

      const result = await canSubmitOrder(context)
      expect(result).toBe(false)
    })
  })

  describe('isRoundMeal', () => {
    it('should identify round meal correctly', () => {
      const context = {
        data: {
          sqbBridge: {
            getTerminal: jest.fn().mockReturnValue({
              mealType: 'round_meal',
              serviceType: 2
            })
          }
        },
        getTerminal: jest.fn().mockReturnValue({
          mealType: 'round_meal',
          serviceType: 2
        })
      }

      const result = isRoundMeal(context)
      expect(result).toBe(true)
    })
  })
})
