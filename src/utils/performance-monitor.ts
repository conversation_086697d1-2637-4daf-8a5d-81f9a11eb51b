/**
 * 商品列表渲染性能监控工具
 */

interface PerformanceMetric {
  name: string
  startTime: number
  endTime?: number
  duration?: number
  metadata?: any
}

class PerformanceMonitor {
  private metrics: Map<string, PerformanceMetric> = new Map()
  private enabled: boolean = false

  constructor(enabled: boolean = false) {
    this.enabled = enabled
  }

  /**
   * 开始性能监控
   */
  start(name: string, metadata?: any) {
    if (!this.enabled) return

    this.metrics.set(name, {
      name,
      startTime: Date.now(),
      metadata
    })
  }

  /**
   * 结束性能监控
   */
  end(name: string) {
    if (!this.enabled) return

    const metric = this.metrics.get(name)
    if (!metric) return

    const endTime = Date.now()
    const duration = endTime - metric.startTime

    metric.endTime = endTime
    metric.duration = duration

    // 输出性能日志
    console.log(`[Performance] ${name}: ${duration}ms`, metric.metadata)

    return duration
  }

  /**
   * 获取性能指标
   */
  getMetric(name: string): PerformanceMetric | undefined {
    return this.metrics.get(name)
  }

  /**
   * 获取所有性能指标
   */
  getAllMetrics(): PerformanceMetric[] {
    return Array.from(this.metrics.values())
  }

  /**
   * 清理性能指标
   */
  clear() {
    this.metrics.clear()
  }

  /**
   * 生成性能报告
   */
  generateReport(): string {
    const metrics = this.getAllMetrics()
    const report = metrics
      .filter(m => m.duration !== undefined)
      .sort((a, b) => (b.duration || 0) - (a.duration || 0))
      .map(m => `${m.name}: ${m.duration}ms`)
      .join('\n')

    return `Performance Report:\n${report}`
  }
}

// 创建全局实例
export const performanceMonitor = new PerformanceMonitor(
  // 可以通过环境变量或配置控制是否启用
  process.env.NODE_ENV === 'development'
)

/**
 * 性能监控装饰器
 */
export function measurePerformance(name?: string) {
  return function (target: any, propertyKey: string, descriptor: PropertyDescriptor) {
    const originalMethod = descriptor.value
    const metricName = name || `${target.constructor.name}.${propertyKey}`

    descriptor.value = function (...args: any[]) {
      performanceMonitor.start(metricName, { args })
      
      try {
        const result = originalMethod.apply(this, args)
        
        // 处理异步方法
        if (result && typeof result.then === 'function') {
          return result.finally(() => {
            performanceMonitor.end(metricName)
          })
        }
        
        performanceMonitor.end(metricName)
        return result
      } catch (error) {
        performanceMonitor.end(metricName)
        throw error
      }
    }

    return descriptor
  }
}

/**
 * 商品列表渲染性能分析器
 */
export class GoodsListPerformanceAnalyzer {
  private renderTimes: number[] = []
  private maxSamples = 50

  /**
   * 记录渲染时间
   */
  recordRenderTime(duration: number) {
    this.renderTimes.push(duration)
    
    // 保持样本数量在限制内
    if (this.renderTimes.length > this.maxSamples) {
      this.renderTimes.shift()
    }
  }

  /**
   * 获取平均渲染时间
   */
  getAverageRenderTime(): number {
    if (this.renderTimes.length === 0) return 0
    
    const sum = this.renderTimes.reduce((a, b) => a + b, 0)
    return sum / this.renderTimes.length
  }

  /**
   * 获取最大渲染时间
   */
  getMaxRenderTime(): number {
    return Math.max(...this.renderTimes, 0)
  }

  /**
   * 获取最小渲染时间
   */
  getMinRenderTime(): number {
    return Math.min(...this.renderTimes, Infinity)
  }

  /**
   * 检查是否存在性能问题
   */
  hasPerformanceIssue(): boolean {
    const avgTime = this.getAverageRenderTime()
    const maxTime = this.getMaxRenderTime()
    
    // 如果平均渲染时间超过100ms或最大时间超过500ms，认为有性能问题
    return avgTime > 100 || maxTime > 500
  }

  /**
   * 生成性能建议
   */
  getPerformanceSuggestions(): string[] {
    const suggestions: string[] = []
    const avgTime = this.getAverageRenderTime()
    const maxTime = this.getMaxRenderTime()

    if (avgTime > 100) {
      suggestions.push('平均渲染时间过长，建议启用虚拟滚动')
    }

    if (maxTime > 500) {
      suggestions.push('存在严重的渲染卡顿，建议检查数据处理逻辑')
    }

    if (this.renderTimes.length > 10) {
      const variance = this.calculateVariance()
      if (variance > 1000) {
        suggestions.push('渲染时间波动较大，建议优化数据缓存策略')
      }
    }

    return suggestions
  }

  /**
   * 计算渲染时间方差
   */
  private calculateVariance(): number {
    const avg = this.getAverageRenderTime()
    const squaredDiffs = this.renderTimes.map(time => Math.pow(time - avg, 2))
    return squaredDiffs.reduce((a, b) => a + b, 0) / this.renderTimes.length
  }

  /**
   * 重置统计数据
   */
  reset() {
    this.renderTimes = []
  }
}

// 创建全局商品列表性能分析器实例
export const goodsListAnalyzer = new GoodsListPerformanceAnalyzer()
